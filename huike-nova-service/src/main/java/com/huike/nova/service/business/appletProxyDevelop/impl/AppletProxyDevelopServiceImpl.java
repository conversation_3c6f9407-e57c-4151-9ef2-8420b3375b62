package com.huike.nova.service.business.appletProxyDevelop.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.OemRedisPrefixConstant;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.FsDateUtils;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.appletProxyDevelop.MsgDecrypt;
import com.huike.nova.common.util.appletProxyDevelop.ServerVerification;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.result.qyk.mina.CountMerchantCardNumberResultDTO;
import com.huike.nova.dao.entity.QykAppUserServiceCardDO;
import com.huike.nova.dao.repository.QykAppUserMerchantCardDAO;
import com.huike.nova.dao.repository.QykAppUserServiceCardDAO;
import com.huike.nova.service.business.appletProxyDevelop.AppletProxyDevelopPlatformService;
import com.huike.nova.service.business.appletProxyDevelop.AppletProxyDevelopService;
import com.huike.nova.service.domain.dto.appletProxyDevelop.AppletProxyDevelopCallbackBaseDTO;
import com.huike.nova.service.domain.dto.appletProxyDevelop.ComponentTicketCallbackDTO;
import com.huike.nova.service.domain.model.appletProxyDevelop.QueryOrderQykListModel;
import com.huike.nova.service.domain.model.appletProxyDevelop.platform.AuthTokenModel;
import com.huike.nova.service.domain.model.appletProxyDevelop.platform.GetComponentAccessTokenModel;
import com.huike.nova.service.domain.model.appletProxyDevelop.platform.RetrieveAuthorizationCodeModel;
import com.huike.nova.service.domain.param.appletProxyDevelop.QueryOrderQykListParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class AppletProxyDevelopServiceImpl implements AppletProxyDevelopService {

    private SysConfig sysConfig;
    private RedissonClient redissonClient;
    private QykAppUserServiceCardDAO qykAppUserServiceCardDAO;
    private QykAppUserMerchantCardDAO qykAppUserMerchantCardDAO;
    private AppletProxyDevelopPlatformService appletProxyDevelopPlatformService;


    /**
     * 授权事件回调
     *
     * @param request 回调信息
     * @return 回调结果
     */
    @Override
    public String authorizedCallback(HttpServletRequest request) {
        // 读取body中的内容
        String callbackContent = getIceCallbackContent(request);
        LogUtil.info(log, "AppletProxyDevelopServiceImpl.authorizedCallback >> 读取body中的内容 >> callbackContent = {}", callbackContent);
        if (StringUtils.isBlank(callbackContent)) {
            LogUtil.error(log, "AppletProxyDevelopServiceImpl.authorizedCallback >> 返回的数据为null");
            return CommonConstant.ERROR;
        }
        ComponentTicketCallbackDTO componentTicketCallbackDTO;
        try {
            AppletProxyDevelopCallbackBaseDTO callbackBaseDTO = JSON.parseObject(callbackContent, AppletProxyDevelopCallbackBaseDTO.class);
            String newMsgSignature = ServerVerification.getMsgSignature(sysConfig.getAppletProxyDevelopTripartiteAppletVerifyToken(), callbackBaseDTO.getTimeStamp(), callbackBaseDTO.getNonce(), callbackBaseDTO.getEncrypt());
            boolean verify = ServerVerification.verify(callbackBaseDTO.getMsgSignature(), newMsgSignature);
            if (!verify) {
                LogUtil.info(log, "AppletProxyDevelopServiceImpl.authorizedCallback >> 参数校验失败 >> MsgSignature = {},newMsgSignature = {},token = {}", callbackBaseDTO.getMsgSignature(), newMsgSignature, sysConfig.getAppletProxyDevelopTripartiteAppletVerifyToken());
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("参数校验失败");
            }
            MsgDecrypt msgDecrypt = new MsgDecrypt(sysConfig.getAppletProxyDevelopTripartiteAppletEncodingAesKey());
            componentTicketCallbackDTO = JSON.parseObject(msgDecrypt.decrypt(callbackBaseDTO.getEncrypt()), ComponentTicketCallbackDTO.class);
            if (Objects.isNull(componentTicketCallbackDTO)) {
                LogUtil.info(log, "AppletProxyDevelopServiceImpl.authorizedCallback >> 参数转换为空 >> callbackContent = {}", callbackContent);
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("参数转换为空");
            }
            LogUtil.info(log, "AppletProxyDevelopServiceImpl.authorizedCallback >> 参数转换完毕 >> componentTicketCallbackDTO = {}", JSON.toJSONString(componentTicketCallbackDTO));
            if (!CommonConstant.TICKET.equalsIgnoreCase(componentTicketCallbackDTO.getMsgType())) {
                LogUtil.info(log, "AppletProxyDevelopServiceImpl.authorizedCallback >> 不为ticket推送 >> componentTicketCallbackDTO = {}", JSON.toJSONString(componentTicketCallbackDTO));
                return CommonConstant.SUCCESS;
            }
        } catch (Exception e) {
            LogUtil.info(log, "AppletProxyDevelopServiceImpl.authorizedCallback >> 全局异常 >> callbackContent = {},token = {}", e, callbackContent, sysConfig.getAppletProxyDevelopTripartiteAppletVerifyToken());
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("全局异常");
        }
        final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.APPLET_PROXY_DEVELOP_COMPONENT_TICKET, componentTicketCallbackDTO.getAppId()));
        if (bucket.isExists()) {
            // 缓存已存在
            LogUtil.info(log, "AppletProxyDevelopServiceImpl.authorizedCallback >> 缓存已存在 >> componentTicketCallbackDTO = {}", JSON.toJSONString(componentTicketCallbackDTO));
            return CommonConstant.SUCCESS;
        }
        // 官方提示Ticket有效期3小时（180分钟） 缓存定为2小时（120分钟）
        bucket.set(componentTicketCallbackDTO.getTicket(), 120, TimeUnit.MINUTES);
        LogUtil.info(log, "AppletProxyDevelopServiceImpl.authorizedCallback >> 更新ticket完毕 >> componentTicketCallbackDTO = {}", JSON.toJSONString(componentTicketCallbackDTO));
        return CommonConstant.SUCCESS;
    }

    /**
     * 获取三方小程序访问令牌
     *
     * @param componentAppId 三方小程序appid
     * @return 三方小程序授权令牌
     */
    @Override
    public String getComponentAccessToken(String componentAppId) {
        LogUtil.info(log, "AppletProxyDevelopServiceImpl.getComponentAccessToken >> 接口开始 >> componentAppId = {}", componentAppId);
        if (StringUtils.isBlank(componentAppId)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("componentAppId不得为空");
        }
        RLock lock = redissonClient.getLock(StrUtil.format(OemRedisPrefixConstant.LOCK_GET_COMPONENT_ACCESS_TOKEN_KEY, componentAppId));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                throw new CommonException(ErrorCodeEnum.ICE_SCENE_MATERIAL_LESS_ERROR).detailMessage("获取第三方小程序接口调用凭据等待超时");
            }
            final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.COMPONENT_ACCESS_TOKEN, componentAppId));
            if (bucket.isExists()) {
                LogUtil.info(log, "AppletProxyDevelopServiceImpl.getComponentAccessToken >> 缓存中获取token >> token = {}", bucket.get());
                return bucket.get();
            }
            // 不存在则去获取token
            GetComponentAccessTokenModel componentAccessTokenModel = appletProxyDevelopPlatformService.getComponentAccessToken(componentAppId);
            // 有效期7200秒（2小时）  缓存有效期6000秒
            // if (componentAccessTokenModel.getExpiresIn() - 1200 < 0) {
            //     LogUtil.info(log, "AppletProxyDevelopServiceImpl.getComponentAccessToken >> 缓存有效期不足 >> componentAccessTokenModel = {}", JSON.toJSONString(componentAccessTokenModel));
            //     throw new CommonException(ErrorCodeEnum.ICE_SCENE_MATERIAL_LESS_ERROR).detailMessage("获取缓存有效期不足");
            // }
            String componentAccessToken = componentAccessTokenModel.getComponentAccessToken();
            bucket.set(componentAccessToken, componentAccessTokenModel.getExpiresIn() - 1200, TimeUnit.SECONDS);
            return componentAccessToken;
        } catch (Exception e) {
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.getComponentAccessToken >> 全局异常 >> componentAppId = {} ", e, componentAppId);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("全局异常");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.getComponentAccessToken >> 解锁异常 >> componentAppId = {} ", e, componentAppId);
            }
        }
    }

    /**
     * 获取授权小程序访问令牌
     *
     * @param authorizationAppid 授权小程序appId
     * @return 授权小程序访问令牌
     */
    @Override
    public String getAuthorizerAccessToken(String authorizationAppid) {
        LogUtil.info(log, "AppletProxyDevelopServiceImpl.getAuthorizerAccessToken >> 接口开始 >> authorizationAppid = {}", authorizationAppid);
        RLock lock = redissonClient.getLock(StrUtil.format(OemRedisPrefixConstant.LOCK_GET_AUTHORIZER_ACCESS_TOKEN_KEY, authorizationAppid));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                throw new CommonException(ErrorCodeEnum.ICE_SCENE_MATERIAL_LESS_ERROR).detailMessage("获取授权小程序接口调用凭据等待超时");
            }
            final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.AUTHORIZER_ACCESS_TOKEN, authorizationAppid));
            if (bucket.isExists()) {
                LogUtil.info(log, "AppletProxyDevelopServiceImpl.getAuthorizerAccessToken >> 缓存中获取token >> token = {}", bucket.get());
                return bucket.get();
            }
            // 不存在则去获取token
            final RBucket<String> refreshBucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.AUTHORIZER_REFRESH_TOKEN, authorizationAppid));
            if (refreshBucket.isExists()) {
                LogUtil.info(log, "AppletProxyDevelopServiceImpl.getAuthorizerAccessToken >> 缓存中获取refreshToken >> refreshToken = {}", refreshBucket.get());
                // 根据refreshToken获取token
                AuthTokenModel refreshGetAuthToken = appletProxyDevelopPlatformService.refreshAuthToken(authorizationAppid, refreshBucket.get());
                // 有效期为259200秒  缓存存储250000秒
                refreshBucket.set(refreshGetAuthToken.getAuthorizerRefreshToken(), refreshGetAuthToken.getRefreshExpiresIn() - 9200, TimeUnit.SECONDS);
                // // 有效期7200秒（2小时）  缓存有效期6000秒
                String authorizerAccessToken = refreshGetAuthToken.getAuthorizerAccessToken();
                bucket.set(authorizerAccessToken, refreshGetAuthToken.getExpiresIn() - 1200, TimeUnit.SECONDS);
                LogUtil.info(log, "AppletProxyDevelopServiceImpl.getAuthorizerAccessToken >> 根据refreshToken刷新后返回 >> token = {}", bucket.get());
                return authorizerAccessToken;
            }
            // 无refreshToken则找回授权码，根据对应授权码在获取token
            RetrieveAuthorizationCodeModel retrieveAuthorizationCodeModel = appletProxyDevelopPlatformService.retrieveAuthorizationCode(authorizationAppid);
            AuthTokenModel authTokenModel = appletProxyDevelopPlatformService.getAuthToken(retrieveAuthorizationCodeModel.getAuthorizationCode());
            String authorizerAccessToken = authTokenModel.getAuthorizerAccessToken();
            // 填充授权小程序的访问令牌
            bucket.set(authorizerAccessToken, authTokenModel.getExpiresIn() - 1200, TimeUnit.SECONDS);
            // 填充授权小程序的刷新令牌
            refreshBucket.set(authTokenModel.getAuthorizerRefreshToken(), authTokenModel.getRefreshExpiresIn() - 9200, TimeUnit.SECONDS);
            LogUtil.info(log, "AppletProxyDevelopServiceImpl.getAuthorizerAccessToken >> 获取new授权code获取token返回 >> token = {}", authorizerAccessToken);
            return authorizerAccessToken;
        } catch (Exception e) {
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.getAuthorizerAccessToken >> 全局异常 >> authorizationAppid = {} ", e, authorizationAppid);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("全局异常");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.getAuthorizerAccessToken >> 解锁异常 >> authorizationAppid = {} ", e, authorizationAppid);
            }
        }
    }

    /**
     * 查询订单权益卡列表
     *
     * @param param 查询参数
     * @return 订单列表
     */
    @Override
    public List<QueryOrderQykListModel> queryOrderQykList(QueryOrderQykListParam param) {
        LogUtil.info(log, "AppletProxyDevelopServiceImpl.queryOrderQykList >> 接口开始 >> param = {}", JSON.toJSONString(param));
        String orderSn = param.getOrderSn();
        List<QueryOrderQykListModel> modelList = new ArrayList<>();
        // 根据订单id查询母券
        List<QykAppUserServiceCardDO> serviceCardDOList = qykAppUserServiceCardDAO.findByOrderSn(orderSn);
        if (CollectionUtil.isNotEmpty(serviceCardDOList)) {
            List<String> serviceCardIdList = serviceCardDOList.stream().map(QykAppUserServiceCardDO::getServiceCardId).collect(Collectors.toList());
            List<CountMerchantCardNumberResultDTO> merchantCardNumberList = qykAppUserMerchantCardDAO.countMerchantCardNumberList(serviceCardIdList);
            Map<String, Integer> merchantCardNumberMap = merchantCardNumberList.stream().collect(Collectors.toMap(CountMerchantCardNumberResultDTO::getServiceCardId, CountMerchantCardNumberResultDTO::getMerchantCardNumber));
            for (QykAppUserServiceCardDO cardDO : serviceCardDOList) {
                QueryOrderQykListModel model = new QueryOrderQykListModel();
                model.setQykCardFace(cardDO.getQykCardFace());
                model.setProductName(cardDO.getProductName());
                model.setEndTime(FsDateUtils.timestampSecToDateTimeString(cardDO.getEndTime()));
                model.setServiceCardId(cardDO.getServiceCardId());
                model.setChildrenCouponCount(merchantCardNumberMap.getOrDefault(cardDO.getServiceCardId(), CommonConstant.ZERO));
                model.setActiveStatus(cardDO.getActiveStatus());
                if (null != cardDO.getActiveTime()) {
                    model.setActiveTime(FsDateUtils.formatSecond(cardDO.getActiveTime(), DatePattern.NORM_DATETIME_PATTERN));
                }
                model.setCreateTime(DateUtil.format(cardDO.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
                model.setExpiredTime(DateUtil.format(new DateTime(Long.valueOf(cardDO.getExpiredTime()) * 1000L), DatePattern.NORM_DATETIME_PATTERN));
                modelList.add(model);
            }
        }
        return modelList;
    }

    /**
     * 读取body中的内容
     *
     * @param request 请求
     * @return 回包
     */
    private String getIceCallbackContent(HttpServletRequest request) {
        BufferedReader br = null;
        StringBuilder sb = new StringBuilder();
        try {
            br = request.getReader();
            String str;
            while ((str = br.readLine()) != null) {
                sb.append(str);
            }
            br.close();
        } catch (IOException e) {
            LogUtil.info(log, "AppletProxyDevelopServiceImpl.getIceCallbackContent >> msg={}", e.getMessage());
        } finally {
            if (null != br) {
                try {
                    br.close();
                } catch (IOException e) {
                    LogUtil.error(log, "AppletProxyDevelopServiceImpl.getIceCallbackContent >> msg={}", e.getMessage());
                }
            }
        }
        return sb.toString();
    }
}
