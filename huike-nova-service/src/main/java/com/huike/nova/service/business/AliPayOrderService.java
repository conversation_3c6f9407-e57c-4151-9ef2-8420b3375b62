package com.huike.nova.service.business;

import com.alibaba.fastjson.JSONObject;
import com.huike.nova.service.domain.model.mina.orderCallback.CreateRefundModel;
import com.huike.nova.service.domain.model.order.ScanCodeModel;
import com.huike.nova.service.domain.model.order.VerifyCouponModel;
import com.huike.nova.service.domain.param.alipay.AliPayMsgParam;
import com.huike.nova.service.domain.param.alipay.AlipayMarketingCertificateCertificationSendParam;
import com.huike.nova.service.domain.param.mina.order.MinaOrderDeveloperOperateRefundParam;
import com.huike.nova.service.domain.param.mina.orderCallback.CreateRefundMsgParam;
import com.huike.nova.service.domain.param.order.InputCodeParam;
import com.huike.nova.service.domain.param.order.ScanCodeParam;
import com.huike.nova.service.domain.param.order.VerifyCouponParam;
import com.huike.nova.service.domain.result.channel.TiktokOpenConfig;

/**
 * <AUTHOR>
 * @date 2023年11月02日 17:23
 */
public interface AliPayOrderService {

    /**
     * 订单结果通知
     *
     * @param msgParam 订单结果
     */
    void orderResultNotification(AliPayMsgParam msgParam);

    /**
     * 三方凭证发放
     *
     * @param param 营销认证发放参数
     * @return 营销认证发放结果
     */
    String alipayMarketingCertificateCertificationSend(AlipayMarketingCertificateCertificationSendParam param);

    /**
     * 输码验券准备
     *
     * @param param code等信息
     * @return 券信息，商品信息
     */
    ScanCodeModel inputCode(InputCodeParam param);

    /**
     * 验券
     *
     * @param verifyCouponParam 验券参数
     * @return 验券结果
     */
    VerifyCouponModel verifyCoupon(VerifyCouponParam verifyCouponParam);

    /**
     * 扫码验券准备
     *
     * @param scanCodeParam 扫码参数
     * @return 扫码结果
     */
    ScanCodeModel scanCode(ScanCodeParam scanCodeParam);

    /**
     * 凭证消息通知
     *
     * @param msgParam 通知信息
     */
    void certificateMsgNotification(AliPayMsgParam msgParam);

    /**
     * 开发者退款，仅退款功能，不操作数据库
     *
     * @param param 参数
     * @param config
     * @return
     */
    JSONObject developerOperateRefund(CreateRefundMsgParam param, CreateRefundModel refund, TiktokOpenConfig config);
}
