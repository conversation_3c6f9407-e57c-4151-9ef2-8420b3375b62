/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version CustomerDataSaveJobHandler.java, v 0.1 2023-03-30 3:44 PM ruanzy
 */
@Component
@Slf4j
@JobHandler("customerDataSaveJobHandler")
@AllArgsConstructor
public class CustomerDataSaveJobHandler extends IJobHandler {

    private TaskService taskService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("CustomerDataSaveJobHandler.execute >> 客资新增脚本执行开始：time = {}", DateUtil.now());

        //taskService.saveCustomerData();
        taskService.customerProductDataSync(s);

        XxlJobLogger.log("CustomerDataSaveJobHandler.execute >> 客资新增脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}