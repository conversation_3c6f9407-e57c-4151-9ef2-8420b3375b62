package com.huike.nova.service.exception;

import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 核券异常
 *
 * <AUTHOR> (<EMAIL>)
 * @version VerifyCouponErrorException.java, v1.0 07/10/2024 22:26 John Exp$
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VerifyCouponErrorException extends CommonException {
    public VerifyCouponErrorException() {
        super(ErrorCodeEnum.COUPON_VERIFY_ERROR);
    }

    public VerifyCouponErrorException(String couponCode) {
        super(ErrorCodeEnum.COUPON_VERIFY_ERROR);
        this.couponCode = couponCode;
    }

    private String couponCode;
}
