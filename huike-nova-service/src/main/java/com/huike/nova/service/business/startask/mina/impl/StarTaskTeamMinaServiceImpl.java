/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.mina.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.ExcelConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.enums.AliOssEnum;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogRemarkTypeEnum;
import com.huike.nova.common.enums.startask.web.DistributionTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.common.util.PhoneNumberUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.param.startask.PageRewardDetailParamDTO;
import com.huike.nova.dao.domain.param.startask.TeamRankParamDTO;
import com.huike.nova.dao.domain.result.startask.PageRewardDetailResultDTO;
import com.huike.nova.dao.domain.result.startask.TeamRankResultDTO;
import com.huike.nova.dao.entity.StarTaskAgentDO;
import com.huike.nova.dao.entity.StarTaskBalanceLogDO;
import com.huike.nova.dao.entity.StarTaskIdentityDO;
import com.huike.nova.dao.entity.StarTaskIdentityRelationDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.StarTaskAgentDAO;
import com.huike.nova.dao.repository.StarTaskBalanceLogDAO;
import com.huike.nova.dao.repository.StarTaskIdentityDAO;
import com.huike.nova.dao.repository.StarTaskIdentityRelationDAO;
import com.huike.nova.service.business.AilikeGaodeCodeService;
import com.huike.nova.service.business.CommonService;
import com.huike.nova.service.business.OssManager;
import com.huike.nova.service.business.startask.mina.StarTaskTeamMinaService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.mina.StarTaskTeamMinaServiceObjMapper;
import com.huike.nova.service.domain.model.startask.mina.login.StarTaskMinaLoginModel;
import com.huike.nova.service.domain.model.startask.mina.team.ExportCommonModel;
import com.huike.nova.service.domain.model.startask.mina.team.GetInvitationInfoModel;
import com.huike.nova.service.domain.model.startask.mina.team.PageRewardDetailModel;
import com.huike.nova.service.domain.model.startask.mina.team.StatisticsTeamInfoModel;
import com.huike.nova.service.domain.model.startask.mina.team.TeamRankModel;
import com.huike.nova.service.domain.param.startask.mina.team.GetInvitationInfoParam;
import com.huike.nova.service.domain.param.startask.mina.team.PageRewardDetailParam;
import com.huike.nova.service.domain.param.startask.mina.team.StatisticsTeamInfoParam;
import com.huike.nova.service.domain.param.startask.mina.team.TeamRankParam;
import com.huike.nova.service.domain.result.excel.RewardDetailExportResult;
import com.huike.nova.service.domain.result.excel.TeamRankExportResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version StarTaskTeamMinaServiceImpl.java, v 0.1 2024-01-22 2:36 PM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class StarTaskTeamMinaServiceImpl implements StarTaskTeamMinaService {

    private StarTaskIdentityRelationDAO starTaskIdentityRelationDAO;

    private StarTaskBalanceLogDAO starTaskBalanceLogDAO;

    private StarTaskIdentityDAO starTaskIdentityDAO;

    private StarTaskAgentDAO starTaskAgentDAO;

    private StarTaskTeamMinaServiceObjMapper starTaskTeamMinaServiceObjMapper;

    private AilikeGaodeCodeService ailikeGaodeCodeService;

    private OssManager ossManager;

    private CommonService commonService;

    private SysConfig sysConfig;

    /**
     * 团队信息总览
     *
     * @param param
     * @return
     */
    @Override
    public StatisticsTeamInfoModel statisticsTeamInfo(StatisticsTeamInfoParam param) {
        // 登录态中获取身份信息
        StarTaskMinaLoginModel basicInfo = LoginUtil.getStarTaskLoginBasicInfo(param.getIdentityType());
        // 身份id
        String identityId = basicInfo.getIdentityId();
        // 获取邀请总人数和有效邀请人数
        List<StarTaskIdentityRelationDO> list = starTaskIdentityRelationDAO.findListByParentId(identityId);
        StatisticsTeamInfoModel model = new StatisticsTeamInfoModel();
        model.setTotalNumber(list.size());
        model.setEffectiveNumber((int) list.stream().filter(item -> CommonConstant.INTEGER_ONE.equals(item.getIsEffective())).count());
        // 统计任务和奖励金
        List<StarTaskBalanceLogDO> logList = starTaskBalanceLogDAO.findListByIdentityIdAndRemarkType(identityId, BalanceLogRemarkTypeEnum.RETURN_REWARD.getValue());
        model.setTaskNumber(logList.size());
        model.setTotalIncome(logList.stream().map(item -> item.getChangeAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
        model.setTotalTaskAmount(logList.stream().map(item -> item.getTaskMoney()).reduce(BigDecimal.ZERO, BigDecimal::add));
        return model;
    }

    /**
     * 成员列表
     *
     * @param param
     * @return
     */
    @Override
    public PageResult<TeamRankModel> teamRank(PageParam<TeamRankParam> param) {
        // 成员列表
        Page<TeamRankResultDTO> resultDTOPage = starTaskIdentityRelationDAO.teamRank(this.getTeamRankParamDTOPageParam(param));
        List<TeamRankResultDTO> records = resultDTOPage.getRecords();
        List<TeamRankModel> list = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(records)) {
            for (TeamRankResultDTO record : records) {
                TeamRankModel model = starTaskTeamMinaServiceObjMapper.toTeamRankModel(record);
                // 商家名称/昵称/手机号转换
                String merchantName = record.getMerchantName();
                model.setName(merchantName);
                if (StringUtils.isBlank(merchantName)) {
                    model.setName(StringUtils.isBlank(record.getNickname()) ? PhoneNumberUtil.mask(FieldEncryptUtil.decode(record.getPhoneNumber())) : record.getNickname());
                }
                // 地区名称
                List<String> arrayList = Lists.newArrayList();
                arrayList.add(record.getProvince());
                arrayList.add(record.getCity());
                String areaName = ailikeGaodeCodeService.getSplicingAreaName(arrayList, StringPool.EMPTY);
                model.setAreaName(areaName);
                list.add(model);
            }
        }
        PageResult<TeamRankModel> pageResult = new PageResult<>();
        pageResult.setSize(resultDTOPage.getSize());
        pageResult.setCurrent(resultDTOPage.getCurrent());
        pageResult.setTotal(resultDTOPage.getTotal());
        pageResult.setRecords(list);
        return pageResult;
    }

    /**
     * 奖励明细
     *
     * @param param
     * @return
     */
    @Override
    public PageResult<PageRewardDetailModel> pageRewardDetail(PageParam<PageRewardDetailParam> param) {
        // 奖励明细
        Page<PageRewardDetailResultDTO> resultDTOPage = starTaskBalanceLogDAO.pageRewardDetail(this.getPageRewardDetailParamDTOPageParam(param));
        List<PageRewardDetailResultDTO> records = resultDTOPage.getRecords();
        List<PageRewardDetailModel> list = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(records)) {
            for (PageRewardDetailResultDTO record : records) {
                PageRewardDetailModel model = starTaskTeamMinaServiceObjMapper.toPageRewardDetailModel(record);
                // 商家名称/昵称/手机号转换
                String merchantName = record.getMerchantName();
                model.setName(merchantName);
                if (StringUtils.isBlank(merchantName)) {
                    model.setName(StringUtils.isBlank(record.getNickname()) ? PhoneNumberUtil.mask(FieldEncryptUtil.decode(record.getPhoneNumber())) : record.getNickname());
                }
                model.setTaskAmount(record.getTaskMoney());
                model.setIncome(record.getChangeAmount());
                list.add(model);
            }
        }
        PageResult<PageRewardDetailModel> pageResult = new PageResult<>();
        pageResult.setSize(resultDTOPage.getSize());
        pageResult.setCurrent(resultDTOPage.getCurrent());
        pageResult.setTotal(resultDTOPage.getTotal());
        pageResult.setRecords(list);
        return pageResult;
    }

    /**
     * 导出成员列表
     *
     * @param param
     * @return
     */
    @Override
    public ExportCommonModel exportTeamRank(TeamRankParam param) {
        // 登录态中获取身份信息
        StarTaskMinaLoginModel loginBasicInfo = LoginUtil.getStarTaskLoginBasicInfo(param.getIdentityType());
        // 构建入参
        PageParam<TeamRankParamDTO> pageParam = new PageParam<>();
        pageParam.setPage(1);
        pageParam.setPageSize(CommonConstant.EXPORT_MAX_COUNT);
        TeamRankParamDTO dto = starTaskTeamMinaServiceObjMapper.toTeamRankParamDTO(param);
        dto.setIdentityId(loginBasicInfo.getIdentityId());
        // 时间转换
        String startTime = param.getStartTime();
        String endTime = param.getEndTime();
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            dto.setStartTime(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(startTime, DatePattern.NORM_DATE_PATTERN))));
            dto.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(endTime, DatePattern.NORM_DATE_PATTERN))));
        }
        dto.setPhoneEncrypt(FieldEncryptUtil.encode(param.getKeyword()));
        pageParam.setQuery(dto);
        Page<TeamRankResultDTO> resultDTOPage = starTaskIdentityRelationDAO.teamRank(pageParam);
        List<TeamRankResultDTO> records = resultDTOPage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("导出数据为空");
        }
        List<TeamRankExportResult> list = Lists.newArrayList();
        boolean flag = StarTaskIdentityTypeEnum.MERCHANT.getValue().equals(param.getIdentityType());
        String templateName = flag ? ExcelConstant.TEMPLATE_MERCHANT_TEAM_FILE_NAME : ExcelConstant.TEMPLATE_STAR_TEAM_FILE_NAME;
        String downloadFile = ossManager.downloadFile(templateName, AliOssEnum.EXCEL_EXPORT_TEMPLATE);
        String fileName = (flag ? "商家战队成员" : "达人团队成员") + StringPool.DASH + System.currentTimeMillis() + ".xlsx";
        // 数据转换
        for (TeamRankResultDTO record : records) {
            TeamRankExportResult result = starTaskTeamMinaServiceObjMapper.toTeamRankExportResult(record);
            // 手机号解密
            result.setPhoneNumber(FieldEncryptUtil.decode(result.getPhoneNumber()));
            // 地区名称
            if (flag) {
                List<String> arrayList = Lists.newArrayList();
                arrayList.add(record.getProvince());
                arrayList.add(record.getCity());
                String areaName = ailikeGaodeCodeService.getSplicingAreaName(arrayList, StringPool.SLASH);
                result.setAreaName(areaName);
            }
            list.add(result);
        }
        try (ExcelWriter excelWriter = EasyExcel.write(fileName).withTemplate(downloadFile).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
            excelWriter.fill(list, fillConfig, writeSheet);
        }
        File excelFile = new File(downloadFile);
        if (!excelFile.delete()) {
            log.error("delete" + fileName + "error");
        }
        String fileUrl = commonService.uploadExcelFile(fileName);
        ExportCommonModel model = new ExportCommonModel();
        model.setUrl(commonService.httpToHttps(fileUrl));
        return model;
    }

    /**
     * 导出奖励明细
     *
     * @param param
     * @return
     */
    @Override
    public ExportCommonModel exportRewardDetail(PageRewardDetailParam param) {
        String keyword = param.getKeyword();
        // 登录态中获取身份信息
        StarTaskMinaLoginModel loginBasicInfo = LoginUtil.getStarTaskLoginBasicInfo(param.getIdentityType());
        // 奖励明细
        PageParam<PageRewardDetailParamDTO> pageParam = new PageParam<>();
        pageParam.setPage(CommonConstant.INTEGER_ONE);
        pageParam.setPageSize(CommonConstant.EXPORT_MAX_COUNT);
        PageRewardDetailParamDTO dto = new PageRewardDetailParamDTO();
        // 时间转换
        String startTime = param.getStartTime();
        String endTime = param.getEndTime();
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            dto.setStartTime(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(startTime, DatePattern.NORM_DATE_PATTERN))));
            dto.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(endTime, DatePattern.NORM_DATE_PATTERN))));
        }
        dto.setIdentityId(loginBasicInfo.getIdentityId());
        dto.setKeyword(keyword);
        dto.setPhoneEncrypt(FieldEncryptUtil.encode(keyword));
        pageParam.setQuery(dto);
        Page<PageRewardDetailResultDTO> resultDTOPage = starTaskBalanceLogDAO.pageRewardDetail(pageParam);
        List<PageRewardDetailResultDTO> records = resultDTOPage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("导出数据为空");
        }
        boolean flag = StarTaskIdentityTypeEnum.MERCHANT.getValue().equals(param.getIdentityType());
        String templateName = flag ? ExcelConstant.TEMPLATE_MERCHANT_REWARD_DETAIL_FILE_NAME : ExcelConstant.TEMPLATE_STAR_REWARD_DETAIL_FILE_NAME;
        String downloadFile = ossManager.downloadFile(templateName, AliOssEnum.EXCEL_EXPORT_TEMPLATE);
        String fileName = (flag ? "商家战队-返还明细" : "达人团队-返还明细") + StringPool.DASH + System.currentTimeMillis() + ".xlsx";
        List<RewardDetailExportResult> list = Lists.newArrayList();
        // 导出数据转化
        for (PageRewardDetailResultDTO record : records) {
            RewardDetailExportResult result = starTaskTeamMinaServiceObjMapper.toRewardDetailExportResult(record);
            // 手机号解密
            result.setPhoneNumber(FieldEncryptUtil.decode(result.getPhoneNumber()));
            list.add(result);
        }
        try (ExcelWriter excelWriter = EasyExcel.write(fileName).withTemplate(downloadFile).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
            excelWriter.fill(list, fillConfig, writeSheet);
        }
        File excelFile = new File(downloadFile);
        if (!excelFile.delete()) {
            log.error("delete" + fileName + "error");
        }
        String fileUrl = commonService.uploadExcelFile(fileName);
        ExportCommonModel model = new ExportCommonModel();
        model.setUrl(commonService.httpToHttps(fileUrl));
        return model;
    }

    /**
     * 邀请信息查询
     *
     * @param param
     * @return
     */
    @Override
    public GetInvitationInfoModel getInvitationInfo(GetInvitationInfoParam param) {
        Integer identityType = param.getIdentityType();
        // 登录态获取登录信息
        StarTaskMinaLoginModel basicInfo = LoginUtil.getStarTaskLoginBasicInfo(identityType);
        String identityId = basicInfo.getIdentityId();
        // 查询身份信息
        StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(identityId);
        if (Objects.isNull(identityDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息不存在");
        }
        // 构建返回对象
        GetInvitationInfoModel model = new GetInvitationInfoModel();
        model.setAvatar(identityDO.getAvatar());
        // 商家名称/昵称/手机号转换
        String merchantName = identityDO.getMerchantName();
        model.setName(merchantName);
        if (StringUtils.isBlank(merchantName)) {
            model.setName(StringUtils.isBlank(identityDO.getNickname()) ? PhoneNumberUtil.mask(FieldEncryptUtil.decode(identityDO.getPhoneNumber())) : identityDO.getNickname());
        }
        boolean flag = StarTaskIdentityTypeEnum.MERCHANT.getValue().equals(identityType);
        // 二维码
        String url = flag ? sysConfig.getStarTaskMinaMerchantTeamUrl() : sysConfig.getStarTaskMinaStarTeamUrl();
        model.setQrCode(StrUtil.format(url, identityId, identityType));
        // 奖励率（商家和达人逻辑不一致）
        StarTaskIdentityRelationDO relationDO = starTaskIdentityRelationDAO.findListByIdentityId(identityDO.getIdentityId());
        BigDecimal rewardRate;
        BigDecimal totalRate = new BigDecimal(sysConfig.getStarTaskMinaPlatformRate());
        // 判断是否是区代
        StarTaskAgentDO agentDO = starTaskAgentDAO.checkAreaAgentByCity(identityDO.getCity());
        if (Objects.nonNull(agentDO)) {
            totalRate = agentDO.getAgentRate();
            rewardRate = flag ? agentDO.getMerchantRate() : agentDO.getStarRate();
        } else {
            rewardRate = flag ? sysConfig.getStarTaskMinaMerchantDistributionRate() : sysConfig.getStarTaskMinaStarDistributionRate();
        }
        // 最后判断自定义
        if (Objects.nonNull(relationDO) && DistributionTypeEnum.CUSTOM.getValue().equals(relationDO.getDistributionType())) {
            rewardRate = relationDO.getDistributionRate();
        }
        // 达人团长计算公式：分销比例 / (1-总分成比例) （可能会产生小数，四舍五入保留一位）
        BigDecimal subtract = new BigDecimal("1").subtract(totalRate.divide(new BigDecimal("100")));
        model.setRewardRate(flag ? rewardRate : rewardRate.divide(subtract, 1, BigDecimal.ROUND_HALF_UP));
        return model;
    }

    @NotNull
    private PageParam<PageRewardDetailParamDTO> getPageRewardDetailParamDTOPageParam(PageParam<PageRewardDetailParam> param) {
        PageRewardDetailParam query = param.getQuery();
        // 登录态获取身份信息
        StarTaskMinaLoginModel loginBasicInfo = LoginUtil.getStarTaskLoginBasicInfo(query.getIdentityType());
        // 构建入参
        PageParam<PageRewardDetailParamDTO> pageParam = new PageParam<>();
        pageParam.setPage(param.getPage());
        pageParam.setPageSize(param.getPageSize());
        PageRewardDetailParamDTO dto = new PageRewardDetailParamDTO();
        dto.setIdentityId(loginBasicInfo.getIdentityId());
        dto.setKeyword(query.getKeyword());
        // 时间转换
        String startTime = query.getStartTime();
        String endTime = query.getEndTime();
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            dto.setStartTime(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(startTime, DatePattern.NORM_DATE_PATTERN))));
            dto.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(endTime, DatePattern.NORM_DATE_PATTERN))));
        }
        dto.setPhoneEncrypt(FieldEncryptUtil.encode(query.getKeyword()));
        pageParam.setQuery(dto);
        return pageParam;
    }

    @NotNull
    private PageParam<TeamRankParamDTO> getTeamRankParamDTOPageParam(PageParam<TeamRankParam> param) {
        TeamRankParam query = param.getQuery();
        // 登录态中获取身份信息
        StarTaskMinaLoginModel loginBasicInfo = LoginUtil.getStarTaskLoginBasicInfo(query.getIdentityType());
        // 构建入参
        PageParam<TeamRankParamDTO> pageParam = new PageParam<>();
        pageParam.setPage(param.getPage());
        pageParam.setPageSize(param.getPageSize());
        TeamRankParamDTO dto = starTaskTeamMinaServiceObjMapper.toTeamRankParamDTO(query);
        dto.setIdentityId(loginBasicInfo.getIdentityId());
        // 时间转换
        String startTime = query.getStartTime();
        String endTime = query.getEndTime();
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            dto.setStartTime(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(startTime, DatePattern.NORM_DATE_PATTERN))));
            dto.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(endTime, DatePattern.NORM_DATE_PATTERN))));
        }
        dto.setPhoneEncrypt(FieldEncryptUtil.encode(query.getKeyword()));
        pageParam.setQuery(dto);
        return pageParam;
    }
}