/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler.startask;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.startask.common.StarTaskJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version AgentDayStatisticsJobHandler.java, v 0.1 2024-05-24 3:45 PM ruanzy
 */
@Component
@Slf4j
@JobHandler("agentDayStatisticsJobHandler")
@AllArgsConstructor
public class AgentDayStatisticsJobHandler extends IJobHandler {

    private StarTaskJobService starTaskJobService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("AgentDayStatisticsJobHandler.execute >> 区代日统计脚本执行开始：time = {}", DateUtil.now());
        starTaskJobService.agentDayStatistics(param);
        XxlJobLogger.log("AgentDayStatisticsJobHandler.execute >> 区代日统计脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}