/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.mina;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.login.FindProtocolListModel;
import com.huike.nova.service.domain.model.startask.mina.common.CheckAuthModel;
import com.huike.nova.service.domain.model.startask.mina.common.FindTransactionDetailModel;
import com.huike.nova.service.domain.model.startask.mina.common.GetAccountBalanceModel;
import com.huike.nova.service.domain.model.startask.mina.common.GetAuthConfigModel;
import com.huike.nova.service.domain.model.startask.mina.common.GetNoticeContentModel;
import com.huike.nova.service.domain.model.startask.mina.common.GetUserShowAttrModel;
import com.huike.nova.service.domain.model.startask.mina.common.GetWithdrawPermissionModel;
import com.huike.nova.service.domain.model.startask.mina.common.GetWithdrawalInfoModel;
import com.huike.nova.service.domain.param.startask.mina.common.ApplyWithdrawalParam;
import com.huike.nova.service.domain.param.startask.mina.common.AuthenticateParam;
import com.huike.nova.service.domain.param.startask.mina.common.CheckAuthParam;
import com.huike.nova.service.domain.param.startask.mina.common.FindTransactionDetailParam;
import com.huike.nova.service.domain.param.startask.mina.common.GetAccountBalanceParam;
import com.huike.nova.service.domain.param.startask.mina.common.GetNoticeContentParam;
import com.huike.nova.service.domain.param.startask.mina.common.GetWithdrawalInfoParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version StarTaskCommonMinaService.java, v 0.1 2023-12-04 4:08 PM ruanzy
 */
public interface StarTaskCommonMinaService {

    /**
     * 余额
     *
     * @param param
     * @return
     */
    GetAccountBalanceModel getAccountBalance(GetAccountBalanceParam param);

    /**
     * 交易明细
     *
     * @param param
     * @return
     */
    PageResult<FindTransactionDetailModel> findTransactionDetail(PageParam<FindTransactionDetailParam> param);

    /**
     * 申请提现
     *
     * @param param
     */
    void applyWithdrawal(ApplyWithdrawalParam param);

    /**
     * 获取提现信息
     *
     * @param param
     * @return
     */
    GetWithdrawalInfoModel getWithdrawalInfo(GetWithdrawalInfoParam param);

    /**
     * 根据任务id获取达人区域限制名称列表
     *
     * @param taskId 任务id
     * @return 名称列表
     */
    List<String> findStarAreaNameList(String taskId);

    /**
     * 获取提现权限
     *
     * @return
     */
    GetWithdrawPermissionModel getWithdrawPermission();

    /**
     * 获取通知内容
     *
     * @param param 入参
     * @return 通知内容
     */
    GetNoticeContentModel getNoticeContent(GetNoticeContentParam param);

    /**
     * 判断是否身份认证
     *
     * @param param
     * @return
     */
    CheckAuthModel checkAuth(CheckAuthParam param);

    /**
     * 身份认证
     *
     * @param param
     */
    void authenticate(AuthenticateParam param);

    /**
     * 获取认证协议
     *
     * @return
     */
    FindProtocolListModel getAuthenticationAgreement();

    /**
     * 获取用户展示信息
     *
     * @return
     */
    GetUserShowAttrModel getUserShowAttr(Integer identityType);

    /**
     * 更新用户展示信息
     */
    void updateUserShowAttr(Integer identityType);

    /**
     * 获取身份认证配置
     *
     * @return
     */
    GetAuthConfigModel getAuthConfig();
}