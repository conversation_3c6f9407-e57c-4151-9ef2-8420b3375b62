/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.mina.impl;

import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.constant.SystemConstants;
import com.huike.nova.common.constant.startask.StarTaskConstant;
import com.huike.nova.common.constant.startask.mina.StarTaskMinaRedisPrefixConstant;
import com.huike.nova.common.enums.BooleanValueEnum;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.SmsTypeEnum;
import com.huike.nova.common.enums.oem.BooleanEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskMinaLoginTypeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskSendCaptchaTypeEnum;
import com.huike.nova.common.enums.startask.web.DistributionTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.DingTalkUtil;
import com.huike.nova.common.util.FsDateUtils;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.PhoneNumberUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.result.startask.FindIdentityByInviterPhoneListResultDTO;
import com.huike.nova.dao.entity.AilikeGaodeCodeDO;
import com.huike.nova.dao.entity.StarTaskBalanceAccountDO;
import com.huike.nova.dao.entity.StarTaskIdentityDO;
import com.huike.nova.dao.entity.StarTaskIdentityRelationDO;
import com.huike.nova.dao.entity.StarTaskPartnerCodeConfigDO;
import com.huike.nova.dao.entity.StarTaskUserDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.AilikeGaodeCodeDAO;
import com.huike.nova.dao.repository.StarTaskBalanceAccountDAO;
import com.huike.nova.dao.repository.StarTaskIdentityDAO;
import com.huike.nova.dao.repository.StarTaskIdentityRelationDAO;
import com.huike.nova.dao.repository.StarTaskPartnerCodeConfigDAO;
import com.huike.nova.dao.repository.StarTaskUserDAO;
import com.huike.nova.service.business.CommonService;
import com.huike.nova.service.business.SmsService;
import com.huike.nova.service.business.startask.mina.StarTaskMinaLoginService;
import com.huike.nova.service.business.startask.mina.StarTaskWechatMinaService;
import com.huike.nova.service.domain.model.login.FindProtocolDetailModel;
import com.huike.nova.service.domain.model.login.FindProtocolListModel;
import com.huike.nova.service.domain.model.mina.wechat.WechatMinaGetUserPhoneNumberModel;
import com.huike.nova.service.domain.model.startask.mina.login.GetLoginInfoModel;
import com.huike.nova.service.domain.model.startask.mina.login.InviterInfoModel;
import com.huike.nova.service.domain.model.startask.mina.login.StarTaskIdentityModel;
import com.huike.nova.service.domain.model.startask.mina.login.StarTaskMinaLoginModel;
import com.huike.nova.service.domain.param.sms.SmsMessageCodeParam;
import com.huike.nova.service.domain.param.sms.SmsMessageParam;
import com.huike.nova.service.domain.param.startask.mina.login.ConfirmIdentityParam;
import com.huike.nova.service.domain.param.startask.mina.login.GetLoginInfoParam;
import com.huike.nova.service.domain.param.startask.mina.login.RegisterInvitedUserParam;
import com.huike.nova.service.domain.param.startask.mina.login.StarTaskMinaLoginParam;
import com.huike.nova.service.domain.param.startask.mina.login.StarTaskSendCaptchaParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version StarTaskMinaLoginServiceImpl.java, v 0.1 2023-11-24 1:46 PM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class StarTaskMinaLoginServiceImpl implements StarTaskMinaLoginService {

    private StarTaskUserDAO starTaskUserDAO;

    private StarTaskIdentityDAO starTaskIdentityDAO;

    private StarTaskIdentityRelationDAO starTaskIdentityRelationDAO;

    private RedissonClient redissonClient;

    private TransactionTemplate transactionTemplate;

    private StarTaskBalanceAccountDAO starTaskBalanceAccountDAO;

    private StarTaskPartnerCodeConfigDAO starTaskPartnerCodeConfigDAO;

    private CommonService commonService;

    private SmsService smsService;

    private StarTaskWechatMinaService starTaskWechatMinaService;

    private AilikeGaodeCodeDAO ailikeGaodeCodeDAO;

    private SysConfig sysConfig;

    /**
     * 钉钉发送短信通知
     */
    private SysConfig.DingDingStarTaskSmsNotifyConfig dingDingStarTaskSmsNotifyConfig;

    /**
     * 登录-验证码登录/快捷登录
     *
     * @param param
     * @return
     */
    @Override
    public StarTaskMinaLoginModel login(StarTaskMinaLoginParam param) {
        LogUtil.info(log, "StarTaskMinaLoginServiceImpl.login >> 来探呗小程序登录 >> 登录参数, {}", JSONObject.toJSONString(param));
        StarTaskMinaLoginTypeEnum loginTypeEnum = StarTaskMinaLoginTypeEnum.getByValue(param.getLoginType());
        if (Objects.isNull(loginTypeEnum)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("登录类型错误");
        }
        boolean hasRegister = Boolean.FALSE;
        String phone;
        String appletId = param.getAppletId();
        switch (loginTypeEnum) {
            case QUICK_LOGIN:
                // 需要解析小程序手机号
                WechatMinaGetUserPhoneNumberModel userPhoneNumberModel = starTaskWechatMinaService.getUserPhoneNumber(StarTaskConstant.DEFAULT_MINA_CHANNEL, param.getAuthCode());
                phone = userPhoneNumberModel.getPhoneNumber();
                break;
            case CODE_LOGIN:
                phone = param.getAccount();
                String code = param.getCode();
                if (sysConfig.isTest() || sysConfig.isDev()) {
                    break;
                }
                // 校验验证码是否正确
                final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_SMS_VERIFY
                        , appletId, SmsTypeEnum.STAR_TASK_MINA_LOGIN.getValue(), phone), StringCodec.INSTANCE);
                String smdCode = bucket.get();
                if (StringUtils.isBlank(smdCode)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("验证码已失效，请重新发送");
                }
                if (Objects.isNull(code) || !smdCode.equals(code)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("验证码错误，请重新输入");
                }
                break;
            default:
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("类型错误");
        }
        StarTaskUserDO userInfo = starTaskUserDAO.getUserInfo(phone, appletId);
        StarTaskMinaLoginModel model = new StarTaskMinaLoginModel();
        // 已注册的用户
        if (Objects.nonNull(userInfo)) {
            hasRegister = Boolean.TRUE;
            String userId = userInfo.getUserId();
            model.setOpenId(param.getOpenId());
            this.buildLoginInfo(phone, appletId, userId, model);

            // 未注册的用户
        } else {
            model.setPhoneNumber(phone);
            model.setAppletId(appletId);
            model.setPartnerCode(param.getPartnerCode());
            // 默认为0
            model.setLoginIdentityType(CommonConstant.ZERO);
            // 根据合作伙伴Code
            val partnerConfigList = starTaskPartnerCodeConfigDAO.queryPartnerCodeConfigList(param.getPartnerCode());
            if (CollectionUtil.isNotEmpty(partnerConfigList)) {
                model.setInviterInfo(partnerConfigList.stream().map(p -> {
                    val phoneNo = FieldEncryptUtil.decode(p.getPhoneNumber());
                    return new InviterInfoModel()
                            .setInviterName(p.getPartnerName())
                            .setInviterPhoneNumber(PhoneNumberUtil.mask(phoneNo))
                            .setIdentityType(p.getIdentityType());
                }).collect(Collectors.toList()));
                // 只有一条记录时，获取登录类型
                if (partnerConfigList.size() == CommonConstant.ONE) {
                    model.setLoginIdentityType(CollectionUtil.getFirst(partnerConfigList).getIdentityType());
                }

            } else {
                model.setInviterInfo(Lists.newArrayList());
            }
        }
        model.setHasRegister(hasRegister);
        LogUtil.info(log, "StarTaskMinaLoginServiceImpl.login >> 来探呗小程序登录 >> 登录返参, {}", JSONObject.toJSONString(model));
        return model;
    }

    /**
     * 发送验证码
     *
     * @param param
     */
    @Override
    public void sendCaptcha(StarTaskSendCaptchaParam param) {
        String phoneNumber = param.getPhoneNumber();
        String appletId = param.getAppletId();
        // 获取6位随机数
        final String smdCode = RandomUtil.randomNumbers(6);
        StarTaskSendCaptchaTypeEnum typeEnum = StarTaskSendCaptchaTypeEnum.getByValue(param.getType());
        if (Objects.isNull(typeEnum)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("验证码类型错误");
        }
        SmsTypeEnum smsTypeEnum;
        switch (typeEnum) {
            case APP_LOGIN:
                smsTypeEnum = SmsTypeEnum.STAR_TASK_MINA_LOGIN;
                break;
            case BACKEND_LOGIN:
                smsTypeEnum = SmsTypeEnum.STAR_TASK_WEB_LOGIN;
                break;
            case BACKEND_RECHARGE:
                smsTypeEnum = SmsTypeEnum.STAR_TASK_WEB_MERCHANT_RECHARGE;
                break;
            default:
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("验证码类型错误");
        }
        // 风控校验
        this.checkSms(phoneNumber, smsTypeEnum, smdCode, appletId);
        // 发送验证码
        final RBucket<String> cacheVerify = redissonClient.getBucket(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_SMS_VERIFY,
                appletId, smsTypeEnum.getValue(), phoneNumber), StringCodec.INSTANCE);

        // 设置5分钟验证码缓存
        cacheVerify.set(smdCode, 5, TimeUnit.MINUTES);
        // 判断是否发送短信验证
        if (BooleanValueEnum.isTrue(dingDingStarTaskSmsNotifyConfig.getEnabled())) {
            String[] parts = new String[] {
                    "#### 来探呗短信验证",
                    StrUtil.format("> 短信类型：**{}**", smsTypeEnum.getName()),
                    StrUtil.format("> 手机号码：**{}**", phoneNumber),
                    StrUtil.format("> 验证码：**{}**", smdCode),
                    StrUtil.format("> 发送时间：{}", DateUtil.format(new Date(), FsDateUtils.MM_DD_HH_MM)),
                    StrUtil.format("> 有效期：5分钟", phoneNumber)
            };
            DingTalkUtil.directSendDingDingWebHookMarkdownMessage(dingDingStarTaskSmsNotifyConfig.getToken(), dingDingStarTaskSmsNotifyConfig.getSecret(),
                    "来探呗", Joiner.on(StringPool.CRLF).join(parts));
        }
    }

    /**
     * 风控校验，发送验证码
     *
     * @param phoneNumber
     * @param smsTypeEnum
     * @param code
     * @param appletId
     */
    private void checkSms(String phoneNumber, SmsTypeEnum smsTypeEnum, String code, String appletId) {
        Integer type = smsTypeEnum.getValue();
        // 发送间隔要超过一分钟
        final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_SMS_REQUEST, appletId, type, phoneNumber), StringCodec.INSTANCE);
        if (bucket.isExists()) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("手机号码验证码发送频繁，请稍后再试");
        }

        // 30分钟内，只能发送3次
        final RAtomicLong cacheCount = redissonClient.getAtomicLong(
                StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_SMS_REQUEST_COUNT, appletId, type, phoneNumber));

        if (!SmsTypeEnum.STAR_TASK_WEB_MERCHANT_RECHARGE.equals(smsTypeEnum)) {
            if (cacheCount.get() >= SystemConstants.SEND_SMS_MAX_COUNT) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("手机号码验证码发送过于频繁，请30分钟后再试");
            }
        }
        // 发送验证码
        final SmsMessageParam smsMessageParam = new SmsMessageParam();
        smsMessageParam.setParam(new SmsMessageCodeParam(code));
        smsMessageParam.setSign(smsTypeEnum.getSignEnum());
        smsMessageParam.setSmsTemplateCodeEnum(smsTypeEnum.getTemplateCodeEnum());
        smsMessageParam.setPhoneNumber(phoneNumber);
        smsService.sendMessage(smsMessageParam);
        // 增加一次发送次数
        cacheCount.getAndIncrement();
        // 第一次加过期时间
        if (cacheCount.get() == 1) {
            cacheCount.expire(30, TimeUnit.MINUTES);
        }
        // 设置一分钟缓存
        bucket.set(phoneNumber, 1, TimeUnit.MINUTES);
    }


    /**
     * 构建登录信息
     *
     * @param phone
     * @param appletId
     * @param userId
     * @param model
     */
    private void buildLoginInfo(String phone, String appletId, String userId, StarTaskMinaLoginModel model) {
        // 查询用户身份信息
        List<StarTaskIdentityDO> identityList = starTaskIdentityDAO.findIdentityList(userId);
        if (CollectionUtil.isEmpty(identityList)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账号异常");
        }
        // 下线
        StpUtil.logout();
        // 登录
        SaLoginModel loginModel = new SaLoginModel();
        loginModel.setTimeout(60 * 60 * 24 * 7);
        loginModel.setDevice("star.task.mina");
        StpUtil.login(userId, loginModel);
        // 获取accessToken
        final String accessToken = StpUtil.getTokenInfo().getTokenValue();
        model.setAccessToken(accessToken);
        model.setAppletId(appletId);
        model.setPhoneNumber(phone);
        model.setUserId(userId);
        List<StarTaskIdentityModel> identityModelList = Lists.newArrayList();
        boolean flag = Boolean.TRUE;
        // 获取邀请人手机号和商家名称
        List<String> identityIdList = identityList.stream().map(StarTaskIdentityDO::getIdentityId).collect(Collectors.toList());
        List<FindIdentityByInviterPhoneListResultDTO> identityInfoList = starTaskIdentityDAO.findIdentityByIdentityIdList(identityIdList);
        Map<String, FindIdentityByInviterPhoneListResultDTO> map = identityInfoList.stream().collect(Collectors.toMap(FindIdentityByInviterPhoneListResultDTO::getIdentityId, Function.identity(), (v1, v2) -> v2));
        for (StarTaskIdentityDO starTaskIdentityDO : identityList) {
            Integer identityType = starTaskIdentityDO.getIdentityType();
            Integer accountStatus = starTaskIdentityDO.getAccountStatus();
            StarTaskIdentityModel identityModel = new StarTaskIdentityModel();
            identityModel.setIdentityId(starTaskIdentityDO.getIdentityId());
            identityModel.setIdentityType(identityType);
            identityModel.setAccountStatus(accountStatus);
            identityModel.setNickname(starTaskIdentityDO.getNickname());
            identityModel.setAvatar(starTaskIdentityDO.getAvatar());
            FindIdentityByInviterPhoneListResultDTO dto = map.getOrDefault(starTaskIdentityDO.getIdentityId(), new FindIdentityByInviterPhoneListResultDTO());
            identityModel.setInviterPhone(StringUtils.isNotBlank(dto.getPhoneNumber()) ? FieldEncryptUtil.decode(dto.getPhoneNumber()) : StringPool.EMPTY);
            if (StarTaskIdentityTypeEnum.MERCHANT.getValue().equals(identityType)) {
                identityModel.setInviterName(StringUtils.isNotBlank(dto.getInviterName()) ? dto.getInviterName() : StringPool.EMPTY);
            } else {
                String city = starTaskIdentityDO.getCity();
                AilikeGaodeCodeDO codeInfo = ailikeGaodeCodeDAO.getCodeInfo(city);
                identityModel.setCityName(null == codeInfo ? StringPool.EMPTY : codeInfo.getAdname());
            }
            identityModelList.add(identityModel);
            if (BooleanEnum.YES.getValue().equals(accountStatus)) {
                flag = Boolean.FALSE;
            }
        }
        if (flag) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账号已停用");
        }
        model.setIdentityList(identityModelList);
        // sa-token存储登录信息
        StpUtil.getTokenSessionByToken(accessToken).set(accessToken, JSON.toJSONString(model));
        StarTaskUserDO userDO = starTaskUserDAO.getUserInfoByUserId(userId);
        if (null != userDO && StringUtils.isBlank(userDO.getOpenId())) {
            userDO.setOpenId(model.getOpenId());
            starTaskUserDAO.updateById(userDO);
        }
    }

    /**
     * 确认身份选择并登录
     *
     * @param param
     * @return
     */
    @Override
    public StarTaskMinaLoginModel confirmIdentity(ConfirmIdentityParam param) {
        Integer identityType = param.getIdentityType();
        String phoneNumber = param.getPhoneNumber();
        String appletId = param.getAppletId();
        // 校验邀请人信息，获取身份关系信息
        StarTaskIdentityRelationDO relationDO = this.getStarTaskIdentityRelationDO(param);
        String identityId = StarTaskConstant.IDENTITY_PREFIX + commonService.buildIncr();
        // 判断是否存在账号信息
        StarTaskUserDO userInfo = starTaskUserDAO.getUserInfo(phoneNumber, appletId);
        // 身份信息
        StarTaskIdentityDO starTaskIdentityDO = new StarTaskIdentityDO();
        starTaskIdentityDO.setIdentityId(identityId);
        starTaskIdentityDO.setIdentityType(identityType);
        starTaskIdentityDO.setAccountStatus(BooleanEnum.YES.getValue());
        starTaskIdentityDO.setAppletId(appletId);
        starTaskIdentityDO.setPartnerCode(param.getPartnerCode());
        // 账户信息
        StarTaskBalanceAccountDO balanceAccountDO = new StarTaskBalanceAccountDO();
        balanceAccountDO.setIdentityId(identityId);
        String userId;
        if (Objects.nonNull(userInfo)) {
            userId = userInfo.getUserId();
            // 判断是否存在身份信息
            StarTaskIdentityDO identityInfo = starTaskIdentityDAO.getIdentityInfo(userId, identityType);
            if (Objects.nonNull(identityInfo)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息已存在");
            }
        } else {
            userId = StarTaskConstant.USER_PREFIX + commonService.buildIncr();
        }
        starTaskIdentityDO.setUserId(userId);
        // 手机号
        String encode = FieldEncryptUtil.encode(phoneNumber);
        String encodeLikeFieldExt = FieldEncryptUtil.encodeLikeFieldExt(phoneNumber);
        starTaskIdentityDO.setPhoneNumber(encode);
        starTaskIdentityDO.setPhoneNumberEncryptExt(encodeLikeFieldExt);
        balanceAccountDO.setUserId(userId);
        relationDO.setUserId(userId);
        relationDO.setIdentityId(identityId);
        relationDO.setIdentityType(identityType);
        relationDO.setDistributionType(DistributionTypeEnum.DEFAULT.getValue());
        // 事物
        transactionTemplate.execute(status -> {
            if (Objects.isNull(userInfo)) {
                StarTaskUserDO starTaskUserDO = new StarTaskUserDO();
                starTaskUserDO.setUserId(userId);
                starTaskUserDO.setAppletId(appletId);
                starTaskUserDO.setPhoneNumber(encode);
                starTaskUserDO.setPhoneNumberEncryptExt(encodeLikeFieldExt);
                starTaskUserDO.setOpenId(param.getOpenId());
                starTaskUserDAO.saveStarTaskUser(starTaskUserDO);
            } else if (StringUtils.isBlank(userInfo.getOpenId())) {
                userInfo.setOpenId(param.getOpenId());
                starTaskUserDAO.updateById(userInfo);
            }
            starTaskIdentityDAO.saveStarTaskIdentity(starTaskIdentityDO);
            // 新增账户表
            starTaskBalanceAccountDAO.saveBalanceAccount(balanceAccountDO);
            // 新增身份关系表
            starTaskIdentityRelationDAO.saveIdentityRelation(relationDO);
            return Boolean.TRUE;
        });
        // 构建返回信息
        StarTaskMinaLoginModel model = new StarTaskMinaLoginModel();
        model.setOpenId(param.getOpenId());
        this.buildLoginInfo(phoneNumber, appletId, userId, model);
        return model;
    }

    /**
     * 校验邀请人信息，获取身份关系信息
     *
     * @param param
     * @return
     */
    private StarTaskIdentityRelationDO getStarTaskIdentityRelationDO(ConfirmIdentityParam param) {
        // 查询邀请人信息
        String inviterPhone = param.getInviterPhone();
        // 查询合作方信息
        if (StringUtils.isNotBlank(param.getPartnerCode())) {
            val partnerCodeConfig = starTaskPartnerCodeConfigDAO.queryPartnerCodeConfig(param.getPartnerCode(), param.getIdentityType());
            if (Objects.nonNull(partnerCodeConfig)) {
                inviterPhone = FieldEncryptUtil.decode(partnerCodeConfig.getPhoneNumber());
            }
        }
        if (StringUtils.isNotBlank(inviterPhone)) {
            inviterPhone = FieldEncryptUtil.encode(inviterPhone);
        }
        // 校验邀请人信息
        StarTaskIdentityRelationDO relationDO = new StarTaskIdentityRelationDO();
        if (StringUtils.isNotBlank(inviterPhone)) {
            StarTaskIdentityDO identityInfo = starTaskIdentityDAO.getIdentityByPhone(inviterPhone, param.getIdentityType(), param.getAppletId());
            if (null == identityInfo) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("邀请人手机号不存在");
            }
            relationDO.setParentId(identityInfo.getIdentityId());
            StarTaskIdentityRelationDO identityRelationDO = starTaskIdentityRelationDAO.findListByIdentityId(identityInfo.getIdentityId());
            if (Objects.nonNull(identityRelationDO)) {
                relationDO.setGrandparentId(identityRelationDO.getParentId());
            }
        }
        String inviterId = param.getInviterId();
        // 邀请人id
        if (StringUtils.isNotBlank(inviterId)) {
            relationDO.setParentId(inviterId);
            StarTaskIdentityRelationDO identityRelationDO = starTaskIdentityRelationDAO.findListByIdentityId(inviterId);
            if (Objects.nonNull(identityRelationDO)) {
                relationDO.setGrandparentId(identityRelationDO.getParentId());
            }
        }
        return relationDO;
    }

    /**
     * 获取协议列表
     *
     * @return
     */
    @Override
    public FindProtocolListModel findProtocolList() {
        // 获取配置
        String starTaskMinaProtocolList = sysConfig.getStarTaskMinaProtocolList();
        FindProtocolListModel model = new FindProtocolListModel();
        model.setProtocolList(JSONObject.parseArray(starTaskMinaProtocolList, FindProtocolDetailModel.class));
        return model;
    }

    /**
     * 退出登录
     */
    @Override
    public void logout() {
        // 下线
        StpUtil.logout();
    }

    /**
     * 登录页获取邀请人信息
     *
     * @param param
     * @return
     */
    @Override
    public GetLoginInfoModel getLoginInfo(GetLoginInfoParam param) {
        StarTaskIdentityDO identityDO;
        if (StringUtils.isNotBlank(param.getIdentityId())) {
            identityDO = starTaskIdentityDAO.getIdentityByIdentityId(param.getIdentityId());
        } else {
            val partnerConfigList = starTaskPartnerCodeConfigDAO.queryPartnerCodeConfigList(param.getPartnerCode());
            if (CollectionUtil.isEmpty(partnerConfigList)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("合作方标识码不正确");
            }
            StarTaskPartnerCodeConfigDO configDO = partnerConfigList.get(0);
            identityDO = starTaskIdentityDAO.getIdentityByPhoneAndIdentityType(configDO.getPhoneNumber(), configDO.getIdentityType(), configDO.getMinaAppId());
        }
        if (Objects.isNull(identityDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息不存在");
        }
        GetLoginInfoModel model = new GetLoginInfoModel();
        // 名称
        String merchantName = identityDO.getMerchantName();
        model.setName(merchantName);
        if (StringUtils.isBlank(merchantName)) {
            model.setName(StringUtils.isBlank(identityDO.getNickname()) ? PhoneNumberUtil.mask(FieldEncryptUtil.decode(identityDO.getPhoneNumber())) : identityDO.getNickname());
        }
        // 头像
        model.setAvatar(identityDO.getAvatar());
        model.setIdentityType(identityDO.getIdentityType());
        model.setIdentityId(identityDO.getIdentityId());
        return model;
    }

    /**
     * 注册邀请用户
     *
     * @param param
     * @return
     */
    @Override
    public StarTaskMinaLoginModel registerInvitedUser(RegisterInvitedUserParam param) {
        StarTaskMinaLoginTypeEnum loginTypeEnum = StarTaskMinaLoginTypeEnum.getByValue(param.getLoginType());
        if (Objects.isNull(loginTypeEnum)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("登录类型错误");
        }
        String phone = param.getAccount();
        String appletId = param.getAppletId();
        String inviterId = param.getInviterId();
        switch (loginTypeEnum) {
            case QUICK_LOGIN:
                // 需要解析小程序手机号
                WechatMinaGetUserPhoneNumberModel userPhoneNumberModel = starTaskWechatMinaService.getUserPhoneNumber(StarTaskConstant.DEFAULT_MINA_CHANNEL, param.getAuthCode());
                phone = userPhoneNumberModel.getPhoneNumber();
                break;
            case CODE_LOGIN:
                String code = param.getCode();
                if (sysConfig.isTest() || sysConfig.isDev()) {
                    break;
                }
                // 校验验证码是否正确
                final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_SMS_VERIFY
                        , appletId, SmsTypeEnum.STAR_TASK_MINA_LOGIN.getValue(), phone), StringCodec.INSTANCE);
                String smdCode = bucket.get();
                if (StringUtils.isBlank(smdCode)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("验证码已失效，请重新发送");
                }
                if (Objects.isNull(code) || !smdCode.equals(code)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("验证码错误，请重新输入");
                }
                break;
            default:
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("类型错误");
        }
        // 根据身份id查询身份信息
        StarTaskIdentityDO inviterIdentityInfo = starTaskIdentityDAO.getIdentityByIdentityId(inviterId);
        if (Objects.isNull(inviterIdentityInfo)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息不存在");
        }
        Integer identityType = inviterIdentityInfo.getIdentityType();
        // 判断受邀请的人是否已经注册
        StarTaskIdentityDO identityInfo = starTaskIdentityDAO.getIdentityByPhoneAndIdentityType(FieldEncryptUtil.encode(phone), identityType, appletId);
        if (Objects.nonNull(identityInfo)) {
            StarTaskMinaLoginModel model = new StarTaskMinaLoginModel();
            model.setHasRegister(Boolean.FALSE);
            model.setOpenId(param.getOpenId());
            // 已经存在账号直接登录
            this.buildLoginInfo(phone, appletId, identityInfo.getUserId(), model);
            return model;
        }
        // 否则，走注册逻辑
        ConfirmIdentityParam identityParam = new ConfirmIdentityParam();
        identityParam.setAppletId(appletId);
        identityParam.setInviterId(inviterId);
        identityParam.setPhoneNumber(phone);
        identityParam.setIdentityType(identityType);
        identityParam.setOpenId(param.getOpenId());
        StarTaskMinaLoginModel starTaskMinaLoginModel = this.confirmIdentity(identityParam);
        starTaskMinaLoginModel.setHasRegister(Boolean.TRUE);
        return starTaskMinaLoginModel;
    }
}