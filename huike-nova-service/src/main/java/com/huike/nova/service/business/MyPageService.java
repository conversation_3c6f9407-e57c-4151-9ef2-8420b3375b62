/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.dao.domain.result.OemMerchantAgentInfoDTO;
import com.huike.nova.service.domain.model.miniprogram.GetOemMerchantDetailModel;
import com.huike.nova.service.domain.model.mypage.GetMerchantDetailModel;
import com.huike.nova.service.domain.model.mypage.StoreListModel;
import com.huike.nova.service.domain.model.mypage.WhiteListCheckModel;
import com.huike.nova.service.domain.param.mypage.FeedbackAddParam;
import com.huike.nova.service.domain.param.mypage.GetMerchantDetailParam;
import com.huike.nova.service.domain.param.mypage.StoreListParam;
import com.huike.nova.service.domain.param.mypage.WhiteListCheckParam;

/**
 * <AUTHOR>
 * @version MyPageService.java, v 0.1 2022-09-01 11:09 AM ruanzy
 */
public interface MyPageService {

    /**
     * 查询商户信息
     *
     * @param param
     * @return
     */
    GetMerchantDetailModel getMerchantDetail(GetMerchantDetailParam param);

    /**
     * 查询门店列表
     *
     * @param param
     * @return
     */
    PageResult<StoreListModel> findStorePage(PageParam<StoreListParam> param);

    /**
     * 白名单校验
     *
     * @param param
     * @return
     */
    WhiteListCheckModel checkWhiteList(WhiteListCheckParam param);

    /**
     * 意见反馈
     *
     * @param param
     */
    void addFeedback(FeedbackAddParam param);

    /**
     * 查询小程序Oem商家信息
     *
     * @param param
     * @return
     */
    GetOemMerchantDetailModel getOemMerchantAgentInfo(GetMerchantDetailParam param);
}