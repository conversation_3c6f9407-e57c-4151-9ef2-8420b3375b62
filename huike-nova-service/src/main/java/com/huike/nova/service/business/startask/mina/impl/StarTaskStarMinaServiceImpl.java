/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.mina.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.constant.startask.mina.StarTaskMinaRedisPrefixConstant;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.PlatformTypeEnum;
import com.huike.nova.common.enums.QuotaFullEnum;
import com.huike.nova.common.enums.oem.BooleanEnum;
import com.huike.nova.common.enums.startask.mina.ApplyListStatusEnum;
import com.huike.nova.common.enums.startask.mina.BindRelationStatusEnum;
import com.huike.nova.common.enums.startask.mina.CommissionTypeEnum;
import com.huike.nova.common.enums.startask.mina.OperateApplyListEnum;
import com.huike.nova.common.enums.startask.mina.OperateStarTypeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskTypeEnum;
import com.huike.nova.common.enums.startask.mina.TaskStatusEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.common.util.DesensitizationUtil;
import com.huike.nova.common.util.IdWorkerUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.StringUtil2;
import com.huike.nova.common.util.WebUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.param.startask.FindTaskPlazaListParamDTO;
import com.huike.nova.dao.domain.param.startask.StarAccountParamDTO;
import com.huike.nova.dao.domain.param.startask.StarApplyListParamDTO;
import com.huike.nova.dao.domain.result.startask.FindTaskPlazaListResultDTO;
import com.huike.nova.dao.domain.result.startask.StarAccountResultDTO;
import com.huike.nova.dao.domain.result.startask.StarApplyListResultDTO;
import com.huike.nova.dao.entity.AilikeGaodeCodeDO;
import com.huike.nova.dao.entity.StarTaskApplyListDO;
import com.huike.nova.dao.entity.StarTaskAreaLimitDO;
import com.huike.nova.dao.entity.StarTaskBindRelationDO;
import com.huike.nova.dao.entity.StarTaskBindStarDO;
import com.huike.nova.dao.entity.StarTaskCommissionPlanDO;
import com.huike.nova.dao.entity.StarTaskDO;
import com.huike.nova.dao.entity.StarTaskIdentityDO;
import com.huike.nova.dao.entity.StarTaskPartnerCodeConfigDO;
import com.huike.nova.dao.entity.StarTaskStarSquareDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.AilikeGaodeCodeDAO;
import com.huike.nova.dao.repository.StarTaskApplyListDAO;
import com.huike.nova.dao.repository.StarTaskAreaLimitDAO;
import com.huike.nova.dao.repository.StarTaskBindRelationDAO;
import com.huike.nova.dao.repository.StarTaskBindStarDAO;
import com.huike.nova.dao.repository.StarTaskCommissionPlanDAO;
import com.huike.nova.dao.repository.StarTaskDAO;
import com.huike.nova.dao.repository.StarTaskIdentityDAO;
import com.huike.nova.dao.repository.StarTaskPartnerCodeConfigDAO;
import com.huike.nova.dao.repository.StarTaskStarSquareDAO;
import com.huike.nova.sdk.douyin.DouyinOpenApi;
import com.huike.nova.service.business.AilikeGaodeCodeService;
import com.huike.nova.service.business.CommonService;
import com.huike.nova.service.business.EnterpriseService;
import com.huike.nova.service.business.startask.common.StarTaskAsyncService;
import com.huike.nova.service.business.startask.mina.StarTaskCommonMinaService;
import com.huike.nova.service.business.startask.mina.StarTaskStarMinaService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.mina.StarTaskStarMinaServiceObjMapper;
import com.huike.nova.service.domain.model.amap.ReGeoAddressComponentModel;
import com.huike.nova.service.domain.model.startask.mina.login.StarTaskMinaLoginModel;
import com.huike.nova.service.domain.model.startask.mina.star.AddStarModel;
import com.huike.nova.service.domain.model.startask.mina.star.CheckStarTaskAreaLimitModel;
import com.huike.nova.service.domain.model.startask.mina.star.ConfirmApplyModel;
import com.huike.nova.service.domain.model.startask.mina.star.FindStarGeolocationModel;
import com.huike.nova.service.domain.model.startask.mina.star.FindStarInfoModel;
import com.huike.nova.service.domain.model.startask.mina.star.FindTaskPlazaListModel;
import com.huike.nova.service.domain.model.startask.mina.star.PublishAccountModel;
import com.huike.nova.service.domain.model.startask.mina.star.SelectPublishAccountModel;
import com.huike.nova.service.domain.model.startask.mina.star.SquareStarModel;
import com.huike.nova.service.domain.model.startask.mina.star.StarApplyDetailModel;
import com.huike.nova.service.domain.model.startask.mina.star.StarApplyListModel;
import com.huike.nova.service.domain.model.startask.mina.star.StarCountModel;
import com.huike.nova.service.domain.model.startask.mina.star.StarDetailQueryModel;
import com.huike.nova.service.domain.model.startask.mina.star.StarModel;
import com.huike.nova.service.domain.model.startask.mina.star.StarRedisModel;
import com.huike.nova.service.domain.param.startask.mina.star.AddPartnerCodeParam;
import com.huike.nova.service.domain.param.startask.mina.star.AddStarParam;
import com.huike.nova.service.domain.param.startask.mina.star.CheckStarTaskAreaLimitParam;
import com.huike.nova.service.domain.param.startask.mina.star.ConfirmApplyListParam;
import com.huike.nova.service.domain.param.startask.mina.star.ConfirmApplyParam;
import com.huike.nova.service.domain.param.startask.mina.star.FindStarGeolocationParam;
import com.huike.nova.service.domain.param.startask.mina.star.FindStarInfoParam;
import com.huike.nova.service.domain.param.startask.mina.star.FindTaskPlazaListParam;
import com.huike.nova.service.domain.param.startask.mina.star.OperateStarApplyParam;
import com.huike.nova.service.domain.param.startask.mina.star.OperateStarParam;
import com.huike.nova.service.domain.param.startask.mina.star.SaveKsStarParam;
import com.huike.nova.service.domain.param.startask.mina.star.SelectPublishAccountParam;
import com.huike.nova.service.domain.param.startask.mina.star.StarApplyDetailParam;
import com.huike.nova.service.domain.param.startask.mina.star.StarApplyListParam;
import com.huike.nova.service.domain.param.startask.mina.star.StarDetailQueryParam;
import com.huike.nova.service.domain.param.startask.mina.star.UpdateStarInfoParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version StarTaskStarMinaServiceImpl.java, v 0.1 2023-11-29 1:50 PM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class StarTaskStarMinaServiceImpl implements StarTaskStarMinaService {

    private StarTaskDAO starTaskDAO;

    private StarTaskCommissionPlanDAO starTaskCommissionPlanDAO;

    private StarTaskApplyListDAO starTaskApplyListDAO;

    private StarTaskBindRelationDAO starTaskBindRelationDAO;

    private StarTaskBindStarDAO starTaskBindStarDAO;

    private StarTaskStarSquareDAO starTaskStarSquareDAO;

    private StarTaskPartnerCodeConfigDAO starTaskPartnerCodeConfigDAO;

    private TransactionTemplate transactionTemplate;

    private RedissonClient redissonClient;

    private CommonService commonService;

    private SysConfig sysConfig;

    private StarTaskAsyncService starTaskAsyncService;

    private StarTaskStarMinaServiceObjMapper starTaskStarMinaServiceObjMapper;

    private StarTaskCommonMinaService starTaskCommonMinaService;

    private AilikeGaodeCodeDAO ailikeGaodeCodeDAO;

    private StarTaskIdentityDAO starTaskIdentityDAO;

    private EnterpriseService enterpriseService;

    private StarTaskAreaLimitDAO starTaskAreaLimitDAO;

    private AilikeGaodeCodeService ailikeGaodeCodeService;


    /**
     * 达人任务广场列表
     *
     * @param param
     * @return
     */
    @Override
    public PageResult<FindTaskPlazaListModel> findTaskPlazaList(PageParam<FindTaskPlazaListParam> param) {
        Page<FindTaskPlazaListResultDTO> resultDTOPage = starTaskDAO.findTaskPlazaList(this.getFindTaskPlazaListParamDTOPageParam(param));
        List<FindTaskPlazaListResultDTO> records = resultDTOPage.getRecords();
        List<FindTaskPlazaListModel> list = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(records)) {
            List<String> taskIdList = records.stream().map(item -> item.getTaskId()).collect(Collectors.toList());
            List<StarTaskCommissionPlanDO> commissionPlanByTaskIds = starTaskCommissionPlanDAO.findCommissionPlanByTaskIdList(taskIdList);
            Map<String, List<StarTaskCommissionPlanDO>> commissionPlanMap = commissionPlanByTaskIds.stream().collect(Collectors.groupingBy(StarTaskCommissionPlanDO::getTaskId));
            for (FindTaskPlazaListResultDTO record : records) {
                Date applyEndTime = record.getApplyEndTime();
                String taskId = record.getTaskId();
                FindTaskPlazaListModel listModel = starTaskStarMinaServiceObjMapper.toPublishTaskListModel(record);
                // 报名结束时间
                if (BooleanEnum.YES.getValue().equals(record.getRecruitmentCycle())) {
                    listModel.setApplyEndTimeHour((Duration.between(Instant.now(), applyEndTime.toInstant()).toMinutes() + 59) / 60);
                }
                // 等级限制列表
                List<StarTaskCommissionPlanDO> commissionPlanDOList = commissionPlanMap.getOrDefault(taskId, Lists.newArrayList());
                // 报名进度
                if (CollectionUtil.isNotEmpty(commissionPlanDOList)) {
                    this.buildCommissionPlan(listModel, commissionPlanDOList);
                }
                // 达人区域列表
                if (BooleanEnum.YES.getValue().equals(record.getIsStarAreaLimit())) {
                    listModel.setStarAreaName(starTaskCommonMinaService.findStarAreaNameList(taskId));
                }
                //团长收益 = 团长至高收益*团长分销
                listModel.setParentInCome(record.getMaxTaskMoney().multiply(sysConfig.getStarTaskMinaStarDistributionRate()).divide(BigDecimal.valueOf(CommonConstant.INTEGER_HUNDRED)).setScale(CommonConstant.INTEGER_TWO, BigDecimal.ROUND_HALF_UP).toString());
                list.add(listModel);
            }
        }
        PageResult<FindTaskPlazaListModel> pageResult = new PageResult<>();
        pageResult.setRecords(list);
        pageResult.setCurrent(resultDTOPage.getCurrent());
        pageResult.setTotal(resultDTOPage.getTotal());
        pageResult.setSize(resultDTOPage.getSize());
        return pageResult;
    }

    /**
     * 达人任务-参与列表
     *
     * @param param
     * @return
     */
    @Override
    public PageResult<StarApplyListModel> list(PageParam<StarApplyListParam> param) {
        // 登录态获取用户信息
        StarTaskMinaLoginModel loginBasicInfo = LoginUtil.getStarTaskLoginBasicInfo(StarTaskIdentityTypeEnum.STAR.getValue());
        // 查询达人任务参与列表
        PageParam<StarApplyListParamDTO> pageParam = new PageParam<>();
        pageParam.setPage(param.getPage());
        pageParam.setPageSize(param.getPageSize());
        StarApplyListParamDTO paramDTO = new StarApplyListParamDTO();
        paramDTO.setStatus(param.getQuery().getStatus());
        paramDTO.setUserId(loginBasicInfo.getUserId());
        paramDTO.setIdentityId(loginBasicInfo.getIdentityId());
        pageParam.setQuery(paramDTO);
        Page<StarApplyListResultDTO> resultDTOPage = starTaskApplyListDAO.findStarApplyListPage(pageParam);
        List<StarApplyListResultDTO> records = resultDTOPage.getRecords();
        List<StarApplyListModel> list = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(records)) {
            List<String> taskIdList = records.stream().map(StarApplyListResultDTO::getTaskId).collect(Collectors.toList());
            List<StarTaskCommissionPlanDO> commissionPlanByTaskIds = starTaskCommissionPlanDAO.findCommissionPlanByTaskIdList(taskIdList);
            Map<String, List<StarTaskCommissionPlanDO>> commissionPlanMap = commissionPlanByTaskIds.stream().collect(Collectors.groupingBy(StarTaskCommissionPlanDO::getTaskId));
            for (StarApplyListResultDTO record : records) {
                String taskId = record.getTaskId();
                StarApplyListModel applyListModel = starTaskStarMinaServiceObjMapper.toStarApplyListModel(record);
                // 等级限制列表
                List<StarTaskCommissionPlanDO> commissionPlanDOList = commissionPlanMap.getOrDefault(taskId, Lists.newArrayList());
                // 报名进度
                applyListModel.setStarLevelLimitList(commissionPlanDOList.stream().map(t -> t.getStarLevelLimit()).collect(Collectors.toList()));
                // 达人区域列表
                if (BooleanEnum.YES.getValue().equals(record.getIsStarAreaLimit())) {
                    applyListModel.setStarAreaName(starTaskCommonMinaService.findStarAreaNameList(taskId));
                }
                // 任务截止
                Date taskClosingTime = record.getTaskClosingTime();
                if (BooleanEnum.YES.getValue().equals(record.getRecruitmentCycle()) && Objects.nonNull(taskClosingTime)) {
                    applyListModel.setTaskClosingTimeHour((Duration.between(Instant.now(), taskClosingTime.toInstant()).toMinutes() + 59) / 60);
                }
                if (StringUtils.isNotBlank(record.getMerchantPhoneNumber())) {
                    applyListModel.setMerchantPhoneNumber(FieldEncryptUtil.decode(record.getMerchantPhoneNumber()));
                }
                list.add(applyListModel);
            }
        }
        PageResult<StarApplyListModel> pageResult = new PageResult<>();
        pageResult.setRecords(list);
        pageResult.setCurrent(resultDTOPage.getCurrent());
        pageResult.setTotal(resultDTOPage.getTotal());
        pageResult.setSize(resultDTOPage.getSize());
        return pageResult;
    }

    /**
     * 参与任务操作
     *
     * @param param
     */
    @Override
    public void operate(OperateStarApplyParam param) {
        RLock lock = redissonClient.getLock(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_APPLY_LIST, param.getApplyId()));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                LogUtil.error(log, "StarTaskStarMinaServiceImpl" + "." + "operate" + " >>>>> " + "锁获取失败");
                throw new CommonException(ErrorCodeEnum.GET_LOCK_ERROR).detailMessage("StarTaskStarMinaServiceImpl" + " >>>>> " + "operate" + "  锁获取失败");
            }
            OperateApplyListEnum operateEnum = OperateApplyListEnum.getByValue(param.getOperateType());
            if (Objects.isNull(operateEnum)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("类型不合法");
            }
            // 查询任务报名清单信息
            StarTaskApplyListDO applyListDO = starTaskApplyListDAO.getApplyListByApplyId(param.getApplyId());
            if (Objects.isNull(applyListDO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("参与的任务信息异常");
            }
            Integer applyStatus = applyListDO.getApplyStatus();
            switch (operateEnum) {
                case SUBMIT_LINK:
                    // 待提交和已驳回状态可以提交链接
                    if (!ApplyListStatusEnum.WAITING_SUBMIT.getValue().equals(applyStatus) && !ApplyListStatusEnum.REJECTED.getValue().equals(applyStatus)) {
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("参与任务状态变化，该操作不允许");
                    }
                    // 校验任务
                    this.checkTask(applyListDO);
                    applyListDO.setApplyStatus(ApplyListStatusEnum.LINK_AUDIT.getValue());
                    applyListDO.setSubmitTime(new Date());
                    applyListDO.setApplyLink(param.getApplyLink());
                    starTaskApplyListDAO.updateApplyList(applyListDO);
                    break;
                case CANCEL_REGISTRATION:
                    // 取消任务
                    applyListDO.setCancelReason("手动取消");
                    starTaskAsyncService.cancelRegistration(applyListDO);
                    break;
                case MODIFY_LINK:
                    // 审核中允许修改链接
                    if (!ApplyListStatusEnum.LINK_AUDIT.getValue().equals(applyStatus) && !ApplyListStatusEnum.PLATFORM_AUDIT.getValue().equals(applyStatus)) {
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("不属于审核中的参与任务");
                    }
                    // 校验任务
                    this.checkTask(applyListDO);
                    applyListDO.setSubmitTime(new Date());
                    applyListDO.setApplyLink(param.getApplyLink());
                    starTaskApplyListDAO.updateApplyList(applyListDO);
                    break;
                default:
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("类型不合法");
            }
        } catch (CommonException commonException) {
            throw commonException;
        } catch (Exception e) {
            LogUtil.error(log, "StarTaskStarMinaServiceImpl" + "." + "operate" + " >>>>> " + "执行加锁方法失败", e);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("StarTaskStarMinaServiceImpl" + " >>>>> " + "operate" + "  执行加锁方法失败");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.error(log, "StarTaskStarMinaServiceImpl" + " >>>>> " + "operate" + "  锁释放失败", e);
            }
        }

    }

    /**
     * 任务校验
     *
     * @param applyListDO
     */
    private void checkTask(StarTaskApplyListDO applyListDO) {
        // 判断是否已经到了任务截止时间
        StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(applyListDO.getTaskId());
        if (Objects.isNull(starTaskDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务信息为空");
        }
//        if (!TaskStatusEnum.IN_PROGRESS.getValue().equals(starTaskDO.getTaskStatus())) {
//            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("不是进行中的任务无法修改");
//        }
        if (BooleanEnum.YES.getValue().equals(starTaskDO.getRecruitmentCycle()) && starTaskDO.getTaskClosingTime().before(new Date())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务已截止，无法提交链接");
        }
    }

    /**
     * 达人任务-参与详情
     *
     * @param param
     */
    @Override
    public StarApplyDetailModel detail(StarApplyDetailParam param) {
        String taskId = param.getTaskId();
        // 查询任务信息
        StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(taskId);
        if (Objects.isNull(starTaskDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务信息异常");
        }
        StarApplyDetailModel model = starTaskStarMinaServiceObjMapper.toStarApplyDetailModel(starTaskDO);
        // 等级限制列表
        List<StarTaskCommissionPlanDO> commissionPlanDOList = starTaskCommissionPlanDAO.findCommissionPlanByTaskId(taskId);
        if (CollectionUtil.isEmpty(commissionPlanDOList)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("达人佣金信息为空");
        }
        model.setStarLevelLimitList(commissionPlanDOList.stream().map(t -> t.getStarLevelLimit()).collect(Collectors.toList()));
        // 任务截止
        Date taskClosingTime = starTaskDO.getTaskClosingTime();
        if (BooleanEnum.YES.getValue().equals(starTaskDO.getRecruitmentCycle()) && Objects.nonNull(taskClosingTime)) {
            model.setTaskClosingTimeHour((Duration.between(Instant.now(), taskClosingTime.toInstant()).toMinutes() + 59) / 60);
        }

        //参考样例的图片或视频地址
        if (StringUtils.isNotBlank(starTaskDO.getSampleUrl())) {
            model.setSampleUrlList(JSON.parseArray(starTaskDO.getSampleUrl(), String.class));
        }

        // 达人区域列表
        if (BooleanEnum.YES.getValue().equals(starTaskDO.getIsStarAreaLimit())) {
            model.setStarAreaName(starTaskCommonMinaService.findStarAreaNameList(taskId));
        }
        // 达人报名信息
        StarApplyListResultDTO resultDTO = starTaskApplyListDAO.getApplyListDetail(param.getApplyId());
        if (Objects.isNull(resultDTO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("绑定信息异常");
        }
        model.setTaskMoney(resultDTO.getTaskMoney());
        model.setStarLevel(resultDTO.getStarLevel());
        model.setAvatar(resultDTO.getAvatar());
        model.setFanNumber(resultDTO.getFanNumber());
        model.setNickname(resultDTO.getNickname());
        model.setApplyLink(resultDTO.getApplyLink());
        model.setRejectReason(resultDTO.getRejectReason());
        model.setApplyStatus(resultDTO.getApplyStatus());
        model.setApplyId(resultDTO.getApplyId());
        model.setPlatformType(resultDTO.getPlatformType());
        model.setMerchantPhoneNumber(FieldEncryptUtil.decode(starTaskDO.getStorePhone()));
        return model;
    }

    /**
     * 达人任务-选择发布账号
     *
     * @param param
     * @return
     */
    @Override
    public SelectPublishAccountModel selectPublishAccount(SelectPublishAccountParam param) {
        String taskId = param.getTaskId();
        // 登录态获取用户信息
        StarTaskMinaLoginModel loginBasicInfo = LoginUtil.getStarTaskLoginBasicInfo(StarTaskIdentityTypeEnum.STAR.getValue());
        // 查询用户达人账号信息
        List<StarAccountResultDTO> starAccountList = starTaskBindRelationDAO.findStarAccountList(loginBasicInfo.getUserId(), loginBasicInfo.getIdentityId(), BooleanEnum.YES.getValue());
        // 查询任务信息
        StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(taskId);
        if (Objects.isNull(starTaskDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务信息查询为空");
        }
        // 根据任务id查询佣金计划
        List<StarTaskCommissionPlanDO> commissionPlanList = starTaskCommissionPlanDAO.findCommissionPlanByTaskId(taskId);
        if (CollectionUtil.isEmpty(commissionPlanList)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("达人佣金信息为空");
        }
        CommissionTypeEnum typeEnum = CommissionTypeEnum.getByValue(starTaskDO.getCommissionType());
        if (Objects.isNull(typeEnum)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("佣金类型异常");
        }
        // 校验账号信息
        List<PublishAccountModel> list = this.checkPublishAccount(starAccountList, starTaskDO, commissionPlanList, typeEnum);
        // 构建返回参数
        SelectPublishAccountModel model = new SelectPublishAccountModel();
        model.setPublishAccountList(list);
        return model;
    }

    /**
     * 校验发布账号
     *
     * @param starAccountList
     * @param starTaskDO
     * @param commissionPlanList
     * @param typeEnum
     * @return
     */
    private List<PublishAccountModel> checkPublishAccount(List<StarAccountResultDTO> starAccountList, StarTaskDO starTaskDO, List<StarTaskCommissionPlanDO> commissionPlanList, CommissionTypeEnum typeEnum) {
        List<PublishAccountModel> list = Lists.newArrayList();
        // 校验是否参加过任务
        List<StarTaskApplyListDO> applyListDOS = starTaskApplyListDAO.findNoCancelApplyListByTaskId(starTaskDO.getTaskId());
        Map<String, List<StarTaskApplyListDO>> applyListMap = applyListDOS.stream().collect(Collectors.groupingBy(StarTaskApplyListDO::getRelationId));
        switch (typeEnum) {
            case FIXED_AMOUNT:
                // 固定金额
                StarTaskCommissionPlanDO commissionPlanDO = commissionPlanList.get(0);
                for (StarAccountResultDTO starAccountResultDTO : starAccountList) {
                    PublishAccountModel accountModel = starTaskStarMinaServiceObjMapper.toPublishAccountModel(starAccountResultDTO);
                    if (!BindRelationStatusEnum.BIND.getValue().equals(starAccountResultDTO.getBindStatus())) {
                        list.add(accountModel);
                        continue;
                    }
                    if (CollectionUtil.isNotEmpty(applyListMap.get(starAccountResultDTO.getRelationId()))) {
                        accountModel.setStatus(CommonConstant.INTEGER_FIVE);
                        list.add(accountModel);
                        continue;
                    }
                    //判断账号和任务的平台是否一致
                    if (!starTaskDO.getPlatform().equals(starAccountResultDTO.getPlatformType())) {
                        accountModel.setStatus(CommonConstant.INTEGER_SEVEN);
                        list.add(accountModel);
                        continue;
                    }
                    //扫码直发校验单日发布次数
                    if (CommonConstant.INTEGER_ONE.equals(starTaskDO.getTaskType())) {
                        String starId = starAccountResultDTO.getStarId();
                        RAtomicLong atomicLong = redissonClient.getAtomicLong(StrUtil.format(RedisPrefixConstant.STAR_DAILY_APPLY_NUMBER, starId));
                        if (atomicLong.get() >= sysConfig.getStarDailyApplyLimit()) {
                            accountModel.setStatus(CommonConstant.INTEGER_SIX);
                            list.add(accountModel);
                            continue;
                        }
                    }
                    Integer status = CommonConstant.INTEGER_ONE;
                    // 状态 1-满足 2-粉丝数不够 3-达人等级不够 4-粉丝数、达人等级不够 5-任务已报名 6-非平台账号
                    // 判读粉丝数是否足够
                    if (starAccountResultDTO.getFanNumber() < starTaskDO.getFansNumber()) {
                        status += CommonConstant.INTEGER_ONE;
                    }
                    // 判断是否符合佣金计划
                    if (starAccountResultDTO.getStarLevel() < commissionPlanDO.getStarLevelLimit()) {
                        status += CommonConstant.INTEGER_TWO;
                    } else {
                        accountModel.setCommissionPlanId(commissionPlanDO.getCommissionPlanId());
                        accountModel.setTaskMoney(commissionPlanDO.getTaskMoney());
                    }
                    accountModel.setStatus(status);
                    list.add(accountModel);
                }
                break;
            case LEVEL_STEP:
                // 等级阶梯价
                Map<Integer, StarTaskCommissionPlanDO> commissionPlanDOMap = commissionPlanList.stream()
                        .collect(Collectors.toMap(StarTaskCommissionPlanDO::getStarLevelLimit, Function.identity(), (v1, v2) -> v2));
                for (StarAccountResultDTO starAccountResultDTO : starAccountList) {
                    PublishAccountModel accountModel = starTaskStarMinaServiceObjMapper.toPublishAccountModel(starAccountResultDTO);
                    if (!BindRelationStatusEnum.BIND.getValue().equals(starAccountResultDTO.getBindStatus())) {
                        list.add(accountModel);
                        continue;
                    }
                    if (CollectionUtil.isNotEmpty(applyListMap.get(starAccountResultDTO.getRelationId()))) {
                        accountModel.setStatus(CommonConstant.INTEGER_FIVE);
                        list.add(accountModel);
                        continue;
                    }
                    //扫码直发校验单日发布次数
                    if (CommonConstant.INTEGER_ONE.equals(starTaskDO.getTaskType())) {
                        String starId = starAccountResultDTO.getStarId();
                        RAtomicLong atomicLong = redissonClient.getAtomicLong(StrUtil.format(RedisPrefixConstant.STAR_DAILY_APPLY_NUMBER, starId));
                        if (atomicLong.get() >= sysConfig.getStarDailyApplyLimit()) {
                            accountModel.setStatus(CommonConstant.INTEGER_SIX);
                            list.add(accountModel);
                            continue;
                        }
                    }
                    //判断账号和任务的平台是否一致
                    if (!starTaskDO.getPlatform().equals(starAccountResultDTO.getPlatformType())) {
                        accountModel.setStatus(CommonConstant.INTEGER_SEVEN);
                        list.add(accountModel);
                        continue;
                    }
                    Integer status = CommonConstant.INTEGER_ONE;
                    // 判断粉丝数是否足够
                    if (starAccountResultDTO.getFanNumber() < starTaskDO.getFansNumber()) {
                        status += CommonConstant.INTEGER_ONE;
                    }
                    // 查看是否有符合的计划
                    StarTaskCommissionPlanDO starTaskCommissionPlanDO = commissionPlanDOMap.get(starAccountResultDTO.getStarLevel());
                    if (Objects.isNull(starTaskCommissionPlanDO)) {
                        status += CommonConstant.INTEGER_TWO;
                    } else {
                        accountModel.setCommissionPlanId(starTaskCommissionPlanDO.getCommissionPlanId());
                        accountModel.setTaskMoney(starTaskCommissionPlanDO.getTaskMoney());
                    }
                    accountModel.setStatus(status);
                    list.add(accountModel);
                }
                break;
            default:
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("佣金类型异常");
        }
        return list;
    }

    /**
     * 达人任务-确认报名
     *
     * @param param
     */
    @Override
    public ConfirmApplyModel confirmApply(ConfirmApplyParam param) {
        // 根据任务id做幂等
        RLock lock = redissonClient.getLock(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_STAR_TASK, param.getTaskId()));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                LogUtil.error(log, "StarTaskStarMinaServiceImpl" + "." + "confirmApply" + " >>>>> " + "锁获取失败");
                throw new CommonException(ErrorCodeEnum.GET_LOCK_ERROR).detailMessage("StarTaskStarMinaServiceImpl" + " >>>>> " + "confirmApply" + "  锁获取失败");
            }
            // 确认报名逻辑
            return this.confirmApplyMethod(param);
        } catch (CommonException commonException) {
            throw commonException;
        } catch (Exception e) {
            LogUtil.error(log, "StarTaskStarMinaServiceImpl" + "." + "confirmApply" + " >>>>> " + "执行加锁方法失败", e);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("StarTaskStarMinaServiceImpl" + " >>>>> " + "confirmApply" + "  执行加锁方法失败");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.error(log, "StarTaskStarMinaServiceImpl" + " >>>>> " + "confirmApply" + "  锁释放失败", e);
            }
        }
    }

    /**
     * 添加达人
     *
     * @param param
     */
    @Override
    public AddStarModel addStar(AddStarParam param) {
        // 登录态获取数据
        StarTaskMinaLoginModel loginBasicInfo = LoginUtil.getStarTaskLoginBasicInfo(StarTaskIdentityTypeEnum.STAR.getValue());
        String userId = loginBasicInfo.getUserId();
        String identityId = loginBasicInfo.getIdentityId();
        // 新增达人账号
        String starLink = param.getStarLink();
        if (StringUtils.isBlank(starLink)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("添加达人的链接不能为空");
        }
        // 如果StarLink不为空
        // 从口令中获得链接
        val starPureLink = WebUtil.getUrlFromPhrase(starLink);
        if (StringUtils.isBlank(starPureLink)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("未包含达人链接");
        }
        // 从刚才的纯链接中找到真实的链接
        String starLinkActually = DouyinOpenApi.getRedirectUrl(starPureLink);

        // 从特殊链接中获得抖音Url
        val secUid = DouyinOpenApi.getDouyinSecUidFromUrl(starLinkActually);
        // 有效达人记录
        String starId = null;
        StarTaskBindStarDO bindStarDO = null;
        if (StringUtils.isNotBlank(secUid)) {
            bindStarDO = starTaskBindStarDAO.queryBindStarByDouyinSecUid(secUid, PlatformTypeEnum.TIK_TOK);
        }
        // 生成绑定Id
        String relationId = commonService.buildIncr();
        AddStarModel addStarModel = new AddStarModel();
        //抖音账号已经存在就不能添加
        if (Objects.nonNull(bindStarDO)) {
            starId = bindStarDO.getStarId();
            // 判断达人是否绑定或者绑定中
            List<StarTaskBindRelationDO> bindStarList = starTaskBindRelationDAO.findBindStarListByStarId(starId);
            // 账号已被绑定
            if (CollectionUtil.isNotEmpty(bindStarList)) {
                addStarModel.setStatus(CommonConstant.INTEGER_TWO);
                return addStarModel;
            }
            // 根据身份证Id和达人Id查询绑定记录
            StarTaskBindRelationDO existedBindRelationDO = starTaskBindRelationDAO.queryBindStar(identityId, starId);
            // 绑定记录已存在，只更新绑定状态和绑定时间
            if (Objects.nonNull(existedBindRelationDO)) {
                existedBindRelationDO.setBindStatus(BindRelationStatusEnum.BIND.getValue());
                existedBindRelationDO.setReason(StringUtils.EMPTY);
                existedBindRelationDO.setUpdateTime(new Date());
                starTaskBindRelationDAO.getBaseMapper().updateById(existedBindRelationDO);
                addStarModel.setStatus(CommonConstant.INTEGER_ONE);
                return addStarModel;
            }
        } else {
            // 缓存
            String redisKey = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_RPA_OPERATE_STAR, sysConfig.getEnv());
            RBlockingQueue<String> blockingQueue = redissonClient.getBlockingQueue(redisKey, new StringCodec(StandardCharsets.UTF_8));
            // 构建缓存数据
            StarRedisModel model = new StarRedisModel();
            model.setRelationId(relationId);
            model.setStarLink(starPureLink);
            model.setUserId(userId);
            model.setIdentityId(identityId);
            model.setOperateType(CommonConstant.INTEGER_ONE);
            try {
                blockingQueue.offer(JSON.toJSONString(model), 5, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                // 超时抛出自定义异常
                throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("点击繁忙,请稍后再试");
            }
        }
        // 数据库操作
        StarTaskBindRelationDO bindRelationDO = new StarTaskBindRelationDO();
        bindRelationDO.setRelationId(relationId);
        bindRelationDO.setUserId(userId);
        bindRelationDO.setIdentityId(identityId);
        bindRelationDO.setStarLink(starPureLink);
        bindRelationDO.setStarLinkActually(starLinkActually);
        if (StringUtils.isBlank(starId)) {
            bindRelationDO.setBindStatus(BindRelationStatusEnum.BINDING_IN_PROGRESS.getValue());
        } else {
            bindRelationDO.setStarId(starId);
            bindRelationDO.setBindStatus(BindRelationStatusEnum.BIND.getValue());
        }
        bindRelationDO.setReason(StringPool.EMPTY);
        starTaskBindRelationDAO.saveBindRelation(bindRelationDO);
        addStarModel.setStatus(CommonConstant.INTEGER_ONE);
        return addStarModel;
    }

    /**
     * 保存快手达人信息
     *
     * @param param
     */
    @Override
    public AddStarModel saveKsStar(SaveKsStarParam param) {
        // 登录态获取数据
        StarTaskMinaLoginModel loginBasicInfo = LoginUtil.getStarTaskLoginBasicInfo(StarTaskIdentityTypeEnum.STAR.getValue());
        String userId = loginBasicInfo.getUserId();
        String identityId = loginBasicInfo.getIdentityId();

        //新增快手账号
        if (Objects.isNull(param.getFanNumber()) || StringUtils.isBlank(param.getNickname()) || StringUtils.isBlank(param.getUserAccount())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("必填参数不能为空");
        }

        //根据快手账号查询达人信息
        StarTaskBindStarDO starDO = starTaskBindStarDAO.findByUserAccount(param.getUserAccount(), PlatformTypeEnum.KS.getValue());

        AddStarModel addStarModel = new AddStarModel();
        //判断是否修改快手账号信息
        if (StringUtils.isNotBlank(param.getStarId())) {
            //更新快手达人信息
            StarTaskBindStarDO bindStarDO = starTaskBindStarDAO.findByStarId(param.getStarId());
            if (Objects.isNull(bindStarDO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("修改的快手账号信息不存在");
            }
            if (Objects.nonNull(starDO) && !starDO.getStarId().equals(param.getStarId())) {
                LogUtil.warn(log, "saveKsStar >> 保存快手达人账号已经存在 >> param={}", param);
                addStarModel.setStatus(CommonConstant.INTEGER_TWO);
                return addStarModel;
            }
            bindStarDO.setFanNumber(param.getFanNumber());
            bindStarDO.setNickname(param.getNickname());
            bindStarDO.setUserAccount(param.getUserAccount());
            bindStarDO.setUpdateTime(new Date());
            starTaskBindStarDAO.updateById(bindStarDO);

            addStarModel.setStatus(CommonConstant.INTEGER_ONE);
            return addStarModel;
        }

        //新增达人基本信息
        String starId = IdWorkerUtil.getSingleId();
        if (Objects.isNull(starDO)) {
            starDO = new StarTaskBindStarDO();
            starDO.setStarId(starId);
            starDO.setFanNumber(param.getFanNumber());
            starDO.setNickname(param.getNickname());
            starDO.setPlatformType(PlatformTypeEnum.KS.getValue());
            starDO.setUserAccount(param.getUserAccount());
            starTaskBindStarDAO.save(starDO);

            //绑定关系数据
            this.saveBindStarRelation(identityId, userId, starId);

            addStarModel.setStatus(CommonConstant.INTEGER_ONE);
            return addStarModel;
        } else {
            starId = starDO.getStarId();
            //判断达人是否已经绑定
            List<StarTaskBindRelationDO> bindRelationDOList = starTaskBindRelationDAO.findStarListByStarId(starId);
            if (CollectionUtil.isNotEmpty(bindRelationDOList)) {
                LogUtil.warn(log, "saveKsStar >> 保存快手达人账号已经存在 >> param={}", param);
                addStarModel.setStatus(CommonConstant.INTEGER_TWO);
                return addStarModel;
            }

            //查询当前身份是否有绑定关系
            StarTaskBindRelationDO bindRelationDO = starTaskBindRelationDAO.queryBindStar(identityId, starId);
            if (Objects.nonNull(bindRelationDO)) {
                starTaskBindRelationDAO.updateBindStatus(bindRelationDO.getRelationId(), BindRelationStatusEnum.BIND.getValue());
            } else {
                this.saveBindStarRelation(identityId, userId, starId);
            }

            starDO.setFanNumber(param.getFanNumber());
            starDO.setNickname(param.getNickname());
            starDO.setUserAccount(param.getUserAccount());
            starDO.setUpdateTime(new Date());
            starTaskBindStarDAO.updateById(starDO);

            addStarModel.setStatus(CommonConstant.INTEGER_ONE);
            return addStarModel;
        }
    }


    /**
     * 保存达人绑定关系
     *
     * @param identityId
     * @param userId
     * @param starId
     */
    private void saveBindStarRelation(String identityId, String userId, String starId) {
        //绑定关系数据
        StarTaskBindRelationDO bindRelationDO = new StarTaskBindRelationDO();
        bindRelationDO.setRelationId(commonService.buildIncr());
        bindRelationDO.setStarId(starId);
        bindRelationDO.setIdentityId(identityId);
        bindRelationDO.setUserId(userId);
        bindRelationDO.setBindStatus(BindRelationStatusEnum.BIND.getValue());
        bindRelationDO.setStarLinkActually(StringUtils.EMPTY);
        starTaskBindRelationDAO.save(bindRelationDO);
    }

    /**
     * 达人信息查询
     *
     * @param param
     */
    @Override
    public StarDetailQueryModel starDetailQuery(StarDetailQueryParam param) {
        if (StringUtils.isBlank(param.getStarId())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("达人id不能为空");
        }
        //更新快手达人信息
        StarTaskBindStarDO bindStarDO = starTaskBindStarDAO.findByStarId(param.getStarId());
        if (Objects.isNull(bindStarDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("达人信息不存在");
        }
        StarDetailQueryModel model = new StarDetailQueryModel();
        model.setFanNumber(bindStarDO.getFanNumber());
        model.setNickname(bindStarDO.getNickname());
        model.setUserAccount(bindStarDO.getUserAccount());
        model.setStarId(bindStarDO.getStarId());
        return model;
    }

    /**
     * 达人列表
     *
     * @return
     */
    @Override
    public PageResult<StarModel> findStarList(PageParam param) {
        // 查询登录态信息
        StarTaskMinaLoginModel loginBasicInfo = LoginUtil.getStarTaskLoginBasicInfo(StarTaskIdentityTypeEnum.STAR.getValue());
        String userId = loginBasicInfo.getUserId();
        String identityId = loginBasicInfo.getIdentityId();
        PageParam<StarAccountParamDTO> pageParam = new PageParam<>();
        pageParam.setPage(param.getPage());
        pageParam.setPageSize(param.getPageSize());
        StarAccountParamDTO query = new StarAccountParamDTO();
        query.setUserId(userId);
        query.setIdentityId(identityId);
        pageParam.setQuery(query);
        // 查询所有的达人账号信息
        Page<StarAccountResultDTO> resultDTOPage = starTaskBindRelationDAO.findAllStarList(pageParam);
        return starTaskStarMinaServiceObjMapper.toStarModelPage(resultDTOPage);
    }

    /**
     * 更新达人数据
     *
     * @param param
     */
    @Override
    public void operateStar(OperateStarParam param) {
        RLock lock = redissonClient.getLock(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_STAR, param.getRelationId()));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                LogUtil.error(log, "StarTaskStarMinaServiceImpl" + "." + "operateStar" + " >>>>> " + "锁获取失败");
                throw new CommonException(ErrorCodeEnum.GET_LOCK_ERROR).detailMessage("StarTaskStarMinaServiceImpl" + " >>>>> " + "operateStar" + "  锁获取失败");
            }
            // 更新达人数据
            this.operateStarMethod(param);
        } catch (CommonException commonException) {
            throw commonException;
        } catch (Exception e) {
            LogUtil.error(log, "StarTaskStarMinaServiceImpl" + "." + "operateStar" + " >>>>> " + "执行加锁方法失败", e);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("StarTaskStarMinaServiceImpl" + " >>>>> " + "operateStar" + "  执行加锁方法失败");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.error(log, "StarTaskStarMinaServiceImpl" + " >>>>> " + "operateStar" + "  锁释放失败", e);
            }
        }

    }

    @Override
    public void addPartner(AddPartnerCodeParam param) {
        val record = starTaskPartnerCodeConfigDAO.queryPartnerCodeConfig(param.getPartnerCode(), param.getIdentityType());
        // 判断数据库中是否存在该记录
        if (Objects.isNull(record)) {
            // 插入记录
            val startTaskPartnerCodeConfigDO = new StarTaskPartnerCodeConfigDO();
            startTaskPartnerCodeConfigDO.setPartnerCode(param.getPartnerCode());
            startTaskPartnerCodeConfigDO.setIdentityType(param.getIdentityType());
            startTaskPartnerCodeConfigDO.setPhoneNumber(FieldEncryptUtil.encode(param.getPhoneNumber()));
            startTaskPartnerCodeConfigDO.setMinaAppId(param.getMinaAppId());
            startTaskPartnerCodeConfigDO.setPartnerName(param.getPartnerName());
            starTaskPartnerCodeConfigDAO.getBaseMapper().insert(startTaskPartnerCodeConfigDO);

        } else {
            record.setPhoneNumber(FieldEncryptUtil.encode(param.getPhoneNumber()));
            record.setUpdateTime(new Date());
            record.setPartnerName(param.getPartnerName());
            record.setIsDel(DelFlagEnum.NOT_DEL.getValue());
            starTaskPartnerCodeConfigDAO.getBaseMapper().updateById(record);
        }
    }

    /**
     * 查询达人总数量
     *
     * @return 达人数量
     */
    @Override
    public StarCountModel findStarCount() {
        // 从缓存中查询达人数量
        try {
            val json = redissonClient.<String>getBucket(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_STAR_SQUARE_SUMMARY).get();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(json)) {
                return JSONObject.parseObject(json, StarCountModel.class);
            }
        } catch (Exception ex) {
            LogUtil.warn(log, "StarTaskMerchantMinaServiceImpl.queryStarCount >> 获得达人广场数量失败, ", ex);
        }
        val model = new StarCountModel();
        model.setStarCount(starTaskStarSquareDAO.getStarCount());
        model.setCitiesCount(starTaskStarSquareDAO.getCitiesCount());
        redissonClient.<String>getBucket(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_STAR_SQUARE_SUMMARY).set(JSONObject.toJSONString(model), 1, TimeUnit.MINUTES);
        return model;
    }

    @Override
    public PageResult<SquareStarModel> querySquareStarList(PageParam<Void> param) {
        Page<StarTaskStarSquareDO> page = starTaskStarSquareDAO.querySquareStarList(param);
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            page.getRecords().forEach(p -> p.setNickname(StringUtil2.replaceWithAsterisk(p.getNickname())));
        }
        return starTaskStarMinaServiceObjMapper.toSquareStarModelPageList(page);
    }

    /**
     * 修改达人信息
     *
     * @param param 请求参数
     */
    @Override
    public void updateStar(UpdateStarInfoParam param) {
        LogUtil.info(log, "StarTaskStarMinaServiceImpl.updateStar >> 接口开始 >> param = {}", JSON.toJSONString(param));
        String identityId = param.getIdentityId();
        String nickname = param.getNickname();
        String avatar = param.getAvatar();
        String longitude = param.getLongitude();
        String latitude = param.getLatitude();
        StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(identityId);
        if (null == identityDO) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息异常");
        }
        if (StringUtils.isNotBlank(nickname)) {
            identityDO.setNickname(nickname);
        }
        if (StringUtils.isNotBlank(avatar)) {
            identityDO.setAvatar(avatar);
        }
        if (StringUtils.isNotBlank(longitude) && StringUtils.isNotBlank(latitude)) {
            // 构建达人区域信息
            AilikeGaodeCodeDO gaodeCodeDO = this.buildStarAreaInfo(longitude, latitude);
            if (!gaodeCodeDO.getCityAdcode().equals(identityDO.getCity())) {
                identityDO.setAreaUpdateTime((int) DateUtil.currentSeconds());
            }
            identityDO.setProvince(gaodeCodeDO.getProvinceAdcode());
            identityDO.setCity(gaodeCodeDO.getCityAdcode());
            identityDO.setArea(gaodeCodeDO.getAdcode());
        }
        identityDO.setUpdateTime(new Date());
        starTaskIdentityDAO.updateById(identityDO);
    }

    /**
     * 高德区域信息
     *
     * @param longitude 经度
     * @param latitude  纬度
     */
    private AilikeGaodeCodeDO buildStarAreaInfo(String longitude, String latitude) {
        ReGeoAddressComponentModel reGeoCodeModel = enterpriseService.queryAmapReGeoCodeModel(longitude + StringPool.COMMA + latitude);
        if (null == reGeoCodeModel) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("地理位置信息异常");
        }
        String adCode = reGeoCodeModel.getAdCode();
        AilikeGaodeCodeDO gaodeCodeDO = ailikeGaodeCodeDAO.getCodeInfo(adCode);
        if (null == gaodeCodeDO) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("高德地理信息异常");
        }
        return gaodeCodeDO;
    }

    /**
     * 查询达人信息
     *
     * @param param 请求
     * @return 返参
     */
    @Override
    public FindStarInfoModel findStarInfo(FindStarInfoParam param) {
        String identityId = param.getIdentityId();
        StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(identityId);
        if (null == identityDO) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息异常");
        }
        List<String> list = Arrays.asList(identityDO.getProvince(), identityDO.getCity(), identityDO.getArea());
        List<String> listWithoutEmptyStrings = list.stream()
                .filter(string -> !string.isEmpty())
                .collect(Collectors.toList());
        List<AilikeGaodeCodeDO> codeDOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(listWithoutEmptyStrings)) {
            codeDOList = ailikeGaodeCodeDAO.findCodeInfoList(list);
        }
        Map<String, String> orderRefundTypeMap = codeDOList.stream().collect(Collectors.toMap(AilikeGaodeCodeDO::getAdcode, AilikeGaodeCodeDO::getAdname));
        FindStarInfoModel model = new FindStarInfoModel();
        model.setIdentityId(identityId);
        model.setNickname(identityDO.getNickname());
        model.setAvatar(identityDO.getAvatar());
        model.setProvince(identityDO.getProvince());
        model.setCity(identityDO.getCity());
        model.setArea(identityDO.getArea());
        model.setProvinceName(orderRefundTypeMap.getOrDefault(identityDO.getProvince(), StringPool.EMPTY));
        model.setCityName(orderRefundTypeMap.getOrDefault(identityDO.getCity(), StringPool.EMPTY));
        model.setAreaName(orderRefundTypeMap.getOrDefault(identityDO.getArea(), StringPool.EMPTY));
        model.setPhoneNumber(FieldEncryptUtil.decode(identityDO.getPhoneNumber()));
        // 小程序端数据需要加密
        model.setRealName(DesensitizationUtil.maskName(identityDO.getRealName()));
        model.setIdCardNo(DesensitizationUtil.maskIdCard(FieldEncryptUtil.decode(identityDO.getIdCardNo())));
        return model;
    }

    /**
     * 获取达人地理位置
     *
     * @param param 请求
     * @return 返参
     */
    @Override
    public FindStarGeolocationModel findStarGeolocation(FindStarGeolocationParam param) {
        // 高德区域信息
        AilikeGaodeCodeDO gaodeCodeDO = this.buildStarAreaInfo(param.getLongitude(), param.getLatitude());
        String province = gaodeCodeDO.getProvinceAdcode();
        String city = gaodeCodeDO.getCityAdcode();
        // 获取地址名称
        List<String> codeList = Lists.newArrayList();
        codeList.add(province);
        codeList.add(city);
        Map<String, String> codeInfoMap = ailikeGaodeCodeService.findAreaName(codeList);
        // 构建返回
        FindStarGeolocationModel model = new FindStarGeolocationModel();
        model.setProvince(province);
        model.setCity(city);
        model.setArea(gaodeCodeDO.getAdcode());
        model.setAreaName(gaodeCodeDO.getAdname());
        model.setProvinceName(codeInfoMap.getOrDefault(province, StringPool.EMPTY));
        model.setCityName(codeInfoMap.getOrDefault(city, StringPool.EMPTY));
        return model;
    }

    /**
     * 检查任务区域限制
     *
     * @param param 请求
     * @return 返参
     */
    @Override
    public CheckStarTaskAreaLimitModel checkStarTaskAreaLimit(CheckStarTaskAreaLimitParam param) {
        String taskId = param.getTaskId();
        String identityId = param.getIdentityId();
        return getLimitModel(taskId, identityId);
    }

    /**
     * 校验达人区域限制
     *
     * @param taskId     任务ID
     * @param identityId 达人身份id
     * @return 返参
     */
    private CheckStarTaskAreaLimitModel getLimitModel(String taskId, String identityId) {
        StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(identityId);
        if (null == identityDO || !StarTaskIdentityTypeEnum.STAR.getValue().equals(identityDO.getIdentityType())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("达人身份信息异常");
        }
        StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(taskId);
        if (null == starTaskDO) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务信息不存在");
        }
        Integer isStarAreaLimit = starTaskDO.getIsStarAreaLimit();
        CheckStarTaskAreaLimitModel model = CheckStarTaskAreaLimitModel.init();
        if (BooleanEnum.NO.getValue().equals(isStarAreaLimit)) {
            return model;
        }
        String province = identityDO.getProvince();
        String city = identityDO.getCity();
        if (StringUtils.isBlank(province) || StringUtils.isBlank(city)) {
            model.setIsApply(BooleanEnum.NO.getValue());
            model.setErrorStatus(CommonConstant.INTEGER_ONE);
            return model;
        }
        List<StarTaskAreaLimitDO> starAreaList = starTaskAreaLimitDAO.findStarAreaList(taskId);
        boolean isLimit = Boolean.FALSE;
        for (StarTaskAreaLimitDO item : starAreaList) {
            String starProvince = item.getStarProvince();
            String starCity = item.getStarCity();
            if (province.equals(starProvince)) {
                if (starCity.equals(city) || starCity.equals(CommonConstant.NEGATIVE_ONE)) {
                    isLimit = Boolean.TRUE;
                }
            }
        }
        if (!isLimit) {
            model.setIsApply(BooleanEnum.NO.getValue());
            model.setErrorStatus(CommonConstant.INTEGER_TWO);
            return model;
        }
        return model;
    }

    /**
     * 更新达人数据方法
     *
     * @param param
     */
    private void operateStarMethod(OperateStarParam param) {
        String relationId = param.getRelationId();
        OperateStarTypeEnum typeEnum = OperateStarTypeEnum.getByValue(param.getOperateType());
        if (Objects.isNull(typeEnum)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("操作类型不合法");
        }
        // 判断达人的发布账号是否存在
        StarTaskBindRelationDO relationDO = starTaskBindRelationDAO.getBindRelation(relationId);
        if (Objects.isNull(relationDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("关联信息不存在");
        }
        switch (typeEnum) {
            case UPDATE_DATA:
                // 缓存
                String redisKey = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_RPA_OPERATE_STAR, sysConfig.getEnv());
                RBlockingQueue<String> blockingQueue = redissonClient.getBlockingQueue(redisKey, new StringCodec(StandardCharsets.UTF_8));
                // 构建缓存数据
                StarRedisModel model = new StarRedisModel();
                model.setRelationId(relationId);
                model.setOperateType(CommonConstant.INTEGER_TWO);
                try {
                    blockingQueue.offer(JSON.toJSONString(model), 5, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    // 超时抛出自定义异常
                    throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("点击繁忙,请稍后再试");
                }
                break;
            case UNBIND:
                // 绑定的账号才允许解绑
                if (!BindRelationStatusEnum.BIND.getValue().equals(relationDO.getBindStatus())) {
                    throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("不是绑定状态的账号，不允许解绑");
                }
                // 操作解绑
                starTaskAsyncService.operateUnbind(relationId);
                break;
            default:
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("操作类型不合法");
        }
    }

    /**
     * 确认报名方法
     *
     * @param param
     */
    private ConfirmApplyModel confirmApplyMethod(ConfirmApplyParam param) {
        // 和更新佣金计划数量同一个锁，为了防止查询数量出现不准确
        RLock lock = redissonClient.getLock(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_UPDATE_PARTICIPATION_COUNT, param.getTaskId()));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                LogUtil.error(log, "StarTaskStarMinaServiceImpl" + "." + "confirmApplyMethod" + " >>>>> " + "锁获取失败");
                throw new CommonException(ErrorCodeEnum.GET_LOCK_ERROR).detailMessage("StarTaskStarMinaServiceImpl" + " >>>>> " + "confirmApplyMethod" + "  锁获取失败");
            }
            //查询任务基本信息
            StarTaskDO starTaskDO = this.getStarTaskDO(param.getTaskId());
            Map<String, StarTaskCommissionPlanDO> map = Maps.newHashMap();
            List<StarTaskApplyListDO> list = Lists.newArrayList();
            // 校验，构建数据库参数
            List<String> redisKeyList = Lists.newArrayList();
            List<StarTaskCommissionPlanDO> updatePlanList = Lists.newArrayList();
            // 校验，构建数据库参数    计算可报名的名额
            int sum = this.getStarTaskCommissionPlanDOS(param, map, list, starTaskDO, redisKeyList);

            // 非实探校验佣金计划 人数信息
            if (!StarTaskTypeEnum.OFFLINE_STORE_EXPLORATION.getType().equals(starTaskDO.getTaskType())) {
                map.values().forEach(item -> updatePlanList.add(item));
                if (CollectionUtil.isEmpty(list)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("报名列表为空");
                }
            }
            for (String item : redisKeyList) {
                RAtomicLong atomicLong = redissonClient.getAtomicLong(item);
                atomicLong.getAndIncrement();
                // 获取本地日期
                LocalDate today = LocalDate.now();
                // 获取今天的最后时间
                LocalTime lastTime = LocalTime.MAX;
                // 组合本地日期和最后时间，创建 Instant 对象
                Instant endOfDay = LocalDateTime.of(today, lastTime)
                        .atZone(ZoneId.of("Asia/Shanghai"))
                        .toInstant();
                // 设置有效期  当天的23:59:59.999999999
                atomicLong.expire(endOfDay);
            }
            // 事物
            transactionTemplate.execute(status -> {
                starTaskApplyListDAO.batchStarTaskApplyList(list);
                //非实探任务更新佣金计划人数信息
                if (!StarTaskTypeEnum.OFFLINE_STORE_EXPLORATION.getType().equals(starTaskDO.getTaskType())) {
                    for (StarTaskCommissionPlanDO starTaskCommissionPlanDO : updatePlanList) {
                        starTaskCommissionPlanDAO.updateCommissionPlan(starTaskCommissionPlanDO);
                    }
                }
                // 如果剩余名额小于等于已报名的名额 && 任务类型不为实探任务时 修改任务为满员
                if (sum <= list.size() && !StarTaskTypeEnum.OFFLINE_STORE_EXPLORATION.getType().equals(starTaskDO.getTaskType())) {
                    starTaskDAO.updateIsQuotaFull(param.getTaskId(), QuotaFullEnum.IS_FULL.getValue());
                }
                return Boolean.TRUE;
            });
            ConfirmApplyModel model = new ConfirmApplyModel();
            model.setApplyId(CommonConstant.INTEGER_ONE.equals(list.size()) ? list.get(0).getApplyId() : StringPool.EMPTY);
            return model;
        } catch (CommonException commonException) {
            throw commonException;
        } catch (Exception e) {
            LogUtil.error(log, "StarTaskStarMinaServiceImpl" + "." + "confirmApplyMethod" + " >>>>> " + "执行加锁方法失败", e);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("StarTaskStarMinaServiceImpl" + " >>>>> " + "confirmApplyMethod" + "  执行加锁方法失败");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.error(log, "StarTaskStarMinaServiceImpl" + " >>>>> " + "confirmApplyMethod" + "  锁释放失败", e);
            }
        }
    }


    /**
     * 校验，构建数据库参数
     *
     * @param param
     * @param map
     * @param list
     * @return
     */
    private int getStarTaskCommissionPlanDOS(ConfirmApplyParam param, Map<String, StarTaskCommissionPlanDO> map, List<StarTaskApplyListDO> list, StarTaskDO starTaskDO, List<String> redisKeyList) {
        String taskId = param.getTaskId();
        // 登录态获取用户信息
        StarTaskMinaLoginModel loginBasicInfo = LoginUtil.getStarTaskLoginBasicInfo(StarTaskIdentityTypeEnum.STAR.getValue());
        String userId = loginBasicInfo.getUserId();
        String identityId = loginBasicInfo.getIdentityId();
        Integer limitSwitch = sysConfig.getStarTaskMinaApplyLimitSwitch();
        if (CommonConstant.INTEGER_ONE.equals(limitSwitch)) {
            if (!CommonConstant.INTEGER_ONE.equals(getLimitModel(param.getTaskId(), identityId).getIsApply())) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("达人区域未补齐或不在可报名区域内，请检查达人区域设置是否符合条件");
            }
        }
        // 查询用户达人账号信息
        List<StarAccountResultDTO> starAccountList = starTaskBindRelationDAO.findStarAccountList(userId, identityId, BooleanEnum.NO.getValue());
        Map<String, StarAccountResultDTO> accountResultDTOMap = starAccountList.stream().collect(Collectors.toMap(StarAccountResultDTO::getRelationId, Function.identity(), (v1, v2) -> v2));

        // 根据任务id查询佣金计划
        List<StarTaskCommissionPlanDO> commissionPlanList = starTaskCommissionPlanDAO.findCommissionPlanByTaskId(taskId);
        if (CollectionUtil.isEmpty(commissionPlanList)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("达人佣金信息为空");
        }
        // 判断报名人数是否已满 --> 计算出可报名的名额
        int sum = commissionPlanList.stream().mapToInt(plan -> plan.getNeededCount() - plan.getParticipationCount()).sum();
        Map<String, StarTaskCommissionPlanDO> planDOMap = commissionPlanList.stream().collect(Collectors.toMap(StarTaskCommissionPlanDO::getCommissionPlanId, Function.identity(), (v1, v2) -> v2));
        CommissionTypeEnum typeEnum = CommissionTypeEnum.getByValue(starTaskDO.getCommissionType());
        if (Objects.isNull(typeEnum)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("佣金类型异常");
        }
        // 校验是否参加过任务
        List<StarTaskApplyListDO> applyListDOS = starTaskApplyListDAO.findNoCancelApplyListByTaskId(starTaskDO.getTaskId());
        Map<String, List<StarTaskApplyListDO>> applyListMap = applyListDOS.stream().collect(Collectors.groupingBy(StarTaskApplyListDO::getRelationId));
        // 查看用户
        for (ConfirmApplyListParam confirmApplyListParam : param.getList()) {
            if (CollectionUtil.isNotEmpty(applyListMap.get(confirmApplyListParam.getRelationId()))) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("不能重复参加任务");
            }
            // 佣金任务
            StarTaskCommissionPlanDO starTaskCommissionPlanDO = planDOMap.get(confirmApplyListParam.getCommissionPlanId());
            if (Objects.isNull(starTaskCommissionPlanDO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务信息异常");
            }
            // 账号信息
            StarAccountResultDTO resultDTO = accountResultDTOMap.get(confirmApplyListParam.getRelationId());
            if (Objects.isNull(resultDTO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("发布账号有变动，请重新选择");
            }
            // 判断粉丝数是否适合
            if (resultDTO.getFanNumber() < starTaskDO.getFansNumber()) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("发布账号粉丝数有变动，无法报名");
            }
            // 校验是否符合任务要求
            switch (typeEnum) {
                case FIXED_AMOUNT:
                    if (resultDTO.getStarLevel() < starTaskCommissionPlanDO.getStarLevelLimit()) {
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("发布账号等级有变动，无法报名");
                    }
                    break;
                case LEVEL_STEP:
                    if (!resultDTO.getStarLevel().equals(starTaskCommissionPlanDO.getStarLevelLimit())) {
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("发布账号等级有变动，无法报名");
                    }
                    break;
                default:
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("佣金类型异常");
            }
            // 人数统计
            StarTaskCommissionPlanDO commissionPlanDO = map.getOrDefault(confirmApplyListParam.getCommissionPlanId(), starTaskCommissionPlanDO);
            int participationCount = commissionPlanDO.getParticipationCount() + 1;
            if (participationCount > commissionPlanDO.getNeededCount()) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("当前等级报名人数已满");
            }
            if (CommonConstant.INTEGER_ONE.equals(starTaskDO.getTaskType())) {
                String starId = resultDTO.getStarId();
                String redisKey = StrUtil.format(RedisPrefixConstant.STAR_DAILY_APPLY_NUMBER, starId);
                RAtomicLong atomicLong = redissonClient.getAtomicLong(redisKey);
                if (atomicLong.get() >= sysConfig.getStarDailyApplyLimit()) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("同一发布账号单日最多可接5条扫码直发任务");
                }
                redisKeyList.add(redisKey);
            }
            commissionPlanDO.setParticipationCount(participationCount);
            map.put(confirmApplyListParam.getCommissionPlanId(), commissionPlanDO);
            // 非实探任务 人数统计
            if (!StarTaskTypeEnum.OFFLINE_STORE_EXPLORATION.getType().equals(starTaskDO.getTaskType())) {
                if (participationCount > commissionPlanDO.getNeededCount()) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("当前等级报名人数已满");
                }
                commissionPlanDO.setParticipationCount(participationCount);
                map.put(confirmApplyListParam.getCommissionPlanId(), commissionPlanDO);
            }


            StarTaskApplyListDO applyListDO = new StarTaskApplyListDO();
            applyListDO.setApplyId(commonService.buildIncr());
            applyListDO.setUserId(userId);
            applyListDO.setIdentityId(identityId);
            applyListDO.setRelationId(confirmApplyListParam.getRelationId());
            applyListDO.setTaskId(taskId);
            applyListDO.setCommissionPlanId(confirmApplyListParam.getCommissionPlanId());
            applyListDO.setTaskMoney(confirmApplyListParam.getTaskMoney());
            //待提交状态
            applyListDO.setApplyStatus(ApplyListStatusEnum.WAITING_SUBMIT.getValue());
            //如果为实探任务，修改为商家前置待审核
            if (StarTaskTypeEnum.OFFLINE_STORE_EXPLORATION.getType().equals(starTaskDO.getTaskType())) {
                applyListDO.setApplyStatus(ApplyListStatusEnum.MERCHANT_PRE_AUDIT.getValue());
            }
            applyListDO.setTotalAmount(starTaskCommissionPlanDO.getTotalAmount());
            list.add(applyListDO);
        }
        return sum;
    }

    @NotNull
    private StarTaskDO getStarTaskDO(String taskId) {
        StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(taskId);
        if (Objects.isNull(starTaskDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务信息查询为空");
        }
        if (QuotaFullEnum.IS_FULL.getValue().equals(starTaskDO.getIsQuotaFull())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("名额已满,请选择其它任务");
        }
        // 判断是否已经到了报名时间
        if (!TaskStatusEnum.IN_PROGRESS.getValue().equals(starTaskDO.getTaskStatus())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务状态发生改变，无法报名任务");
        }
        if (BooleanEnum.YES.getValue().equals(starTaskDO.getRecruitmentCycle())) {
            Date date = new Date();
            if (date.after(starTaskDO.getApplyEndTime()) || date.before(starTaskDO.getApplyStartTime())) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("没有在正确的报名时间报名");
            }
        }
        //判断任务是否提前结束报名
        if (BooleanEnum.YES.getValue().equals(starTaskDO.getAheadEndApplyFlag())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务已提前结束报名");
        }
        return starTaskDO;
    }


    /**
     * 构建佣金计划
     *
     * @param listModel
     * @param commissionPlanDOList
     */
    private void buildCommissionPlan(FindTaskPlazaListModel listModel, List<StarTaskCommissionPlanDO> commissionPlanDOList) {
        Integer totalParticipationCount = CommonConstant.ZERO;
        Integer totalNeededCount = CommonConstant.ZERO;
        List<Integer> starLevelLimitList = Lists.newArrayList();
        for (StarTaskCommissionPlanDO starTaskCommissionPlanDO : commissionPlanDOList) {
            totalParticipationCount += starTaskCommissionPlanDO.getParticipationCount();
            starLevelLimitList.add(starTaskCommissionPlanDO.getStarLevelLimit());
            totalNeededCount += starTaskCommissionPlanDO.getNeededCount();
        }
        listModel.setTotalParticipationCount(totalParticipationCount);
        listModel.setTotalNeededCount(totalNeededCount);
        listModel.setStarLevelLimitList(starLevelLimitList);
    }

    @NotNull
    private PageParam<FindTaskPlazaListParamDTO> getFindTaskPlazaListParamDTOPageParam(PageParam<FindTaskPlazaListParam> param) {
        FindTaskPlazaListParam query = param.getQuery();
        PageParam<FindTaskPlazaListParamDTO> pageParam = new PageParam<>();
        pageParam.setPage(param.getPage());
        pageParam.setPageSize(param.getPageSize());
        FindTaskPlazaListParamDTO paramDTO = starTaskStarMinaServiceObjMapper.toFindTaskPlazaListParamDTO(query);
        String city = query.getCity();
        if (StringUtils.isNotBlank(city)) {
            AilikeGaodeCodeDO codeInfo = ailikeGaodeCodeDAO.getCodeInfo(city);
            paramDTO.setProvince(null == codeInfo ? null : codeInfo.getProvinceAdcode());
        }
        pageParam.setQuery(paramDTO);
        return pageParam;
    }
}