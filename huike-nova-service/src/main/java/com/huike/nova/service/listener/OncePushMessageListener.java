/*
 * ailike.com
 * Copyright (C) 2022-2023 All Rights Reserved.
 */
package com.huike.nova.service.listener;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.huike.nova.common.enums.takeout.TakeoutOrderStatusEnum;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.dao.entity.TakeoutOrderDO;
import com.huike.nova.dao.repository.TakeoutOrderDAO;
import com.huike.nova.service.business.PushService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version CyclePushMessageListener.java, v 0.1 2023-02-01 16:55 zhangling
 */
@Slf4j
@Component
public class OncePushMessageListener implements MessageListener {

    @Autowired
    private TakeoutOrderDAO takeoutOrderDAO;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PushService pushService;

    /**
     * 消费消息接口，由应用来实现<br>
     * 网络抖动等不稳定的情形可能会带来消息重复，对重复消息敏感的业务可对消息做幂等处理
     *
     * @param message 消息
     * @param context 消费上下文
     * @return 消费结果，如果应用抛出异常或者返回Null等价于返回Action.ReconsumeLater
     * @see <a href="https://help.aliyun.com/document_detail/44397.html">如何做到消费幂等</a>
     */
    @Override
    public Action consume(Message message, ConsumeContext context) {
        LogUtil.info(log, "用户下单推送 >> 消息消费开始...");
        String jsonMessage = null;
        jsonMessage = new String(message.getBody(), StandardCharsets.UTF_8);
        if (StringUtils.isEmpty(jsonMessage)) {
            LogUtil.info(log, "博实结播报处理:接收到的消息为空！message={}", message);
            return Action.CommitMessage;
        }
        JSONObject jsonObject = JSONObject.parseObject(jsonMessage);
        // 拿到订单号
        String orderSn = jsonObject.getString("orderSn");
        LogUtil.info(log, "用户下单推送 >> 订单号 >> orderSn = {}", orderSn);
        TakeoutOrderDO takeoutOrderDO = takeoutOrderDAO.getTakeoutOrderInfo(orderSn);
        if (Objects.isNull(takeoutOrderDO)) {
            LogUtil.info(log, "用户下单推送 >> 订单不存在 >> orderSn = {}", orderSn);
            return Action.CommitMessage;
        }
        //已处理的订单不播报
        Integer orderStatus = takeoutOrderDO.getOrderStatus();
        if (!TakeoutOrderStatusEnum.WAITING_ORDER.getValue().equals(orderStatus)) {
            LogUtil.info(log, "用户下单推送 >> 订单已处理，不播报 >> orderSn = {}", orderSn);
            return Action.CommitMessage;
        }
        pushService.onceCyclePushVoice(orderSn);
        // 支付成功的订单进行推送
        return Action.CommitMessage;
    }
}