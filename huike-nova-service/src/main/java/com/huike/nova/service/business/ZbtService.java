package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.zbt.CheckConnectModel;
import com.huike.nova.service.domain.model.zbt.CheckPermissionsModel;
import com.huike.nova.service.domain.model.zbt.FindProductListModel;
import com.huike.nova.service.domain.model.zbt.GetHomeDataModel;
import com.huike.nova.service.domain.model.zbt.GetZbtHelperLiveDataModel;
import com.huike.nova.service.domain.model.zbt.LiveRecordDetailModel;
import com.huike.nova.service.domain.model.zbt.PageLiveRecordCommentModel;
import com.huike.nova.service.domain.model.zbt.PageLiveRecordModel;
import com.huike.nova.service.domain.model.zbt.StoreCommentListModel;
import com.huike.nova.service.domain.param.zbt.CheckConnectParam;
import com.huike.nova.service.domain.param.zbt.CheckPermissionsParam;
import com.huike.nova.service.domain.param.zbt.CloseZbtParam;
import com.huike.nova.service.domain.param.zbt.FindProductListParam;
import com.huike.nova.service.domain.param.zbt.GetHomeDataParam;
import com.huike.nova.service.domain.param.zbt.GetZbtHelperLiveDataParam;
import com.huike.nova.service.domain.param.zbt.LiveRecordCommentOperateParam;
import com.huike.nova.service.domain.param.zbt.OpenZbtParam;
import com.huike.nova.service.domain.param.zbt.PageLiveRecordCommentParam;
import com.huike.nova.service.domain.param.zbt.PageLiveRecordParam;
import com.huike.nova.service.domain.param.zbt.StoreCommentListParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年08月29日 11:06
 */
public interface ZbtService {

    /**
     * 检查是否有权限
     *
     * @param param 入参
     * @return 出参
     */
    CheckPermissionsModel checkPermissions(CheckPermissionsParam param);

    /**
     * 获取首页数据
     *
     * @param param 入参
     * @return 出参
     */
    GetHomeDataModel getHomeData(GetHomeDataParam param);

    /**
     * 开启智播通
     *
     * @param param 入参
     */
    void openZbt(OpenZbtParam param);

    /**
     * 关闭智播通
     *
     * @param param 入参
     */
    void closeZbt(CloseZbtParam param);

    /**
     * 根据门店id获取accountId
     *
     * @param storeId 门店id
     * @return accountId
     */
    String getAccountIdByStoreId(String storeId);

    /**
     * 根据门店id获取直播状态
     *
     * @param storeId 门店id
     * @return 直播状态
     */
    Integer getLiveStatusByStoreId(String storeId);

    /**
     * 获取智播通助手直播数据
     *
     * @param param 入参
     * @return 出参
     */
    GetZbtHelperLiveDataModel getZbtHelperLiveData(GetZbtHelperLiveDataParam param);

    /**
     * 检查是否连接
     *
     * @param param 入参
     * @return 出参
     */
    CheckConnectModel checkConnect(CheckConnectParam param);

    /**
     * 获取直播记录列表
     *
     * @param param 入参
     * @return 出参
     */
    PageResult<PageLiveRecordModel> pageLiveRecord(PageParam<PageLiveRecordParam> param);

    /**
     * 获取直播记录详情
     *
     * @param roomId 房间id
     * @return 出参
     */
    LiveRecordDetailModel liveRecordDetail(String roomId);

    /**
     * 直播记录-评论分页列表
     *
     * @param param 入参
     * @return 出参
     */
    PageResult<PageLiveRecordCommentModel> pageLiveRecordComment(PageParam<PageLiveRecordCommentParam> param);

    /**
     * 直播记录-评论操作
     *
     * @param param 入参
     */
    void liveRecordCommentOperate(LiveRecordCommentOperateParam param);

    /**
     * 门店收录评论分页列表
     *
     * @param param 入参
     * @return 出参
     */
    PageResult<StoreCommentListModel> pageStoreCommentList(PageParam<StoreCommentListParam> param);

    /**
     * 智播通口播数据清洗
     */
    void zbtSoundDataCleaning();

    /**
     * 查询商品列表
     *
     * @param param 请求参数
     * @return 返参
     */
    List<FindProductListModel> findProductList(FindProductListParam param);
}
