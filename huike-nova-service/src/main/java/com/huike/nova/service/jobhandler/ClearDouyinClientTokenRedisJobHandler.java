package com.huike.nova.service.jobhandler;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Splitter;
import com.huike.nova.service.business.qyk.mina.QykToolsService;
import com.huike.nova.service.domain.param.qyk.mina.tools.QykToolsRemoveClientTokenRedisKeysParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 清空抖音ClientTokenJobHandler
 *
 * <AUTHOR> (<EMAIL>)
 * @version clearDouyinClientTokenRedisJobHandler.java, v1.0 2025-02-12 10:02 John Exp$
 */
@Component
@Slf4j
@JobHandler("clearDouyinClientTokenRedisJobHandler")
@AllArgsConstructor
public class ClearDouyinClientTokenRedisJobHandler extends AbsJobHandler {

    private QykToolsService qykToolsService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        if (StringUtils.isBlank(s)) {
            XxlJobLogger.log("ClearDouyinClientTokenJobHandler.execute >> 未指定通道..");
            return ReturnT.SUCCESS;
        }
        List<String> channelCodes = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(s);
        QykToolsRemoveClientTokenRedisKeysParam param = new QykToolsRemoveClientTokenRedisKeysParam();
        param.setChannelCodes(channelCodes);
        JSONArray result = qykToolsService.removeClientTokenRedisKeys(param);
        XxlJobLogger.log("脚本执行完毕, 执行结果: {}", result);
        return ReturnT.SUCCESS;
    }
}
