package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.service.business.TaskService;
import com.huike.nova.service.domain.param.qyk.mina.tools.MerchantOrderSyncJobParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

/**
 * 抖音销售订单同步
 *
 * <AUTHOR> (<EMAIL>)
 * @version DouyinSalesOrderSynchronizeJobHandler.java, v1.0 10/10/2024 15:00 John Exp$
 */
@Component
@Slf4j
@JobHandler("douyinSalesOrderSynchronizeJobHandler")
@AllArgsConstructor
public class DouyinSalesOrderSynchronizeJobHandler extends IJobHandler {

    private TaskService taskService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        String traceId = MDC.get(CommonConstant.TRACE_ID);
        XxlJobLogger.log("抖音闭环订单同步开始：请求参数:{}, traceId:{}", s, traceId);
        LogUtil.warn(log, "DouyinSalesOrderSynchronizeJobHandler.execute >> 抖音闭环订单同步开始, 请求参数: ", s);
        // 对请求参数进行解析
        try {
            // 自动同步抖音订单
            taskService.autoSyncDouyinOrders(true);
        } catch (Exception e) {
            LogUtil.warn(log, "DouyinSalesOrderSynchronizeJobHandler.execute >> 闭环订单同步失败, 错误: ", e);
            XxlJobLogger.log("抖音闭环订单同步失败: {}", e.getMessage());
            return ReturnT.FAIL;
        }
        XxlJobLogger.log("抖音闭环订单同步结束：time = {}", DateUtil.now());
        LogUtil.warn(log, "DouyinSalesOrderSynchronizeJobHandler.execute >> 抖音闭环订单同步结束, time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}
