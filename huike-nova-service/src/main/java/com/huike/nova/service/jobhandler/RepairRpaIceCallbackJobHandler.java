package com.huike.nova.service.jobhandler;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.service.business.IceService;
import com.huike.nova.service.domain.param.sundry.RepairIceCallbackParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 修复RPA ICE回调
 *
 * <AUTHOR> (<EMAIL>)
 * @version RepairRpaIceCallbackJobHandler.java, v1.0 12/22/2023 22:58 John Exp$
 */
@Component
@Slf4j
@JobHandler("repairRpaIceCallbackJobHandler")
@AllArgsConstructor
public class RepairRpaIceCallbackJobHandler extends IJobHandler {

    private IceService iceService;

    private SysConfig sysConfig;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("开始修复RPA ICE回调..");
        RepairIceCallbackParam param = JSONObject.parseObject(s, RepairIceCallbackParam.class);
        if (Objects.isNull(param)) {
            param = new RepairIceCallbackParam();
        }
        if (StringUtils.isBlank(param.getCallbackUrl())) {
            param.setCallbackUrl(sysConfig.getToolsRepairIceCallbackUrl());
        }
        iceService.repairAllRpaIceCallback(param);
        XxlJobLogger.log("开始修复RPA ICE回调完成..");
        return ReturnT.SUCCESS;
    }
}
