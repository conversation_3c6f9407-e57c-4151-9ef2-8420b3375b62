/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler.startask;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.startask.common.StarTaskJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version UnbindRepeatStarJobHandler.java, v 0.1 2024-02-19 11:45 AM ruanzy
 */
@Component
@Slf4j
@JobHandler("unbindRepeatStarJobHandler")
@AllArgsConstructor
public class UnbindRepeatStarJobHandler extends IJobHandler {

    private StarTaskJobService starTaskJobService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("UnbindRepeatStarJobHandler.execute >> 解绑重复达人脚本执行开始：time = {}", DateUtil.now());
        starTaskJobService.unbindRepeatStar();
        XxlJobLogger.log("UnbindRepeatStarJobHandler.execute >> 解绑重复达人脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}