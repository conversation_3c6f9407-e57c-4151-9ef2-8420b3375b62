/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb;

import com.huike.nova.service.domain.model.startask.agentweb.login.StarTaskAgentWebLoginModel;
import com.huike.nova.service.domain.param.startask.agentweb.login.AgentWebOneClickLoginParam;
import com.huike.nova.service.domain.param.startask.agentweb.login.StarTaskAgentSendCaptchaParam;
import com.huike.nova.service.domain.param.startask.agentweb.login.StarTaskAgentWebLoginParam;

/**
 * <AUTHOR>
 * @version AgentWebLoginService.java, v 0.1 2024-05-20 10:43 AM ruanzy
 */
public interface AgentWebLoginService {

    /**
     * 登录
     *
     * @param request
     * @return
     */
    StarTaskAgentWebLoginModel login(StarTaskAgentWebLoginParam request);

    /**
     * 退出登录
     */
    void logout();

    /**
     * 发送验证码
     *
     * @param param
     */
    void sendCaptcha(StarTaskAgentSendCaptchaParam param);


    /**
     * 一键登录
     *
     * @param param
     * @return
     */
    StarTaskAgentWebLoginModel oneClickLogin(AgentWebOneClickLoginParam param);
}