package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.storematerial.MaterialGroupCountStatisticsModel;
import com.huike.nova.service.domain.model.storematerial.StoreMaterialListModel;
import com.huike.nova.service.domain.model.storematerialgroup.QueryStoreMaterialGroupModel;
import com.huike.nova.service.domain.model.storematerialgroup.UpdateMaterialGroupSortParam;
import com.huike.nova.service.domain.param.storematerial.AddMaterialImageParam;
import com.huike.nova.service.domain.param.storematerial.AddMaterialParam;
import com.huike.nova.service.domain.param.storematerial.BatchUpdateMaterialGroupParam;
import com.huike.nova.service.domain.param.storematerial.DeleteMaterialParam;
import com.huike.nova.service.domain.param.storematerial.MaterialGroupCountStatisticsParam;
import com.huike.nova.service.domain.param.storematerial.SliceMaterialParam;
import com.huike.nova.service.domain.param.storematerial.StoreMaterialListParam;
import com.huike.nova.service.domain.param.storematerial.UpdateMaterialParam;
import com.huike.nova.service.domain.param.storematerialgroup.AddStoreMaterialGroupParam;
import com.huike.nova.service.domain.param.storematerialgroup.DeleteStoreMaterialGroupParam;
import com.huike.nova.service.domain.param.storematerialgroup.QueryStoreMaterialGroupParam;
import com.huike.nova.service.domain.param.storematerialgroup.UpdateStoreMaterialGroupParam;

import java.util.List;

/**
 * 门店素材
 *
 * <AUTHOR>
 * @date 2023年03月02日 10:15
 */
public interface StoreMaterialService {

    /**
     * 门店素材分页列表
     *
     * @param param 入参
     * @return 出参
     */
    PageResult<StoreMaterialListModel> materialPageList(PageParam<StoreMaterialListParam> param);

    /**
     * 门店素材分组数量统计
     *
     * @param param 入参
     * @return 出参
     */
    MaterialGroupCountStatisticsModel materialGroupCountStatistics(MaterialGroupCountStatisticsParam param);

    /**
     * 修改门店素材分组
     *
     * @param param 入参
     * @return 出参
     */
    void batchUpdateMaterialGroup(BatchUpdateMaterialGroupParam param);

    /**
     * 修改门店素材
     *
     * @param param 入参
     * @return 出参
     */
    void updateMaterial(UpdateMaterialParam param);

    /**
     * 删除门店素材
     *
     * @param param 入参
     * @return 出参
     */
    void deleteMaterial(DeleteMaterialParam param);

    /**
     * 新增门店素材
     *
     * @param param 入参
     * @return 出参
     */
    void addMaterial(AddMaterialParam param);

    /**
     * 切片门店素材
     *
     * @param param 入参
     * @return 出参
     */
    void sliceMaterial(SliceMaterialParam param);

    /**
     * 查询门店素材分组列表
     *
     * @param param
     * @return {@link List<QueryStoreMaterialGroupModel>}
     * <AUTHOR>
     */
    List<QueryStoreMaterialGroupModel> findMaterialGroupList(QueryStoreMaterialGroupParam param);

    /**
     * 删除门店素材分组
     *
     * @param param
     * <AUTHOR>
     */
    void deleteMaterialGroup(DeleteStoreMaterialGroupParam param);

    /**
     * 新增门店素材分组
     *
     * @param param
     * <AUTHOR>
     */
    void addMaterialGroup(AddStoreMaterialGroupParam param);

    /**
     * 修改门店素材分组名称
     *
     * @param param
     * <AUTHOR>
     */
    void updateMaterialGroup(UpdateStoreMaterialGroupParam param);

    /**
     * 修改素材组排序
     *
     * @param params
     * <AUTHOR>
     */
    void updateMaterialGroupSort(List<UpdateMaterialGroupSortParam> params);

    /**
     * 新增门店图片素材
     *
     * @param param
     */
    void addMaterialImage(AddMaterialImageParam param);
}
