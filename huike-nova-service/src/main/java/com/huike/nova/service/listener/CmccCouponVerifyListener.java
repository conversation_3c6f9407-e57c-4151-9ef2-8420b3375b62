package com.huike.nova.service.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.entity.AilikeServiceProviderCouponVerifyDO;
import com.huike.nova.dao.entity.QykAppUserServiceCardDO;
import com.huike.nova.dao.repository.AilikeServiceProviderCouponVerifyDAO;
import com.huike.nova.dao.repository.QykAppUserServiceCardDAO;
import com.huike.nova.service.business.CmccNoticeService;
import com.huike.nova.service.business.common.DingDingCommonService;
import com.huike.nova.service.business.qyk.mina.QykMinaCardService;
import com.huike.nova.service.domain.model.mina.cmcc.CmccCreateOrderModel;
import com.huike.nova.service.domain.param.qyk.mina.card.ActivateCardParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j(topic = "mq")
@Component
@AllArgsConstructor
public class CmccCouponVerifyListener implements MessageListener {
    private final QykAppUserServiceCardDAO qykAppUserServiceCardDAO;
    private final QykMinaCardService qykMinaCardService;
    private final RedissonClient redissonClient;
    private final AilikeServiceProviderCouponVerifyDAO ailikeServiceProviderCouponVerifyDAO;
    private final CmccNoticeService cmccNoticeService;
    private final DingDingCommonService dingDingCommonService;
    private final SysConfig sysConfig;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {

        LogUtil.info(log, "CmccCouponVerifyListener.consume >> 消费开始 >> message = {}", message);
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        CmccCreateOrderModel orderModel = JSON.parseObject(body, CmccCreateOrderModel.class);
        LogUtil.info(log, "ZbtLiveListener.consume >> orderModel内容 >> orderModel = {},body = {}", orderModel, body);
        if (null == orderModel) {
            LogUtil.info(log, "ZbtLiveListener.consume >> mq推送消息为空");
            return Action.CommitMessage;
        }

        // 加锁
        RLock redisLock = redissonClient.getLock(StrUtil.format(RedisPrefixConstant.LOCK_CMCC_COUPON_VERIFY_KEY, orderModel.getOrderNo()));
        try {
            if (!redisLock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage("操作频繁，请稍后再试");
            }
            List<AilikeServiceProviderCouponVerifyDO> couponVerifyDOList = ailikeServiceProviderCouponVerifyDAO.queryByOrderId(orderModel.getOrderNo());
            if (CollectionUtil.isNotEmpty(couponVerifyDOList)) {
                LogUtil.info(log, "CmccCouponVerifyListener.consume >> 优惠券已核销");
                return Action.CommitMessage;
            }


            List<CmccCreateOrderModel.CouponInfo> couponInfoList = orderModel.getCouponInfos();
            // 获取CouponList
            List<String> couponList = couponInfoList.stream().map(CmccCreateOrderModel.CouponInfo::getCoupon).collect(Collectors.toList());
            List<QykAppUserServiceCardDO> couponCodeList = qykAppUserServiceCardDAO.getServiceCardByCouponCodeList(couponList);
            Map<String, String> couponCodeMap = couponCodeList.stream().collect(Collectors.toMap(QykAppUserServiceCardDO::getCouponCode, QykAppUserServiceCardDO::getServiceCardId));
            for (CmccCreateOrderModel.CouponInfo couponInfo : couponInfoList) {
                ActivateCardParam activateCardParam = new ActivateCardParam();
                String serviceCardId = couponCodeMap.get(couponInfo.getCoupon());
                if (serviceCardId == null) {
                    LogUtil.info(log, " >> receiveOrderNotice >> serviceCardId 未找到 >> couponCodeMap = {},couponInfo={}", couponCodeMap, couponInfo);
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("serviceCardId 未找到");
                }
                activateCardParam.setServiceCardId(serviceCardId);
                activateCardParam.setCouponCode(couponInfo.getCoupon());
                // activateCardParam.setUserId();
                // activateCardParam.setLongitude();
                // activateCardParam.setLatitude();
                // 核销商圈卡
                LogUtil.info(log, "CmccCouponVerifyListener.consume >> 核销商圈卡 >> activateCardParam = {}", activateCardParam);
                qykMinaCardService.activateCard(activateCardParam);
            }

            // 回调移动
            cmccNoticeService.notice(orderModel.getOrderNo(), orderModel.getNotifyUrl());


            LogUtil.info(log, "CmccCouponVerifyListener.consume >> 优惠券核销成功");
            return Action.CommitMessage;


        } catch (Exception e) {
            LogUtil.error(log, "CmccCouponVerifyListener.consume >> 优惠券核销异常, param={}", e, orderModel);
            dingDingCommonService.sendCommonMessage(
                    "南山优惠券核销异常",
                    "南山优惠券核销异常",
                    JSON.toJSONString(orderModel),
                    sysConfig.getCmccRobotToken()
            );
            throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage("移动核销异常");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (redisLock != null && redisLock.isLocked() && redisLock.isHeldByCurrentThread()) {
                    redisLock.unlock();
                }
            } catch (Exception e) {
                LogUtil.error(log, "CmccCouponVerifyListener.consume >> 锁释放异常");
            }
        }


    }
}
