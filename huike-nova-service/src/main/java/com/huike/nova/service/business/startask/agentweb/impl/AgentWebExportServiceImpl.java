/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb.impl;

import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.startask.web.OperateExportTaskStatusEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.dao.domain.param.startask.WebExportListParamDTO;
import com.huike.nova.dao.repository.StarTaskOcExportTaskDAO;
import com.huike.nova.service.business.startask.agentweb.AgentWebExportService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.web.StarTaskWebExportServiceObjMapper;
import com.huike.nova.service.domain.model.startask.web.export.StarTaskExportTaskModel;
import com.huike.nova.service.domain.param.startask.web.export.StarTaskWebExportTaskListParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version AgentWebExportServiceImpl.java, v 0.1 2024-05-22 5:40 PM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class AgentWebExportServiceImpl implements AgentWebExportService {

    private StarTaskOcExportTaskDAO starTaskOcExportTaskDAO;

    private StarTaskWebExportServiceObjMapper starTaskWebExportServiceObjMapper;


    /**
     * 分页请求导出记录
     *
     * @param param 请求参数
     * @return 分页列表导出
     */
    @Override
    public PageResult<StarTaskExportTaskModel> list(PageParam<StarTaskWebExportTaskListParam> param) {
        if (Objects.isNull(param)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("参数不能为空");
        }
        // 获取登录态
        val loginModel = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        PageParam<WebExportListParamDTO> pageParam = new PageParam<>();
        pageParam.setPage(param.getPage());
        pageParam.setPageSize(param.getPageSize());
        val query = new WebExportListParamDTO()
                .setBusinessType(param.getQuery().getBusinessType())
                .setUserId(loginModel.getAgentId());
        pageParam.setQuery(query);
        val dataRecords = starTaskOcExportTaskDAO.queryPageList(pageParam);
        val list = starTaskWebExportServiceObjMapper.toStarTaskOcExportTaskListPageResponse(dataRecords);
        list.getRecords().forEach(records -> {
            records.setTaskStatusName(OperateExportTaskStatusEnum.getByValue(records.getTaskStatus()).getDesc());
            records.setCanDelete(!Objects.equals(records.getTaskStatus(), OperateExportTaskStatusEnum.PROCESSING.getTaskStatus()));
        });
        return list;
    }

    /**
     * 删除任务
     *
     * @param taskId 任务Id
     */
    @Override
    public void deleteTask(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("taskId不能为空");
        }
        val userId = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        val starTaskOcExportTaskDO = starTaskOcExportTaskDAO.queryByTaskIdAndBusinessType(userId.getAgentId(), taskId);
        if (Objects.isNull(starTaskOcExportTaskDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("未找到用户下的任务Id");
        }
        // 删除对应任务
        starTaskOcExportTaskDAO.deleteTaskRecord(taskId);
    }


}