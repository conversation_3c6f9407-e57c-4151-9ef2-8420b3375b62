/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.common;

import com.huike.nova.dao.entity.StarTaskApplyListDO;
import com.huike.nova.dao.entity.StarTaskDO;
import com.huike.nova.dao.entity.StarTaskIdentityRelationDO;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version StarTaskCommonService.java, v 0.1 2023-12-13 2:42 PM ruanzy
 */
public interface StarTaskCommonService {

    /**
     * 更新达人任务金(使用此方法外面需要添加事物和锁)
     *
     * @param identityId
     * @param applyList
     * @param starTaskDO
     * @param operatorId
     * @param operatorName
     */
    void updateTaskMoney(String identityId, StarTaskApplyListDO applyList, StarTaskDO starTaskDO, String operatorId, String operatorName);

    /**
     * 取消任务退款到余额
     *
     * @param starTaskDO
     */
    void cancelTaskToRefund(StarTaskDO starTaskDO);

    /**
     * 退款给商家
     *
     * @param starTaskDO
     * @param amount
     * @param prefix
     */
    void refundToMerchant(StarTaskDO starTaskDO, BigDecimal amount, String prefix);

    /**
     * 手动取消任务需要修改
     *
     * @param taskId        任务id
     * @param operationType 操作类型 1任务截止 2商家结束
     */
    void updateStatusToCanceled(String taskId, Integer operationType);

    /**
     * 构建团长返还奖励
     *
     * @param applyList
     * @param starTaskDO
     * @param identityRelationDO
     */
    void buildStarReturnReward(StarTaskApplyListDO applyList, StarTaskDO starTaskDO, StarTaskIdentityRelationDO identityRelationDO);

    /**
     * 构建商家返还奖励
     *
     * @param starTaskDO
     * @param identityRelationDO
     * @param completeAmount
     * @return
     */
    BigDecimal buildMerchantReturnReward(StarTaskDO starTaskDO, StarTaskIdentityRelationDO identityRelationDO, BigDecimal completeAmount);
}