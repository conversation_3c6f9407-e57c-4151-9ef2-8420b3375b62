package com.huike.nova.service.jobhandler.red;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.mina.redMina.RedMinaRefundService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@JobHandler("RedExpiredAutomaticRefundJobHandler")
@AllArgsConstructor
public class RedExpiredAutomaticRefundJobHandler extends IJobHandler {

    private RedMinaRefundService redMinaRefundService;

    /**
     * 小红书过期自动退
     *
     * @param param 无
     * @return 结束
     * @throws Exception 异常信息
     */
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("RedExpiredAutomaticRefundJobHandler.execute >> 小红书过期自动退开始：time = {}", DateUtil.now());
        redMinaRefundService.expiredAutomaticRefund(param);
        XxlJobLogger.log("RedExpiredAutomaticRefundJobHandler.execute >> 小红书过期自动退结束：time = {}", DateUtil.now());

        return ReturnT.SUCCESS;
    }
}
