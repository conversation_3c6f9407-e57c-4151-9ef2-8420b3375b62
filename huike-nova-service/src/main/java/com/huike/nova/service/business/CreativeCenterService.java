package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.creativecenter.CreativeCenterCategoryModel;
import com.huike.nova.service.domain.model.creativecenter.CreativeListModel;
import com.huike.nova.service.domain.model.creativecenter.FindCreativeSceneListModel;
import com.huike.nova.service.domain.param.creativecenter.AddCreativeCenterCategoryParam;
import com.huike.nova.service.domain.param.creativecenter.AddCreativeParam;
import com.huike.nova.service.domain.param.creativecenter.AddCreativeSceneParam;
import com.huike.nova.service.domain.param.creativecenter.CreativeHeatIncreaseParam;
import com.huike.nova.service.domain.param.creativecenter.CreativeListParam;
import com.huike.nova.service.domain.param.creativecenter.DeleteCreativeCenterCategoryParam;
import com.huike.nova.service.domain.param.creativecenter.DeleteCreativeParam;
import com.huike.nova.service.domain.param.creativecenter.DeleteCreativeSceneParam;
import com.huike.nova.service.domain.param.creativecenter.FindCreativeSceneListParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年04月07日 13:56
 */
public interface CreativeCenterService {

    /**
     * 创意中心-类别列表
     *
     * @return CreativeCenterCategoryResponse
     */
    List<CreativeCenterCategoryModel> findCreativeCenterCategory();

    /**
     * 创意列表
     *
     * @param param 类别id
     * @return CreativeListModel
     */
    PageResult<CreativeListModel> findPageCreativeList(PageParam<CreativeListParam> param);

    /**
     * 创意详情
     *
     * @param param param
     * @return FindCreativeSceneListModel
     */
    FindCreativeSceneListModel findCreativeSceneList(FindCreativeSceneListParam param);

    /**
     * 增加创意热度
     *
     * @param param param
     */
    void creativeHeatIncrease(CreativeHeatIncreaseParam param);

    /**
     * 创意中心-类别列表
     *
     * @param param 入参
     */
    void addCreativeCenterCategory(AddCreativeCenterCategoryParam param);

    /**
     * 创意中心-删除类别
     *
     * @param param 入参
     */
    void deleteCreativeCenterCategory(DeleteCreativeCenterCategoryParam param);

    /**
     * 新增创意
     *
     * @param param Param
     */
    void addPageCreative(AddCreativeParam param);

    /**
     * 删除创意
     *
     * @param param 创意id
     */
    void deletePageCreative(DeleteCreativeParam param);

    /**
     * 新增创意场景信息
     *
     * @param param AddCreativeSceneParam
     */
    void addCreativeScene(AddCreativeSceneParam param);

    /**
     * 删除创意场景信息
     *
     * @param param param
     */
    void deleteCreativeScene(DeleteCreativeSceneParam param);
}
