/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.huike.nova.common.constant.startask.agentweb.StarTaskAgentWebRedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.FsDateUtils;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.param.startask.AgentDayStatisticsDataParamDTO;
import com.huike.nova.dao.domain.result.startask.AgentAreaResultDTO;
import com.huike.nova.dao.domain.result.startask.GetManageDataResultDTO;
import com.huike.nova.dao.entity.StarTaskAgentAccountDO;
import com.huike.nova.dao.entity.StarTaskAgentDO;
import com.huike.nova.dao.entity.StarTaskIdentityDO;
import com.huike.nova.dao.repository.StarTaskAgentAccountDAO;
import com.huike.nova.dao.repository.StarTaskAgentAreaDAO;
import com.huike.nova.dao.repository.StarTaskAgentDAO;
import com.huike.nova.dao.repository.StarTaskAgentDayStatisticsDAO;
import com.huike.nova.dao.repository.StarTaskIdentityDAO;
import com.huike.nova.service.business.startask.agentweb.AgentHomePageService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.agentweb.AgentHomePageServiceObjMapper;
import com.huike.nova.service.domain.mapper.startask.web.StarTaskWebAgentServiceObjMapper;
import com.huike.nova.service.domain.model.startask.AgentAreaModel;
import com.huike.nova.service.domain.model.startask.agentweb.home.GetBasicInfoModel;
import com.huike.nova.service.domain.model.startask.agentweb.home.GetManageDataModel;
import com.huike.nova.service.domain.model.startask.agentweb.home.GetProportionConfigModel;
import com.huike.nova.service.domain.model.startask.agentweb.login.StarTaskAgentWebLoginModel;
import com.huike.nova.service.domain.model.web.homepage.GetConfigurationModel;
import com.huike.nova.service.domain.param.startask.agentweb.home.GetManageDataParam;
import com.huike.nova.service.domain.param.startask.agentweb.home.UpdateProportionConfigParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version AgentHomePageServiceImpl.java, v 0.1 2024-05-20 4:38 PM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class AgentHomePageServiceImpl implements AgentHomePageService {

    private StarTaskAgentAreaDAO starTaskAgentAreaDAO;

    private StarTaskAgentDAO starTaskAgentDAO;

    private StarTaskAgentAccountDAO starTaskAgentAccountDAO;

    private StarTaskIdentityDAO starTaskIdentityDAO;

    private StarTaskAgentDayStatisticsDAO starTaskAgentDayStatisticsDAO;

    private StarTaskWebAgentServiceObjMapper starTaskWebAgentServiceObjMapper;

    private AgentHomePageServiceObjMapper agentHomePageServiceObjMapper;

    private SysConfig sysConfig;

    private RedissonClient redissonClient;


    /**
     * 基本信息获取
     *
     * @return
     */
    @Override
    public GetBasicInfoModel getBasicInfo() {
        // 登录态获取信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        String agentId = basicInfo.getAgentId();
        StarTaskAgentDO agentInfo = starTaskAgentDAO.getAgentInfo(agentId);
        if (Objects.isNull(agentInfo)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("信息查询异常");
        }
        // 查询代理商账号信息
        StarTaskAgentAccountDO agentAccount = starTaskAgentAccountDAO.getAgentAccount(agentId);
        if (Objects.isNull(agentAccount)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账号信息异常");
        }
        // 区域查询
        List<AgentAreaResultDTO> list = starTaskAgentAreaDAO.findAgentAreaDTOByAgentId(agentId);
        if (CollectionUtil.isEmpty(list)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("区域信息异常");
        }
        // 商家和达人数量查询
        List<StarTaskIdentityDO> identityList = starTaskIdentityDAO.findIdentityByCity(list.stream().map(AgentAreaResultDTO::getCity).collect(Collectors.toList()));
        Map<Integer, List<StarTaskIdentityDO>> listMap = identityList.stream().collect(Collectors.groupingBy(StarTaskIdentityDO::getIdentityType));
        GetBasicInfoModel model = new GetBasicInfoModel();
        model.setName(agentInfo.getName());
        model.setAvailableBalance(agentAccount.getAvailableBalance());
        model.setAccumulatedCommission(agentAccount.getAccumulatedCommission());
        model.setAccumulatedTaskAmount(agentAccount.getAccumulatedTaskAmount());
        model.setMerchantNumber(listMap.getOrDefault(StarTaskIdentityTypeEnum.MERCHANT.getValue(), Lists.newArrayList()).size());
        model.setStarNumber(listMap.getOrDefault(StarTaskIdentityTypeEnum.STAR.getValue(), Lists.newArrayList()).size());
        model.setAreaList(starTaskWebAgentServiceObjMapper.toAgentAreaModelList(list));
        model.setCreateTime((DateUtil.format(agentInfo.getCreateTime(), FsDateUtils.SIMPLE_DATE_FORMAT)));
        return model;
    }

    /**
     * 首页-更新日志和帮助中心
     *
     * @return
     */
    @Override
    public GetConfigurationModel getConfiguration() {
        String homePageConfiguration = sysConfig.getStarTaskAgentHomePageConfiguration();
        return JSONObject.parseObject(homePageConfiguration, GetConfigurationModel.class);
    }

    /**
     * 查询分成比例配置
     *
     * @return
     */
    @Override
    public GetProportionConfigModel getProportionConfig() {
        // 登录态获取信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        // 查询分成比例配置
        StarTaskAgentDO agentInfo = starTaskAgentDAO.getAgentInfo(basicInfo.getAgentId());
        if (Objects.isNull(agentInfo)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("信息查询异常");
        }
        BigDecimal agentRate = agentInfo.getAgentRate();
        BigDecimal merchantRate = agentInfo.getMerchantRate();
        BigDecimal starRate = agentInfo.getStarRate();
        // 构建返回参数
        GetProportionConfigModel model = new GetProportionConfigModel();
        model.setAgentRate(agentRate);
        model.setMerchantRate(merchantRate);
        model.setStarRate(starRate);
        // 预计比例 = 总分成 - 商家邀请人返还 - 达人团长返还
        model.setPredictRate(agentRate.subtract(merchantRate.add(starRate)));
        model.setMaxAgentRate(agentInfo.getMaxAgentRate());
        return model;
    }

    /**
     * 更新分成比例配置
     *
     * @param param
     */
    @Override
    public void updateProportionConfig(UpdateProportionConfigParam param) {
        BigDecimal agentRate = param.getAgentRate();
        BigDecimal merchantRate = param.getMerchantRate();
        BigDecimal starRate = param.getStarRate();
        // 登录态获取信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        // 查询分成比例配置
        StarTaskAgentDO agentInfo = starTaskAgentDAO.getAgentInfo(basicInfo.getAgentId());
        if (Objects.isNull(agentInfo)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("信息查询异常");
        }
        BigDecimal maxAgentRate = agentInfo.getMaxAgentRate();
        // 检查总分成不能大于代理商最高抽成比
        if (agentRate.compareTo(maxAgentRate) > 0) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("总分成比例超过代理商最高抽成比");
        }
        // 检查商家邀请人返还和达人团长返还之和是否超出总分成
        if (merchantRate.add(starRate).compareTo(agentRate) > 0) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("商家邀请人返还和达人团长返还之和超出总分成");
        }
        starTaskAgentDAO.updateProportionConfig(basicInfo.getAgentId(), agentRate, merchantRate, starRate);
    }

    /**
     * 经营数据
     *
     * @param param
     * @return
     */
    @Override
    public GetManageDataModel getManageData(GetManageDataParam param) {
        // 登录态获取信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        // 查询经营数据
        AgentDayStatisticsDataParamDTO paramDTO = new AgentDayStatisticsDataParamDTO();
        paramDTO.setAgentId(basicInfo.getAgentId());
        paramDTO.setCityList(param.getCityList());
        // 时间转换
        paramDTO.setStartTime(FsDateUtils.dateToInteger(param.getStartTime()));
        paramDTO.setEndTime(FsDateUtils.dateToInteger(param.getEndTime()));
        GetManageDataResultDTO manageData = starTaskAgentDayStatisticsDAO.getManageData(paramDTO);
        // 构建返回参数
        GetManageDataModel model = agentHomePageServiceObjMapper.toGetManageDataModel(manageData);
        model.setUserReward(manageData.getMerchantReward().add(manageData.getStarReward()));
        // 缓存中获取最近更新时间
        RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(StarTaskAgentWebRedisPrefixConstant.STAR_TASK_AGENT_WEB_HOME_UPDATE_TIME));
        if (bucket.isExists()) {
            model.setUpdateTime(bucket.get());
        }
        return model;
    }

    /**
     * 根据类型查询的所有的城市列表
     *
     * @return
     */
    @Override
    public List<AgentAreaModel> findHistoryCityList(Integer type) {
        // 登录态获取信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        // 根据类型查询的所有的城市列表
        List<AgentAreaResultDTO> list = starTaskAgentAreaDAO.findHistoryCityList(basicInfo.getAgentId(), type);
        return agentHomePageServiceObjMapper.toAgentAreaModelList(list);
    }
}