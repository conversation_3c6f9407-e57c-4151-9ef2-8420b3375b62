package com.huike.nova.service.business.verifyTool;

import com.huike.nova.service.domain.model.verifyTool.GetMemberStatusModel;
import com.huike.nova.service.domain.model.verifyTool.MemberRegisterModel;
import com.huike.nova.service.domain.model.verifyTool.QueryVerifyStatusModel;
import com.huike.nova.service.domain.param.qyk.mina.card.SaveOrderParam;
import com.huike.nova.service.domain.param.verifyTool.GetMemberStatusParam;
import com.huike.nova.service.domain.param.verifyTool.MallCooAddPointsParam;
import com.huike.nova.service.domain.param.verifyTool.MemberRegisterParam;
import com.huike.nova.service.domain.param.verifyTool.PushServiceDoneParam;
import com.huike.nova.service.domain.param.verifyTool.QueryVerifyStatusParam;
import com.huike.nova.service.domain.param.verifyTool.VerifyNumberPhoneReportParam;
import com.huike.nova.service.domain.result.SaveOrderResult;

/**
 * <AUTHOR>
 */
public interface VerifyToolService {


    /**
     * 保存闭环商圈卡信息
     *
     * @param param
     * @return
     */
    SaveOrderResult saveCloseOrder(SaveOrderParam param);

    /**
     * 核销状态查询（轮询接口）
     *
     * @param param 请求参数
     */
    QueryVerifyStatusModel queryVerifyStatus(QueryVerifyStatusParam param);

    /**
     * 推送服务完成
     *
     * @param param 请求参数
     */
    void pushServiceDone(PushServiceDoneParam param);


    /**
     * 获取会员状态
     *
     * @param param 请求参数
     */
    GetMemberStatusModel getMemberStatus(GetMemberStatusParam param);

    /**
     * 会员注册
     *
     * @param param 请求参数
     */
    MemberRegisterModel memberRegister(MemberRegisterParam param);

    /**
     * 猫酷会员加积分
     *
     * @param mallCooAddPointsParam 请求参数
     */
    void mallCooAddPoints(MallCooAddPointsParam mallCooAddPointsParam);

    /**
     * 核销手机号上报
     *
     * @param param 请求参数
     */
    void verifyPhoneNumberReport(VerifyNumberPhoneReportParam param);

    /**
     * 设置小程序跳转path
     *
     * @param accountId 抖音来客 id
     */
    void updateMerchantPath(String accountId);
}
