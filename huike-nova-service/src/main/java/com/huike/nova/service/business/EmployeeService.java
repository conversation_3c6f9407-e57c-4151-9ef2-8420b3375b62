/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.employee.EmployeeDetailModel;
import com.huike.nova.service.domain.model.employee.EmployeeListModel;
import com.huike.nova.service.domain.param.employee.EmployeeAddParam;
import com.huike.nova.service.domain.param.employee.EmployeeDeleteParam;
import com.huike.nova.service.domain.param.employee.EmployeeDetailParam;
import com.huike.nova.service.domain.param.employee.EmployeeListParam;
import com.huike.nova.service.domain.param.employee.EmployeeUpdateParam;

/**
 * <AUTHOR>
 * @version EmployeeService.java, v 0.1 2022-10-08 9:42 AM ruanzy
 */
public interface EmployeeService {

    /**
     * 添加员工
     *
     * @param param
     */
    void addEmployee(EmployeeAddParam param);

    /**
     * 员工列表
     *
     * @param param
     * @return
     */
    PageResult<EmployeeListModel> findEmployeeList(PageParam<EmployeeListParam> param);

    /**
     * 查询员工详情
     *
     * @param param
     * @return
     */
    EmployeeDetailModel getEmployeeDetail(EmployeeDetailParam param);

    /**
     * 修改员工
     *
     * @param param
     */
    void updateEmployee(EmployeeUpdateParam param);

    /**
     * 删除员工
     *
     * @param param
     */
    void deleteEmployee(EmployeeDeleteParam param);
}