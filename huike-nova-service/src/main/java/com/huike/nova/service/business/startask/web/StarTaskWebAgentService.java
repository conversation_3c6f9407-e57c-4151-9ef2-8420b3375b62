/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.web;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.startask.web.agent.WebAgentDetailModel;
import com.huike.nova.service.domain.model.startask.web.agent.WebAgentListModel;
import com.huike.nova.service.domain.param.startask.web.agent.WebAgentAddParam;
import com.huike.nova.service.domain.param.startask.web.agent.WebAgentDetailParam;
import com.huike.nova.service.domain.param.startask.web.agent.WebAgentListParam;
import com.huike.nova.service.domain.param.startask.web.agent.WebAgentUpdateParam;
import com.huike.nova.service.domain.param.startask.web.agent.WebOperateAgentParam;


/**
 * <AUTHOR>
 * @version StarTaskWebAgentService.java, v 0.1 2024-05-15 9:38 AM ruanzy
 */
public interface StarTaskWebAgentService {

    /**
     * 新增区代
     *
     * @param param
     */
    void add(WebAgentAddParam param);

    /**
     * 修改区代
     *
     * @param param
     */
    void update(WebAgentUpdateParam param);

    /**
     * 区代详情
     *
     * @param param
     * @return
     */
    WebAgentDetailModel detail(WebAgentDetailParam param);

    /**
     * 区代列表
     *
     * @param param
     * @return
     */
    PageResult<WebAgentListModel> list(PageParam<WebAgentListParam> param);

    /**
     * 区代操作
     *
     * @param param
     */
    void operate(WebOperateAgentParam param);
}