package com.huike.nova.service.business.startask.web;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.dao.domain.param.startask.BalanceDetailsPageListParamDTO;
import com.huike.nova.dao.domain.param.startask.WebMerchantListParamDTO;
import com.huike.nova.service.domain.model.startask.web.star.BalanceDetailsPageListModel;
import com.huike.nova.service.domain.model.startask.web.star.WebStarListModel;
import com.huike.nova.service.domain.param.startask.web.star.BalanceDetailsPageListParam;
import com.huike.nova.service.domain.param.startask.web.star.StarAccountStatusOperationParam;
import com.huike.nova.service.domain.param.startask.web.star.WebStarListParam;
import io.reactivex.Observable;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2023年12月07日 16:40
 */
public interface StarTaskWebStarService {

    /**
     * 达人列表
     *
     * @param param 入参
     * @return 出参
     */
    PageResult<WebStarListModel> list(PageParam<WebStarListParam> param);

    /**
     * 达人列表导出
     *
     * @param param 导出参数
     */
    void requestExport(WebStarListParam param);

    /**
     * 收益明细
     *
     * @param param 入参
     * @return 出参
     */
    PageResult<BalanceDetailsPageListModel> balanceDetailsPageList(PageParam<BalanceDetailsPageListParam> param);

    /**
     * 收银明细导出
     *
     * @param param 入参
     */
    void requestExportBalanceDetailsList(BalanceDetailsPageListParam param);

    /**
     * 达人状态操作
     *
     * @param param 入参
     */
    void starAccountStatusOperation(StarAccountStatusOperationParam param);

    /**
     * 达人列表导出（异步任务）
     *
     * @param param 入参
     * @return 出参 RxJava文件对象
     */
    Observable<File> export(String fileName, WebMerchantListParamDTO param);

    /**
     * 收益明细导出（异步任务）
     *
     * @param param 入参
     * @return 出参 RxJava文件对象
     */
    Observable<File> exportBalanceDetailsList(String fileName, BalanceDetailsPageListParamDTO param);
}
