/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.common;

import com.huike.nova.dao.entity.StarTaskApplyListDO;

/**
 * <AUTHOR>
 * @version AsyncService.java, v 0.1 2023-12-12 5:56 PM ruanzy
 */
public interface StarTaskAsyncService {

    /**
     * 操作解绑
     *
     * @param relationId
     */
    void operateUnbind(String relationId);

    /**
     * 取消报名
     *
     * @param applyListDO
     */
    void cancelRegistration(StarTaskApplyListDO applyListDO);
}