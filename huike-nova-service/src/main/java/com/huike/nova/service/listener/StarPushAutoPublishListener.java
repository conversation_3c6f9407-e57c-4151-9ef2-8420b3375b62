package com.huike.nova.service.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.InterfaceTypeEnum;
import com.huike.nova.common.enums.SoundTypeEnum;
import com.huike.nova.common.enums.SupActivityPublishStatusEnum;
import com.huike.nova.common.enums.UseStatuEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.dao.entity.AilikeBActivityDO;
import com.huike.nova.dao.entity.AilikeBSupActivityDO;
import com.huike.nova.dao.entity.AilikeBVideoCartesianProductDO;
import com.huike.nova.dao.repository.AilikeBActivityDAO;
import com.huike.nova.dao.repository.AilikeBSupActivityDAO;
import com.huike.nova.dao.repository.AilikeBVideoCartesianProductDAO;
import com.huike.nova.service.business.VideoCheckService;
import com.huike.nova.service.domain.dto.rpa.StarPushAutoPublishDTO;
import com.huike.nova.service.domain.param.releasevideo.NewVideoSynthesisParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 达人推自动发布消费者
 *
 * <AUTHOR>
 * @version StarPushAutoPublishListener.java, v 0.1 2023/2/10 4:31 下午 mayucong
 */
@Slf4j
@Component
@AllArgsConstructor
public class StarPushAutoPublishListener implements MessageListener {

    private RedissonClient redissonClient;

    private AilikeBSupActivityDAO supActivityDAO;

    final VideoCheckService videoCheckService;

    private AilikeBActivityDAO bActivityDAO;

    private TransactionTemplate transactionTemplate;

    private AilikeBVideoCartesianProductDAO ailikeBVideoCartesianProductDAO;

    @Override
    public Action consume(Message message, ConsumeContext context) {
        String starPushAutoPublishDTOStr = new String(message.getBody(), StandardCharsets.UTF_8);
        LogUtil.info(log, "StarPushAutoPublishListener.consume >> 达人推自动发布消息处理开始, message = {}, content = {}", message.getMsgID(), starPushAutoPublishDTOStr);
        StarPushAutoPublishDTO starPushAutoPublishDTO = JSONObject.parseObject(starPushAutoPublishDTOStr, StarPushAutoPublishDTO.class);
        String supActivityId = starPushAutoPublishDTO.getSupActivityId();
        // 可发布的视频为空直接确认消息
        if (CollUtil.isEmpty(starPushAutoPublishDTO.getPreVideoList())) {
            LogUtil.info(log, "StarPushAutoPublishListener.consume >> 可发布的视频为空, supActivityId：{}", starPushAutoPublishDTO.getSupActivityId());
            return Action.CommitMessage;
        }
        LogUtil.info(log, "StarPushAutoPublishListener.consume >> 达人推自动发布, supActivityId：{}", starPushAutoPublishDTO.getSupActivityId());

        // 加锁
        String lockKey = StrUtil.format(RedisPrefixConstant.LOCK_STAR_PUSH_AUTO_PUBLISH, supActivityId);
        boolean fallback = false;
        try (RedisLockHelper lock = new RedisLockHelper(lockKey, redissonClient)) {
            lock.tryLockSec(CommonConstant.ONE_SECOND);
            // 查询活动信息
            AilikeBSupActivityDO oneBySupActivityId = supActivityDAO.getOneBySupActivityId(supActivityId);
            if (ObjectUtil.isNull(oneBySupActivityId)) {
                LogUtil.warn(log, "StarPushAutoPublishListener > 未找到对应达人推活动id, supActivityId={}", supActivityId);
                fallback = true;
                throw new CommonException(ErrorCodeEnum.ACTIVITY_PARAM_ERROR).detailMessage("未找到对应达人推活动id");
            }
            // 获得发布状态
            SupActivityPublishStatusEnum supActivityPublishStatusEnum = SupActivityPublishStatusEnum.getByValue(oneBySupActivityId.getPublishStatus());
            // 仅当状态为：未发布、视频不足时（且有待发视频时），才可进行发布
            if (supActivityPublishStatusEnum != SupActivityPublishStatusEnum.UN_PUBLISH && supActivityPublishStatusEnum != SupActivityPublishStatusEnum.INSUFFICIENT_VIDEO) {
                LogUtil.warn(log, "StarPushAutoPublishListener.consume >> 达人推活动状态不正确, 活动Id:{}, 当前状态:{}", supActivityId, supActivityPublishStatusEnum);
                // 回退占用的额度
                fallback = true;
                throw new CommonException(ErrorCodeEnum.ACTIVITY_PARAM_ERROR).detailMessage("活动状态不正确");
            }

            AilikeBActivityDO activityByStoreIdAndActivityId = bActivityDAO.queryByActivityId(oneBySupActivityId.getActivityId());
            // 视频合成校验
            SoundTypeEnum soundTypeEnum = SoundTypeEnum.getByValue(activityByStoreIdAndActivityId.getSoundType());
            NewVideoSynthesisParam newVideoSynthesisParam = NewVideoSynthesisParam.builder()
                    .activityId(oneBySupActivityId.getActivityId())
                    .interfaceType(InterfaceTypeEnum.RPA.getValue())
                    .supActivityId(oneBySupActivityId.getSupActivityId())
                    .activityDO(activityByStoreIdAndActivityId)
                    .soundTypeEnum(soundTypeEnum)
                    .starDataList(starPushAutoPublishDTO.getStarDataList())
                    .starPushRecordId(starPushAutoPublishDTO.getStarPushRecordId())
                    .starPushPresetRecordIdMap(starPushAutoPublishDTO.getPresetRecordIdMap())
                    .build();
            // 更改活动状态
            if (supActivityPublishStatusEnum == SupActivityPublishStatusEnum.UN_PUBLISH) {
                oneBySupActivityId.setPublishStatus(SupActivityPublishStatusEnum.PUBLISHING.getValue());
            }
            oneBySupActivityId.setUpdateTime(null);
            supActivityDAO.updateById(oneBySupActivityId);
            videoCheckService.videoSynthesisSound(newVideoSynthesisParam, starPushAutoPublishDTO.getPreVideoList());
        } catch (Exception ex) {
            LogUtil.warn(log, "StarPushAutoPublishListener.consume >> 达人推自动发布消息发生异常, supActivityId={}, err=", supActivityId, ex);
        }
        if (fallback) {
            fallbackCartesian(starPushAutoPublishDTO.getPreVideoList());
        }
        LogUtil.info(log, "consume >> StarPushAutoPublishListener >> 达人推自动发布消息处理结束 >> messageId = {}", message.getMsgID());
        return Action.CommitMessage;
    }

    /**
     * 回退笛卡尔表的标记位
     *
     * @param preVideoList 视频列表
     */
    private void fallbackCartesian(List<AilikeBVideoCartesianProductDO> preVideoList) {
        // 笛卡尔积视频素材预设组
        List<String> presetGroupIdList = preVideoList.stream().map(AilikeBVideoCartesianProductDO::getPresetGroupId).collect(Collectors.toList());
        try {
            ailikeBVideoCartesianProductDAO.updateUseStatusByPresetGroupIdList(presetGroupIdList, UseStatuEnum.NOT_USED);
        } catch (Exception ex) {
            LogUtil.warn(log, "StarPushAutoPublishListener.fallbackCartesian >> 达人推自动回退笛卡尔表为未使用错误, groupIds={}, err=", JSONObject.toJSONString(presetGroupIdList), ex);
        }
    }

    public void mockConsume(String message) {
        Message msg = new Message();
        msg.setBody(message.getBytes(StandardCharsets.UTF_8));
        consume(msg, null);
    }
}
