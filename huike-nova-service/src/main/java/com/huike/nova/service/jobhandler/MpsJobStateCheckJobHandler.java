package com.huike.nova.service.jobhandler;

import com.huike.nova.service.business.MediaTransCodeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * MPS结果查询回调
 *
 * <AUTHOR> (<EMAIL>)
 * @version MpsJobStateCheckJobHandler.java, v1.0 2024/4/3 23:51 John Exp$
 */
@Component
@Slf4j
@JobHandler("mpsJobStateCheckJobHandler")
@AllArgsConstructor
public class MpsJobStateCheckJobHandler extends IJobHandler {

    private MediaTransCodeService mediaTransCodeService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        mediaTransCodeService.checkSubmittedJobs();
        return ReturnT.SUCCESS;
    }
}