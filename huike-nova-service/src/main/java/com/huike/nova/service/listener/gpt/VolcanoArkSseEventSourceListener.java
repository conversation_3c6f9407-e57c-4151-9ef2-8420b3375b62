package com.huike.nova.service.listener.gpt;

import com.alibaba.fastjson.JSONObject;
import com.annimon.stream.function.Consumer;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.sdk.volcano.gpt.model.VolcanoArkResponse;
import com.huike.nova.service.enums.GPTServiceProviderEnum;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Objects;

/**
 * 火山方舟
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcanoArkSseEventSourceListener.java, v1.0 02/20/2024 14:56 John Exp$
 */
@Slf4j
public class VolcanoArkSseEventSourceListener extends AbsGptSseEventSourceListener {

    public VolcanoArkSseEventSourceListener(GPTServiceProviderEnum gptServiceProvider, SseEmitter sseEmitter, String contentId, String accountId, RAtomicLong atomicLong) {
        super(gptServiceProvider, sseEmitter, contentId, accountId, atomicLong);
    }

    @Override
    protected boolean processContent(String data, Consumer<String> processingDataHandler) {
        LogUtil.info(log, "VolcanoArkSseEventSourceListener.processContent >> SSE.{} >> contentId:{} data:{}", getGptServiceProvider(), contentId, data);
        // 判断结束标志位
        if (StringUtils.equalsIgnoreCase(DONE, data)) {
            return true;
        }
        // 转换为JSON对象
        val completionResponse = JSONObject.parseObject(data, VolcanoArkResponse.class);
        if (Objects.isNull(completionResponse))  {
            throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("三方返回数据为空");
        }
        // 结束标志位
        // 移除回车换行符号
        if (completionResponse.getChoice() == null || completionResponse.getChoice().getMessage() == null) {
            throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("三方返回回答数据为空");
        }
        val content = removeCRLF(completionResponse.getChoice().getMessage().getContent());
        if (StringUtils.isNotBlank(content)) {
            // 处理数据
            processingDataHandler.accept(content);
        }
        return StringUtils.equalsIgnoreCase(completionResponse.getChoice().getFinishReason(), STOP);
    }
}
