/**
 * ailike.com
 * Copyright (C) 2021-2023 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.alibaba.fastjson.JSONObject;
import com.huike.nova.service.domain.model.pay.AppreciationPreOrderModel;
import com.huike.nova.service.domain.model.pay.GetWechatUserInfoModel;
import com.huike.nova.service.domain.param.pay.AppreciationPreOrderParam;
import com.huike.nova.service.domain.param.pay.GetWechatUserInfoParam;
import com.huike.nova.service.domain.param.pay.PayCallBackParam;

/**
 * 支付服务
 *
 * <AUTHOR>
 * @version PayService.java, v 0.1 2023-02-01 11:44 上午 mayucong
 */
public interface PayService {

    /**
     * 增值服务预下单
     *
     * @param param
     * @return {@link AppreciationPreOrderModel}
     * <AUTHOR>
     */
    AppreciationPreOrderModel appreciationPreOrder(AppreciationPreOrderParam param);

    /**
     * 查询微信用户信息
     *
     * @param param
     * @return {@link GetWechatUserInfoModel}
     * <AUTHOR>
     */
    GetWechatUserInfoModel getWechatUserInfo(GetWechatUserInfoParam param);

    /**
     * 支付回调
     *
     * @param param
     * <AUTHOR>
     */
    void payCallback(PayCallBackParam param);

    /**
     * 校验并（现存）扣减账套数
     *
     * @param agentMcnId
     * @param storeId
     * @param merchantId
     * @return {@link Integer}
     * <AUTHOR>
     */
    Integer compareAndReduceAccount(String agentMcnId,String merchantId, String storeId);

    /**
     * 发起三方调用
     *
     * @param requestMethod 请求地址
     * @param paramStr      请求参数
     * @return {@link JSONObject}
     * <AUTHOR>
     */
    JSONObject invoker(String requestMethod, String paramStr);
}
