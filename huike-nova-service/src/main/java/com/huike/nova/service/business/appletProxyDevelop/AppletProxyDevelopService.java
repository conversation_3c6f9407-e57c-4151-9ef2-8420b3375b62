package com.huike.nova.service.business.appletProxyDevelop;

import com.huike.nova.service.domain.model.appletProxyDevelop.QueryOrderQykListModel;
import com.huike.nova.service.domain.param.appletProxyDevelop.QueryOrderQykListParam;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface AppletProxyDevelopService {

    /**
     * 授权事件回调
     *
     * @param request 回调信息
     * @return 回调结果
     */
    String authorizedCallback(HttpServletRequest request);

    /**
     * 获取三方小程序访问令牌
     *
     * @param componentAppId 三方小程序appid
     * @return 三方小程序授权令牌
     */
    String getComponentAccessToken(String componentAppId);

    /**
     * 获取授权小程序访问令牌
     *
     * @param authorizationAppid 授权小程序appId
     * @return 授权小程序访问令牌
     */
    String getAuthorizerAccessToken(String authorizationAppid);

    /**
     * 查询订单权益卡列表
     *
     * @param param 查询参数
     * @return 订单列表
     */
    List<QueryOrderQykListModel> queryOrderQykList(QueryOrderQykListParam param);
}
