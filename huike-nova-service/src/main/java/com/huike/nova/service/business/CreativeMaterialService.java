package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.creativematerial.CreativeMaterialCategoryModel;
import com.huike.nova.service.domain.model.creativematerial.CreativeMaterialModel;
import com.huike.nova.service.domain.param.creativematerial.CreativeMaterialParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年05月11日 14:49
 */
public interface CreativeMaterialService {

    /**
     * 查询创意素材类别
     *
     * @return List<CreativeMaterialCategoryModel>
     */
    List<CreativeMaterialCategoryModel> findCreativeMaterialCategory();

    /**
     * 分页查询创意素材
     *
     * @param param CreativeMaterialParam
     * @return List<CreativeMaterialModel>
     */
    PageResult<CreativeMaterialModel> findPageCreativeMaterial(PageParam<CreativeMaterialParam> param);
}
