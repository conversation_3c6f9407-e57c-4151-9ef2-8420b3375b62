package com.huike.nova.service.listener;

import com.aliyun.openservices.ons.api.Message;

import java.nio.charset.StandardCharsets;

/**
 * MsgBaseListener
 */
public class MsgBaseListener {

    /**
     * 默认重试次数 10
     */
    private static final int DEFAULT_RETRY_TIMES = 10;


    /**
     * 获取字符串类型消息体
     *
     * @param message 消息信息
     */
    String getStringMsg(Message message) {
        return new String(message.getBody(), StandardCharsets.UTF_8);
    }

    /**
     * 是否可以重试;
     *
     * @param runTime 当前执行次数
     */
    Boolean canRetryTimes(int runTime) {
        return runTime < DEFAULT_RETRY_TIMES;
    }
}