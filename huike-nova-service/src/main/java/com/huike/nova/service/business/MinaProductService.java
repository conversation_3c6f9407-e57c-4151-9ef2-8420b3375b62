package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.mina.product.CustomerNoPayOrderCheckModel;
import com.huike.nova.service.domain.model.mina.product.CustomerServiceGetModel;
import com.huike.nova.service.domain.model.mina.product.FindBlocStoreListModel;
import com.huike.nova.service.domain.model.mina.product.FindPowerLongBannerModel;
import com.huike.nova.service.domain.model.mina.product.GetBlocStoreDetailModel;
import com.huike.nova.service.domain.model.mina.product.GetCustomerMemberModel;
import com.huike.nova.service.domain.model.mina.product.GetPlazaByLocationModel;
import com.huike.nova.service.domain.model.mina.product.GetProductBrowseLogModel;
import com.huike.nova.service.domain.model.mina.product.HomePageGetModel;
import com.huike.nova.service.domain.model.mina.product.ProductGetModel;
import com.huike.nova.service.domain.model.mina.product.ProductStockCheckModel;
import com.huike.nova.service.domain.model.mina.product.ProductStoreListModel;
import com.huike.nova.service.domain.param.mina.product.CustomerNoPayOrderCheckParam;
import com.huike.nova.service.domain.param.mina.product.CustomerServiceGetParam;
import com.huike.nova.service.domain.param.mina.product.FindBlocStoreListParam;
import com.huike.nova.service.domain.param.mina.product.FindPowerLongBannerParam;
import com.huike.nova.service.domain.param.mina.product.GetBlocStoreDetailParam;
import com.huike.nova.service.domain.param.mina.product.GetCustomerMemberParam;
import com.huike.nova.service.domain.param.mina.product.GetNearestPlazaParam;
import com.huike.nova.service.domain.param.mina.product.GetPlazaByLocationParam;
import com.huike.nova.service.domain.param.mina.product.GetProductBrowseLogParam;
import com.huike.nova.service.domain.param.mina.product.HomePageGetParam;
import com.huike.nova.service.domain.param.mina.product.MinaLogAddParam;
import com.huike.nova.service.domain.param.mina.product.ProductGetParam;
import com.huike.nova.service.domain.param.mina.product.ProductStockCheckParam;
import com.huike.nova.service.domain.param.mina.product.ProductStoreListParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年10月25日 09:40
 */
public interface MinaProductService {


    /**
     * 获取商品信息
     *
     * @param param
     * @return ProductGetModel
     */
    ProductGetModel getProductInfo(ProductGetParam param);

    /**
     * 获取适用门店列表
     *
     * @param param
     * @return ProductStoreListModel
     */
    PageResult<ProductStoreListModel> findProductStoreList(PageParam<ProductStoreListParam> param);

    /**
     * 获取所有适用门店列表
     *
     * @param param param
     * @return ProductStoreListModel
     */
    List<ProductStoreListModel> findAllProductStoreList(ProductStoreListParam param);

    /**
     * 获取客服链接
     *
     * @param param
     * @return CustomerServiceGetModel
     */
    CustomerServiceGetModel getCustomerService(CustomerServiceGetParam param);

    /**
     * 校验用户是否存在未支付的订单
     *
     * @param param
     * @return
     */
    CustomerNoPayOrderCheckModel checkCustomerNoPayOrder(CustomerNoPayOrderCheckParam param);

    /**
     * 添加小程序统计日志
     *
     * @param param
     * @return
     */
    void addMinaLog(MinaLogAddParam param);


    /**
     * 校验商品库存
     *
     * @param param
     * @return
     */
    ProductStockCheckModel checkProductStock(ProductStockCheckParam param);


    /**
     * 获取集团门店详情
     *
     * @param param
     * @return
     */
    GetBlocStoreDetailModel getBlocStoreDetail(GetBlocStoreDetailParam param);


    /**
     * 获取集团门店详情
     *
     * @param param
     * @return
     */
    PageResult<FindBlocStoreListModel> findBlocStoreList(PageParam<FindBlocStoreListParam> param);

    /**
     * 获取首页信息
     *
     * @param param
     * @return
     */
    PageResult<HomePageGetModel> getHomePage(PageParam<HomePageGetParam> param);

    /**
     * 获取商品浏览记录
     *
     * @param param 商品id
     * @return 返参
     */
    GetProductBrowseLogModel getProductBrowseLog(GetProductBrowseLogParam param);

    /**
     * 获取最近的广场信息
     *
     * @param param
     * @return
     */
    ProductStoreListModel getNearestPlaza(GetNearestPlazaParam param);

    /**
     * 获取宝龙banner地址
     *
     * @param param
     * @return
     */
    FindPowerLongBannerModel findPowerLongBanner(FindPowerLongBannerParam param);

    /**
     * 查询宝龙会员信息
     *
     * @param param
     * @return
     */
    GetCustomerMemberModel getCustomerMember(GetCustomerMemberParam param);

    /**
     * 获取首页信息配置
     *
     * @param param
     * @return
     */
    HomePageGetModel getHomeConfig(HomePageGetParam param);

    /**
     * 根据经纬度获取最近广场信息
     *
     * @param param
     * @return
     */
    GetPlazaByLocationModel getPlazaByLocation(GetPlazaByLocationParam param);

    /**
     * 处理商品销售
     *
     * @param productIdList 商品Id列表
     */
    void handleUpdateProductSalesCount(List<String> productIdList);

    /**
     * 异步请求更新商品Id
     *
     * @param productIdList 商品Id列表
     */
    void requestUpdateProductSalesCount(List<String> productIdList);
}
