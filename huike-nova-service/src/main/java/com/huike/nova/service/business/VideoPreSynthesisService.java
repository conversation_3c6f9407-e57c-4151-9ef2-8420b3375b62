package com.huike.nova.service.business;

import com.huike.nova.service.domain.param.videoPreSynthesis.BatchRefreshPreSynthesisParam;
import com.huike.nova.service.domain.param.videoPreSynthesis.VideoPreSynthesisParam;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @date 2023年03月05日 10:31
 */
public interface VideoPreSynthesisService {

    /**
     * 视频预合成
     *
     * @param param 预合成参数
     */
    void videoPreSynthesis(@Validated VideoPreSynthesisParam param);

    /**
     * 批量刷新预合成列表
     *
     * @param param 入参
     */
    void batchRefreshPreSynthesis(BatchRefreshPreSynthesisParam param);
}
