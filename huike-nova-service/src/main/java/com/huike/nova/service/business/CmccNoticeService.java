/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.service.domain.param.cmcc.ReceiveOrderNoticeParam;

/**
 * <AUTHOR>
 * @version CmccNoticeService.java, v 0.1 2022-09-02 11:19 zhangling
 */
public interface CmccNoticeService {

    /**
     * 回调信息保存
     *
     * @param param 参数
     */
    void noticeSave(ReceiveOrderNoticeParam param);

    /**
     * 激动成功之后通知南山权益下方成功
     *
     * @param outOrderSn 南山订单号
     */
    void notice(String outOrderSn, String notifyUrl);


}