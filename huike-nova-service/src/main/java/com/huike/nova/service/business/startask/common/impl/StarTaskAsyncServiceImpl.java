/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.common.impl;

import cn.hutool.core.util.StrUtil;
import com.huike.nova.common.constant.AsyncThreadConstant;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.startask.mina.StarTaskMinaRedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.QuotaFullEnum;
import com.huike.nova.common.enums.startask.mina.ApplyListStatusEnum;
import com.huike.nova.common.enums.startask.mina.BindRelationStatusEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.dao.entity.StarTaskApplyListDO;
import com.huike.nova.dao.repository.StarTaskApplyListDAO;
import com.huike.nova.dao.repository.StarTaskBindRelationDAO;
import com.huike.nova.dao.repository.StarTaskCommissionPlanDAO;
import com.huike.nova.dao.repository.StarTaskDAO;
import com.huike.nova.service.business.startask.common.StarTaskAsyncService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version StarTaskAsyncServiceImpl.java, v 0.1 2023-12-12 5:57 PM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class StarTaskAsyncServiceImpl implements StarTaskAsyncService {

    private StarTaskApplyListDAO starTaskApplyListDAO;

    private StarTaskCommissionPlanDAO starTaskCommissionPlanDAO;

    private StarTaskBindRelationDAO starTaskBindRelationDAO;

    private StarTaskDAO starTaskDAO;

    private RedissonClient redissonClient;

    private TransactionTemplate transactionTemplate;

    /**
     * 操作解绑
     *
     * @param relationId
     */
    @Async(AsyncThreadConstant.SYNC_PLAY_COUNT_EXECUTOR)
    @Override
    public void operateUnbind(String relationId) {
        // 根据relationId查询报名清单
        List<StarTaskApplyListDO> list = starTaskApplyListDAO.findListByRelationId(relationId);
        for (StarTaskApplyListDO applyListDO : list) {
            String applyId = applyListDO.getApplyId();
            RLock lock = redissonClient.getLock(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_APPLY_LIST, applyId));
            try {
                if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                    LogUtil.error(log, "StarTaskAsyncServiceImpl" + "." + "operateUnbind" + " >>>>> " + "锁获取失败");
                    throw new CommonException(ErrorCodeEnum.GET_LOCK_ERROR).detailMessage("StarTaskAsyncServiceImpl" + " >>>>> " + "operateUnbind" + "  锁获取失败");
                }
                // 查询最新状态
                StarTaskApplyListDO applyListInfo = starTaskApplyListDAO.getApplyListByApplyId(applyId);
                if (Objects.isNull(applyListInfo)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("查询数据为空");
                }
                Integer applyStatus = applyListInfo.getApplyStatus();
                if (ApplyListStatusEnum.COMPLETED.getValue().equals(applyStatus) || ApplyListStatusEnum.CANCELLED.getValue().equals(applyStatus)
                        || ApplyListStatusEnum.PLATFORM_COMPLETED.getValue().equals(applyStatus)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务清单数据已更新");
                }
                // 取消任务
                applyListInfo.setCancelReason("达人解绑");
                this.cancelRegistration(applyListInfo);
            } catch (CommonException commonException) {
                LogUtil.error(log, "StarTaskAsyncServiceImpl" + "." + "operateUnbind" + " >>>>> " + "msg={}", commonException.getMsg());
                continue;
            } catch (Exception e) {
                LogUtil.error(log, "StarTaskAsyncServiceImpl" + "." + "operateUnbind" + " >>>>> " + "执行加锁方法失败", e);
                continue;
            } finally {
                try {
                    // 锁不为空  是否还是锁定状态  当前执行线程的锁
                    if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                } catch (Exception e) {
                    LogUtil.error(log, "StarTaskAsyncServiceImpl" + " >>>>> " + "operateUnbind" + "  锁释放失败", e);
                }
            }
        }
        // 更新绑定状态
        starTaskBindRelationDAO.updateBindStatus(relationId, BindRelationStatusEnum.UNBIND.getValue());
    }

    /**
     * 取消报名
     *
     * @param applyListDO
     */
    @Override
    public void cancelRegistration(StarTaskApplyListDO applyListDO) {
        String lock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_UPDATE_PARTICIPATION_COUNT, applyListDO.getTaskId());
        try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
            redisLockHelper.tryLock();
            Integer applyStatus = applyListDO.getApplyStatus();
            // 已完成不能取消
            if (ApplyListStatusEnum.COMPLETED.getValue().equals(applyStatus) || ApplyListStatusEnum.PLATFORM_COMPLETED.getValue().equals(applyStatus)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("参与任务已完成，不允许取消");
            }
            // 已取消的无需更新
            if (ApplyListStatusEnum.CANCELLED.getValue().equals(applyStatus)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("参与任务已取消");
            }
            // 事物
            transactionTemplate.execute(status -> {
                // 修改状态
                starTaskApplyListDAO.applyStatusOperation(applyListDO.getApplyId(), ApplyListStatusEnum.CANCELLED.getValue(), null, applyListDO.getCancelReason());
                if (!ApplyListStatusEnum.getMerchantPreStatusList.contains(applyListDO.getApplyStatus())) {
                    // 取消需要修改人数
                    starTaskCommissionPlanDAO.updateParticipationCount(applyListDO.getCommissionPlanId());
                    // 更新任务的名额未满状态
                    starTaskDAO.updateIsQuotaFull(applyListDO.getTaskId(), QuotaFullEnum.NOT_FULL.getValue());
                }
                return Boolean.TRUE;
            });
        } catch (CommonException commonException) {
            throw commonException;
        } catch (Exception e) {
            LogUtil.error(log, "StarTaskAsyncServiceImpl" + "." + "cancelRegistration" + " >>>>> " + "执行加锁方法失败", e);
            throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage(e.getMessage());
        }
    }

}