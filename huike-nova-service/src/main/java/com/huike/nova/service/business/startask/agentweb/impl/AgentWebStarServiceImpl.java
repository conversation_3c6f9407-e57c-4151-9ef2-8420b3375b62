/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum;
import com.huike.nova.common.enums.startask.web.DistributionTypeEnum;
import com.huike.nova.common.enums.startask.web.OperateExportBusinessTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.dao.domain.param.startask.BalanceDetailsPageListParamDTO;
import com.huike.nova.dao.domain.param.startask.WebMerchantListParamDTO;
import com.huike.nova.dao.domain.result.StarTaskBalanceLogResultDTO;
import com.huike.nova.dao.domain.result.startask.StarAccountResultDTO;
import com.huike.nova.dao.domain.result.startask.StartAcceptOrderResultDTO;
import com.huike.nova.dao.domain.result.startask.WebMerchantListResultDTO;
import com.huike.nova.dao.entity.AilikeGaodeCodeDO;
import com.huike.nova.dao.entity.StarTaskAgentAreaDO;
import com.huike.nova.dao.entity.StarTaskAgentDO;
import com.huike.nova.dao.entity.StarTaskIdentityRelationDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.AilikeGaodeCodeDAO;
import com.huike.nova.dao.repository.StarTaskAgentAreaDAO;
import com.huike.nova.dao.repository.StarTaskAgentDAO;
import com.huike.nova.dao.repository.StarTaskApplyListDAO;
import com.huike.nova.dao.repository.StarTaskBalanceLogDAO;
import com.huike.nova.dao.repository.StarTaskBindRelationDAO;
import com.huike.nova.dao.repository.StarTaskIdentityDAO;
import com.huike.nova.dao.repository.StarTaskIdentityRelationDAO;
import com.huike.nova.service.business.startask.agentweb.AgentWebStarService;
import com.huike.nova.service.business.startask.web.StarTaskWebExportService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.web.StarTaskWebStarServiceObjMapper;
import com.huike.nova.service.domain.model.startask.agentweb.login.StarTaskAgentWebLoginModel;
import com.huike.nova.service.domain.model.startask.web.star.WebStarListModel;
import com.huike.nova.service.domain.model.startask.web.star.WebStarModel;
import com.huike.nova.service.domain.param.startask.web.star.BalanceDetailsPageListParam;
import com.huike.nova.service.domain.param.startask.web.star.WebStarListParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version AgentWebStarServiceImpl.java, v 0.1 2024-05-22 2:41 PM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class AgentWebStarServiceImpl implements AgentWebStarService {

    private StarTaskWebStarServiceObjMapper starTaskWebStarServiceObjMapper;

    private StarTaskBalanceLogDAO starTaskBalanceLogDAO;

    private StarTaskIdentityDAO starTaskIdentityDAO;

    private StarTaskBindRelationDAO starTaskBindRelationDAO;

    private StarTaskIdentityRelationDAO starTaskIdentityRelationDAO;

    private StarTaskApplyListDAO starTaskApplyListDAO;

    private StarTaskAgentAreaDAO starTaskAgentAreaDAO;

    private StarTaskAgentDAO starTaskAgentDAO;

    private AilikeGaodeCodeDAO ailikeGaodeCodeDAO;

    private StarTaskWebExportService starTaskWebExportService;

    /**
     * 达人列表
     *
     * @param param
     * @return
     */
    @Override
    public PageResult<WebStarListModel> list(PageParam<WebStarListParam> param) {
        // 获取登录态信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        // 分页查询达人列表
        Page<WebMerchantListResultDTO> activityPage = starTaskIdentityDAO.pageList(this.buildPageParam(param, basicInfo));
        List<WebMerchantListResultDTO> resultDTOList = activityPage.getRecords();
        List<WebStarListModel> modelList = Lists.newArrayList();
        // 循环
        if (CollectionUtil.isNotEmpty(resultDTOList)) {
            // 查询代理商信息
            StarTaskAgentDO agentInfo = starTaskAgentDAO.getAgentInfo(basicInfo.getAgentId());
            if (Objects.isNull(agentInfo)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("区代信息异常");
            }
            List<String> identityIdList = resultDTOList.stream().map(WebMerchantListResultDTO::getIdentityId).collect(Collectors.toList());
            List<StarAccountResultDTO> starAccountResultDTOList = starTaskBindRelationDAO.findAllStarListByIdentityIdList(identityIdList);
            // 使用stream对starAccountResultDTOList做分组根据identityId
            Map<String, List<StarAccountResultDTO>> starAccountResultMap = starAccountResultDTOList.stream().collect(Collectors.groupingBy(StarAccountResultDTO::getIdentityId));
            //达人接单数据
            List<StartAcceptOrderResultDTO> startAcceptOrderResultDTOList = starTaskApplyListDAO.statisticsStarAcceptOrderNum(identityIdList);
            Map<String, StartAcceptOrderResultDTO> startAcceptOrderNumMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(startAcceptOrderResultDTOList)) {
                startAcceptOrderNumMap = startAcceptOrderResultDTOList.stream().collect(Collectors.toMap(StartAcceptOrderResultDTO::getIdentityId, Function.identity()));
            }
            //查询分销的达人列表数据
            List<StarTaskIdentityRelationDO> listByParentIdList = starTaskIdentityRelationDAO.findListByParentIdList(identityIdList);
            Map<String, StarTaskBalanceLogResultDTO> distributionMap = new HashMap<>();
            List<StarTaskBalanceLogResultDTO> list = starTaskBalanceLogDAO.statisticsDistributionRewardsAmount(identityIdList);
            if (CollectionUtils.isNotEmpty(list)) {
                distributionMap = list.stream().collect(Collectors.toMap(StarTaskBalanceLogResultDTO::getIdentityId, Function.identity()));
            }
            //省市处理
            List<String> adCodeList = resultDTOList.stream().map(WebMerchantListResultDTO::getProvince).distinct().collect(Collectors.toList());
            adCodeList.addAll(resultDTOList.stream().map(WebMerchantListResultDTO::getCity).distinct().collect(Collectors.toList()));
            List<AilikeGaodeCodeDO> codeInfoList = ailikeGaodeCodeDAO.findCodeInfoList(adCodeList);
            Map<String, AilikeGaodeCodeDO> adCodeMap = codeInfoList.stream().collect(Collectors.toMap(AilikeGaodeCodeDO::getAdcode, Function.identity()));
            for (WebMerchantListResultDTO item : resultDTOList) {
                WebStarListModel model = starTaskWebStarServiceObjMapper.toWebStarListModel(item);
                model.setTotalIncome(item.getIncomeTotalIncome());
                List<StarAccountResultDTO> accountResultDTOList = starAccountResultMap.get(item.getIdentityId());
                List<WebStarModel> starModelList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(accountResultDTOList)) {
                    starModelList = starTaskWebStarServiceObjMapper.toWebStarModelList(accountResultDTOList);
                }
                model.setStarList(starModelList);
                model.setPhoneNumber(FieldEncryptUtil.decode(item.getPhoneNumber()));
                String inviterPhone = item.getInviterPhone();
                if (StringUtils.isNotBlank(inviterPhone)) {
                    model.setInviterPhone(FieldEncryptUtil.decode(inviterPhone));
                }
                model.setTotalIncome(item.getIncomeTotalIncome());
                //达人分销的下级数量
                model.setInviterStarCount(CommonConstant.ZERO);
                if (CollectionUtil.isNotEmpty(listByParentIdList)) {
                    model.setInviterStarCount(listByParentIdList.stream().filter(e -> e.getParentId().equals(item.getIdentityId())).collect(Collectors.toList()).size());
                }
                // 达人分销奖励
                model.setDistributionAwardAmount(CommonConstant.ZERO.toString());
                if (Objects.nonNull(distributionMap.get(item.getIdentityId()))) {
                    model.setDistributionAwardAmount(distributionMap.get(item.getIdentityId()).getDistributionRewardsAmount().toString());
                }
                // 达人有效接单统计
                model.setAcceptOrderNumFor7(CommonConstant.ZERO);
                model.setAcceptOrderNumFor30(CommonConstant.ZERO);
                model.setAcceptOrderNumTotal(CommonConstant.ZERO);
                StartAcceptOrderResultDTO acceptOrderResultDTO = startAcceptOrderNumMap.get(item.getIdentityId());
                if (ObjectUtil.isNotNull(acceptOrderResultDTO)) {
                    model.setAcceptOrderNumFor7(ObjectUtil.defaultIfNull(acceptOrderResultDTO.getAcceptOrderNumFor7(), CommonConstant.ZERO));
                    model.setAcceptOrderNumFor30(ObjectUtil.defaultIfNull(acceptOrderResultDTO.getAcceptOrderNumFor30(), CommonConstant.ZERO));
                    model.setAcceptOrderNumTotal(ObjectUtil.defaultIfNull(acceptOrderResultDTO.getAcceptOrderNumTotal(), CommonConstant.ZERO));
                }
                // 达人等级
                model.setStarLevelName(ObjectUtil.isEmpty(item.getStarLevel()) ? "-" : StrUtil.format("Lv{}", item.getStarLevel()));
                // 省市
                model.setCityName("-");
                AilikeGaodeCodeDO provinceInfo = adCodeMap.get(item.getProvince());
                AilikeGaodeCodeDO cityInfo = adCodeMap.get(item.getCity());
                if (ObjectUtil.isNotNull(provinceInfo) && ObjectUtil.isNotNull(cityInfo)) {
                    model.setCityName(StrUtil.format("{}-{}", ObjectUtil.defaultIfBlank(provinceInfo.getAdname(), ""),
                            ObjectUtil.defaultIfBlank(cityInfo.getAdname(), "")));
                }
                //分销比例
                if (DistributionTypeEnum.DEFAULT.getValue().equals(item.getDistributionType())) {
                    model.setDistributionRate(agentInfo.getStarRate());
                }
                modelList.add(model);
            }
        }
        PageResult<WebStarListModel> result = new PageResult<>();
        result.setRecords(modelList);
        result.setTotal(activityPage.getTotal());
        result.setCurrent(activityPage.getCurrent());
        result.setSize(activityPage.getSize());
        return result;
    }

    /**
     * 导出达人列表
     *
     * @param param
     */
    @Override
    public void requestExport(WebStarListParam param) {
        // 获取登录态信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        String agentId = basicInfo.getAgentId();
        WebMerchantListParamDTO query = starTaskWebStarServiceObjMapper.toWebMerchantListParamDTO(param);
        String phoneNumber = query.getPhoneNumber();
        if (StringUtils.isNotBlank(phoneNumber)) {
            query.setPhoneNumber(FieldEncryptUtil.encode(phoneNumber));
        }
        // 查询区域
        if (CollectionUtil.isEmpty(query.getCityList())) {
            List<StarTaskAgentAreaDO> list = starTaskAgentAreaDAO.findByAgentId(agentId);
            query.setCityList(list.stream().map(StarTaskAgentAreaDO::getCity).collect(Collectors.toList()));
        }
        String createEndDate = query.getCreateEndDate();
        String createStartDate = query.getCreateStartDate();
        if (StringUtils.isNotBlank(createStartDate) && StringUtils.isNotBlank(createEndDate)) {
            query.setStartTime(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(createStartDate, DatePattern.NORM_DATE_PATTERN))));
            query.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(createEndDate, DatePattern.NORM_DATE_PATTERN))));
        }
        String inviterPhone = query.getInviterPhone();
        if (StringUtils.isNotBlank(inviterPhone)) {
            query.setInviterStarPhone(FieldEncryptUtil.encode(inviterPhone));
        }
        query.setIdentityType(StarTaskIdentityTypeEnum.STAR.getValue());
        query.setAgentId(agentId);
        // 异步导出
        starTaskWebExportService.addAgentExportTask(OperateExportBusinessTypeEnum.AGENT_STAR_MANAGEMENT, query, basicInfo);
    }

    /**
     * 收银明细导出
     *
     * @param param 入参
     */
    @Override
    public void requestExportBalanceDetailsList(BalanceDetailsPageListParam param) {
        // 获取登录态信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        BalanceDetailsPageListParamDTO query = starTaskWebStarServiceObjMapper.toBalanceDetailsPageListParamDTO(param);
        val identityTypeEnum = StarTaskIdentityTypeEnum.getByValue(param.getIdentityType());
        OperateExportBusinessTypeEnum businessType = OperateExportBusinessTypeEnum.AGENT_STAR_BALANCE_DETAILS_LIST;
        if (Objects.nonNull(identityTypeEnum)) {
            switch (identityTypeEnum) {
                case MERCHANT:
                    businessType = OperateExportBusinessTypeEnum.AGENT_MERCHANT_BALANCE_DETAILS_LIST;
                    break;
                case STAR:
                    break;
                default:
            }
        }
        // 异步导出
        starTaskWebExportService.addAgentExportTask(businessType, query, basicInfo);
    }

    @NotNull
    private PageParam<WebMerchantListParamDTO> buildPageParam(PageParam<WebStarListParam> param, StarTaskAgentWebLoginModel basicInfo) {
        PageParam<WebMerchantListParamDTO> pageParam = starTaskWebStarServiceObjMapper.toWebMerchantListParamDTO(param);
        WebMerchantListParamDTO query = pageParam.getQuery();
        String phoneNumber = query.getPhoneNumber();
        if (StringUtils.isNotBlank(phoneNumber)) {
            query.setPhoneNumber(FieldEncryptUtil.encode(phoneNumber));
        }
        // 查询区域
        if (CollectionUtil.isEmpty(query.getCityList())) {
            List<StarTaskAgentAreaDO> list = starTaskAgentAreaDAO.findByAgentId(basicInfo.getAgentId());
            query.setCityList(list.stream().map(StarTaskAgentAreaDO::getCity).collect(Collectors.toList()));
        }
        String createEndDate = query.getCreateEndDate();
        String createStartDate = query.getCreateStartDate();
        if (StringUtils.isNotBlank(createStartDate) && StringUtils.isNotBlank(createEndDate)) {
            query.setStartTime(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(createStartDate, DatePattern.NORM_DATE_PATTERN))));
            query.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(createEndDate, DatePattern.NORM_DATE_PATTERN))));
        }
        String inviterPhone = query.getInviterPhone();
        if (StringUtils.isNotBlank(inviterPhone)) {
            query.setInviterStarPhone(FieldEncryptUtil.encode(inviterPhone));
        }
        query.setIdentityType(StarTaskIdentityTypeEnum.STAR.getValue());
        return pageParam;
    }
}