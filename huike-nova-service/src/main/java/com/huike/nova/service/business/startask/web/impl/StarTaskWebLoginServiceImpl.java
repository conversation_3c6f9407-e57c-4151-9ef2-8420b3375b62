/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.web.impl;

import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.huike.nova.common.constant.startask.mina.StarTaskMinaRedisPrefixConstant;
import com.huike.nova.common.enums.EnvEnum;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.SmsTypeEnum;
import com.huike.nova.common.enums.oem.BooleanEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.entity.GrantDO;
import com.huike.nova.dao.entity.StarTaskOperatorDO;
import com.huike.nova.dao.repository.GrantDAO;
import com.huike.nova.dao.repository.StarTaskOperatorDAO;
import com.huike.nova.service.business.startask.web.StarTaskWebLoginService;
import com.huike.nova.service.domain.model.startask.web.login.StarTaskWebLoginModel;
import com.huike.nova.service.domain.param.startask.web.login.StarTaskWebLoginParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version StarTaskWebLoginServiceImpl.java, v 0.1 2023-12-05 6:24 PM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class StarTaskWebLoginServiceImpl implements StarTaskWebLoginService {

    private StarTaskOperatorDAO starTaskOperatorDAO;

    private RedissonClient redissonClient;

    private SysConfig sysConfig;

    private GrantDAO grantDAO;

    /**
     * 登录
     *
     * @param param
     * @return
     */
    @Override
    public StarTaskWebLoginModel login(StarTaskWebLoginParam param) {
        String account = param.getAccount();
        // 登录校验
        StarTaskOperatorDO operator = this.checkLogin(param);
        String operatorId = operator.getOperatorId();
        // 下线
        StpUtil.logout();
        // sa-token登录
        SaLoginModel loginModel = new SaLoginModel();
        loginModel.setTimeout(60 * 60 * 24 * 7);
        loginModel.setDevice("star.task.pc");
        StpUtil.login(operatorId, loginModel);
        // 获取accessToken
        final String accessToken = StpUtil.getTokenInfo().getTokenValue();
        // 查询账号下面角色权限
        List<GrantDO> grantDOList = grantDAO.queryGrantByIdentityId(operatorId);
        StarTaskWebLoginModel model = new StarTaskWebLoginModel();
        model.setGrantList(grantDOList.stream().map(item -> item.getGrantCode()).collect(Collectors.toList()));
        model.setAccessToken(accessToken);
        model.setAppletId(operator.getAppletId());
        model.setContactName(operator.getContactName());
        model.setPhoneNumber(account);
        model.setOperatorId(operator.getOperatorId());
        // sa-token存储登录信息
        StpUtil.getTokenSessionByToken(accessToken).set(accessToken, JSON.toJSONString(model));
        return model;
    }

    /**
     * 登录校验
     *
     * @param param
     * @return
     */
    private StarTaskOperatorDO checkLogin(StarTaskWebLoginParam param) {
        String appletId = param.getAppletId();
        String code = param.getCode();
        // 查看用户是否存在
        StarTaskOperatorDO operator = starTaskOperatorDAO.getStarTaskOperator(param.getAccount(), appletId);
        // 校验
        if (Objects.isNull(operator)) {
            throw new CommonException(ErrorCodeEnum.LOGIN_ERROR);
        }
        if (BooleanEnum.NO.getValue().equals(operator.getOperatorStatus())) {
            throw new CommonException(ErrorCodeEnum.LOGIN_ERROR).detailMessage("账号已被禁用");
        }
        if (EnvEnum.TEST.getValue().equalsIgnoreCase(sysConfig.getEnv()) && param.getCode().equals("9999")) {
            return operator;
        }

        // 校验验证码是否正确
        final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_SMS_VERIFY
                , appletId, SmsTypeEnum.STAR_TASK_WEB_LOGIN.getValue(), param.getAccount()), StringCodec.INSTANCE);
        String smdCode = bucket.get();
        if (StringUtils.isBlank(smdCode)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("验证码已失效，请重新发送");
        }
        if (Objects.isNull(code) || !smdCode.equals(code)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("验证码错误，请重新输入");
        }
        return operator;
    }
}