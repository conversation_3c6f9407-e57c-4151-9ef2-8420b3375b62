package com.huike.nova.service.websocket;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.enums.*;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.result.AudioDTO;
import com.huike.nova.dao.entity.AilikeZbtSoundScriptDO;
import com.huike.nova.dao.entity.AilikeZbtSoundScriptSoundContentDO;
import com.huike.nova.dao.entity.AilikeZbtStoreConfigDO;
import com.huike.nova.dao.repository.AilikeZbtAudioDAO;
import com.huike.nova.dao.repository.AilikeZbtSoundScriptDAO;
import com.huike.nova.dao.repository.AilikeZbtSoundScriptSoundContentDAO;
import com.huike.nova.dao.repository.AilikeZbtStoreConfigDAO;
import com.huike.nova.service.business.ZbtService;
import com.huike.nova.service.domain.dto.zbt.ZbtLivePushDTO;
import com.huike.nova.service.domain.dto.zbt.ZbtStoreLiveDataDTO;
import com.huike.nova.service.domain.dto.zbt.ZbtWebSocketDTO;
import com.huike.nova.service.domain.dto.zbt.ZbtWebSocketPushDataDTO;
import com.huike.nova.service.message.MsgProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023年09月01日 16:58
 */
@Slf4j
@ServerEndpoint(value = "/zbtWebsocket/{userId}")
@Component
public class ZbtWebSocket {

    // session集合,存放对应的session
    private static ConcurrentHashMap<String, Session> sessionPool = new ConcurrentHashMap<>();
    // concurrent包的线程安全Set,用来存放每个客户端对应的WebSocket对象。
    // private static CopyOnWriteArraySet<ZbtWebSocket> webSocketSet = new CopyOnWriteArraySet<>();
    // 与某个客户端的连接会话，需要通过它来给客户端发送数据
    // private Session session;

    private static AilikeZbtSoundScriptDAO ailikeZbtSoundScriptDAO;

    private static AilikeZbtSoundScriptSoundContentDAO ailikeZbtSoundScriptSoundContentDAO;

    private static SysConfig sysConfig;

    private static AilikeZbtAudioDAO ailikeZbtAudioDAO;

    private static RedissonClient redissonClient;

    private static ZbtService zbtService;

    private static MsgProducer msgProducer;

    private static AilikeZbtStoreConfigDAO ailikeZbtStoreConfigDAO;


    @Autowired
    public void setAilikeZbtSoundScriptDAO(AilikeZbtSoundScriptDAO ailikeZbtSoundScriptDAO) {
        ZbtWebSocket.ailikeZbtSoundScriptDAO = ailikeZbtSoundScriptDAO;
    }

    @Autowired
    public void setAilikeZbtSoundScriptSoundContentDAO(AilikeZbtSoundScriptSoundContentDAO ailikeZbtSoundScriptSoundContentDAO) {
        ZbtWebSocket.ailikeZbtSoundScriptSoundContentDAO = ailikeZbtSoundScriptSoundContentDAO;
    }

    @Autowired
    public void setSysConfig(SysConfig sysConfig) {
        ZbtWebSocket.sysConfig = sysConfig;
    }

    @Autowired
    public void setAilikeZbtAudioDAO(AilikeZbtAudioDAO ailikeZbtAudioDAO) {
        ZbtWebSocket.ailikeZbtAudioDAO = ailikeZbtAudioDAO;
    }

    @Autowired
    public void setRedissonClient(RedissonClient redissonClient) {
        ZbtWebSocket.redissonClient = redissonClient;
    }

    @Autowired
    public void setZbtService(ZbtService zbtService) {
        ZbtWebSocket.zbtService = zbtService;
    }

    @Autowired
    public void setMsgProducer(MsgProducer msgProducer) {
        ZbtWebSocket.msgProducer = msgProducer;
    }

    @Autowired
    public void setAilikeZbtStoreConfigDAO(AilikeZbtStoreConfigDAO ailikeZbtStoreConfigDAO) {
        ZbtWebSocket.ailikeZbtStoreConfigDAO = ailikeZbtStoreConfigDAO;
    }


    /**
     * 推送消息到指定用户
     *
     * @param userId  用户ID
     * @param message 发送的消息
     */
    public void sendMessageByUser(String userId, String message) {
        LogUtil.info(log, "ZbtWebSocket.sendMessageByUser >>  推送消息到指定用户>> userId = {},message={}", userId, message);

        Session session = sessionPool.get(userId);
        try {
            session.getBasicRemote().sendText(message);
        } catch (IOException e) {
            LogUtil.info(log, "ZbtWebSocket.sendMessageByUser >>  推送消息到指定用户发生错误", e);
        }
    }


    /**
     * 建立WebSocket连接
     *
     * @param session session
     * @param userId  用户ID
     */
    @OnOpen
    public void onOpen(Session session, @PathParam(value = "userId") String userId) {
        LogUtil.info(log, "ZbtWebSocket.onOpen >> WebSocket建立连接中:连接用户ID  >> userId = {}", userId);
        try {
            Session historySession = sessionPool.get(userId);
            // historySession不为空,说明已经有人登陆账号,应该删除登陆的WebSocket对象
            if (historySession != null) {
                sessionPool.remove(userId);
                historySession.close();
            }
        } catch (IOException e) {
            LogUtil.info(log, "ZbtWebSocket.onOpen >> 重复登录异常", e);
        }
        // 建立连接
        sessionPool.put(userId, session);
        LogUtil.info(log, "ZbtWebSocket.onOpen >> 建立连接完成:当前在线人数为  >> {}", sessionPool.size());
    }

    /**
     * 发生错误
     *
     * @param throwable e
     */
    @OnError
    public void onError(Throwable throwable) {
        LogUtil.info(log, "ZbtWebSocket.onError >> 发生错误 >> throwable = {}", throwable);
        throwable.printStackTrace();
        LogUtil.info(log, "ZbtWebSocket.onError >> 发生错误 >> throwable = {}", throwable);
    }

    /**
     * 连接关闭
     */
    @OnClose
    public void onClose(@PathParam(value = "userId") String userId) {
        sessionPool.remove(userId);
        LogUtil.info(log, "ZbtWebSocket.onClose >>连接断开: 当前在线人数为 >> {}", sessionPool.size());
    }

    /**
     * 接收客户端消息
     *
     * @param message 接收的消息
     */
    @OnMessage
    public void onMessage(String message) {
        // 解析json
        ZbtWebSocketDTO socketParamDTO = JSONArray.parseObject(message, ZbtWebSocketDTO.class);
        if (null == socketParamDTO) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("参数有误");
        }
        Integer messageType = socketParamDTO.getMessageType();
        ZbtMessageTypeEnum zbtMessageTypeEnum = ZbtMessageTypeEnum.getByValue(messageType);
        if (null == zbtMessageTypeEnum) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("未知类型");
        }
        switch (zbtMessageTypeEnum) {
            case SOUND:
                // 获取口播
                getSoundList(socketParamDTO);
                break;
            case AUDIO:
                // 获取背景音乐
                getAudioList(socketParamDTO);
                break;
            case REPLY:
                // 获取口播回复
                break;
            case LIVE_DATA:
                // 获取直播数据
                getLiveData(socketParamDTO);
                break;
            case LIVE_KEEP_ALIVE:
                // 智播通助手保活
                liveKeepAlive(socketParamDTO);
                break;
            default:
                throw new CommonException(ErrorCodeEnum.ACTIVITY_PARAM_ERROR).detailMessage("视频预合成类型不合法");
        }
    }

    /**
     * 获取直播数据
     *
     * @param socketParamDTO 入参
     */
    private void getLiveData(ZbtWebSocketDTO socketParamDTO) {
        // 获取门店配置拿到rpaid
        // 根据rpaid拿到account id
        // 根据account id拿到直播间数据
        String storeId = socketParamDTO.getStoreId();
        String accountId = zbtService.getAccountIdByStoreId(storeId);
        // 获取redis
        RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.EOS_ACCOUNT_ID_OF_LIVE_ROOM, accountId));
        if (bucket.isExists()) {
            String liveDataJson = bucket.get();
            ZbtStoreLiveDataDTO storeLiveDataDTO = JSON.parseObject(liveDataJson, ZbtStoreLiveDataDTO.class);
            Integer liveStatus = zbtService.getLiveStatusByStoreId(storeId);
            storeLiveDataDTO.setLiveStatus(liveStatus == null ? LiveStatusEnum.NOT_LIVE.getValue() : liveStatus);
            this.sendMessageByUser(storeId, JSON.toJSONString(new ZbtWebSocketPushDataDTO(socketParamDTO.getMessageType(), JSON.toJSONString(storeLiveDataDTO))));
        }
    }

    /**
     * 智播通助手保活
     *
     * @param socketParamDTO 入参
     */
    private void liveKeepAlive(ZbtWebSocketDTO socketParamDTO) {
        String storeId = socketParamDTO.getStoreId();
        // 设置五分钟保活
        RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.ZBT_STORE_KEEP_ALIVE_KEY, storeId));
        bucket.set(storeId, 300, TimeUnit.SECONDS);
        //     抖音直播保活
        AilikeZbtStoreConfigDO storeConfig = ailikeZbtStoreConfigDAO.getStoreConfig(storeId);
        if (null == storeConfig || StringUtils.isBlank(storeConfig.getRpaId())) {
            return;
        }
        ZbtLivePushDTO zbtLivePushDTO = new ZbtLivePushDTO(storeConfig.getRpaId(), null, ZbtPushTypeEnum.KEEP_ALIVE.getValue());
        msgProducer.sendMessage(ProduceEnum.ZBT_LIVE_PUSH, JSON.toJSONString(zbtLivePushDTO));
    }

    /**
     * 获取背景音乐
     *
     * @param socketParamDTO 入参
     */
    private void getAudioList(ZbtWebSocketDTO socketParamDTO) {
        String storeId = socketParamDTO.getStoreId();
        //     根据门店id查询已选择音乐列表
        List<AudioDTO> audioList = ailikeZbtAudioDAO.findAudioListByStoreId(storeId);
        if (CollectionUtil.isEmpty(audioList)) {
            return;
        }
        Collections.shuffle(audioList);
        Integer batchCount = sysConfig.getZbtLiveMaterialBatchCount();
        int size = audioList.size();
        int getBatchCount = size < batchCount ? size : batchCount;
        List<AudioDTO> audioModelList = audioList.subList(0, getBatchCount);
        this.sendMessageByUser(storeId, JSON.toJSONString(new ZbtWebSocketPushDataDTO(socketParamDTO.getMessageType(), JSON.toJSONString(audioModelList))));
    }

    /**
     * 获取口播
     *
     * @param socketParamDTO 入参
     */
    private void getSoundList(ZbtWebSocketDTO socketParamDTO) {
        String storeId = socketParamDTO.getStoreId();
        //     根据门店id获取当前选中的口播脚本
        AilikeZbtSoundScriptDO soundScriptDO = ailikeZbtSoundScriptDAO.getUseSoundScriptByStoreId(storeId);
        if (null == soundScriptDO) {
            return;
        }
        String scriptId = soundScriptDO.getScriptId();
        List<AilikeZbtSoundScriptSoundContentDO> soundContentList = ailikeZbtSoundScriptSoundContentDAO.findListSoundContent(scriptId);
        if (CollectionUtil.isEmpty(soundContentList)) {
            return;
        }
        Collections.shuffle(soundContentList);
        Integer batchCount = sysConfig.getZbtLiveMaterialBatchCount();
        int size = soundContentList.size();
        int getBatchCount = size < batchCount ? size : batchCount;
        List<AilikeZbtSoundScriptSoundContentDO> soundContentModelList = soundContentList.subList(0, getBatchCount);
        this.sendMessageByUser(storeId, JSON.toJSONString(new ZbtWebSocketPushDataDTO(socketParamDTO.getMessageType(), JSON.toJSONString(soundContentModelList))));
    }

}
