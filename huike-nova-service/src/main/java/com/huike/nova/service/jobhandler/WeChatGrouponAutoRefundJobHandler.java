/*
 *
 *  * Fshows Technology
 *  * Copyright (C) 2022-2024 All Rights Reserved.
 *
 */

package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.wechat.WechatGrouponPaymentService;
import com.huike.nova.service.domain.param.wechat.mina.WechatZyAutoRefundExpiryCouponsParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 微信团购自动退款
 *
 * <AUTHOR> (<EMAIL>)
 * @version WeChatGrouponAutoRefundJobHandler.java, v1.0 2025/2/21 16:11 John Exp$
 */
@Component
@Slf4j
@JobHandler("weChatGrouponAutoRefundJobHandler")
@AllArgsConstructor
public class WeChatGrouponAutoRefundJobHandler extends AbsJobHandler {

    private WechatGrouponPaymentService wechatGrouponPaymentService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("WeChatGrouponAutoRefundJobHandler.execute >> 微信团购自动退款：time = {}, traceId = {}", DateUtil.now(), getOrGenerateTraceId());
        wechatGrouponPaymentService.autoRefundExpiredCoupons(new WechatZyAutoRefundExpiryCouponsParam());
        XxlJobLogger.log("WeChatGrouponAutoRefundJobHandler.execute >> 微信团购自动退款：time = {}, traceId = {}", DateUtil.now(), getOrGenerateTraceId());
        return ReturnT.SUCCESS;
    }
}
