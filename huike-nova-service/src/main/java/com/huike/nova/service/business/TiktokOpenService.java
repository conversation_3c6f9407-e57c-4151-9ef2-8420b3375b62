/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.alibaba.fastjson.JSONObject;
import com.huike.nova.common.enums.ChannelCodeEnum;
import com.huike.nova.common.enums.ProductOnlineStatusEnum;
import com.huike.nova.service.domain.dto.mina.ClientInfoDTO;
import com.huike.nova.service.domain.model.tiktokopen.*;
import com.huike.nova.service.domain.param.count.FindVideoCountParam;
import com.huike.nova.service.domain.param.count.MerchantAuditCallbackParam;
import com.huike.nova.service.domain.param.count.QueryOrderParam;
import com.huike.nova.service.domain.param.mina.order.MinaOrderDeveloperOperateRefundParam;
import com.huike.nova.service.domain.param.openapi.SaasAuthParam;
import com.huike.nova.service.domain.param.order.ConfigureSelfDeliveryParam;
import com.huike.nova.service.domain.param.order.DealRefundOrderParam;
import com.huike.nova.service.domain.param.order.OperatePayOrderParam;
import com.huike.nova.service.domain.param.order.RefundApplyParam;
import com.huike.nova.service.domain.param.tiktokopen.DouyinAkteAfterSaleOrderAuditParam;
import com.huike.nova.service.domain.param.tiktokopen.*;
import com.huike.nova.service.domain.result.channel.TiktokOpenConfig;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.List;

/**
 * 抖音接口调用类
 *
 * <AUTHOR>
 * @version TiktokOpenService.java, v 0.1 2022-09-01 2:51 PM ruanzy
 */
public interface TiktokOpenService {

    /**
     * 获得Saas认证接口地址
     *
     * @param param 认证参数
     * @return 认证的Url地址
     */
    String getSaasAuthUrl(SaasAuthParam param);

    /**
     * 获取网页client_token
     *
     * @return
     */
    String getWebClientToken(ClientInfoDTO clientInfoDTO);

    /**
     * 获取抖音jsb_ticket
     *
     * @return
     */
    String getJsbTicket(ClientInfoDTO clientInfoDTO);

    /**
     * 获取抖音open_ticket
     *
     * @return
     */
    String getOpenTicket(String clientKey);

    /**
     * 获取抖音access_token
     *
     * @param clientKey
     * @param code
     * @param clientInfoDTO
     * @return
     */
    TiktokAccessTokenModel getAccessToken(String clientKey, String code, ClientInfoDTO clientInfoDTO);

    /**
     * 获取抖音话题
     *
     * @return
     */
    TiktokTopicListModel findTopicList();

    /**
     * 活动抖音热词
     *
     * @return
     */
    TiktokSentenceListModel findSentenceList();

    /**
     * 获取抖音视频share_id
     *
     * @return
     */
    String getShareId(String clientKey);

    /**
     * 获取用户发布视频数据
     *
     * @param param
     * @return
     */
    TiktokVideoCountListModel findVideoStatisticsByOpenIdAndItemIds(FindVideoCountParam param);

    /**
     * 获取抖音门店信息
     *
     * @param accountId
     * @param poiId
     * @return
     */
    TiktokStoreListModel findStoreList(String accountId, String poiId, Integer page, Integer size);

    /**
     * 活动抖音的用户信息
     *
     * @param openId
     * @param accessToken
     * @return
     */
    TiktokUserInfoModel getUserInfo(@Nullable String clientKey, String openId, String accessToken);

    /**
     * 旧的 refresh_token 获取新的 refresh_token
     *
     * @param refreshToken
     * @param clientKey
     * @return
     */
    TiktokRefreshTokenModel getNewRefreshToken(String refreshToken, String clientKey);

    /**
     * 获取客服url
     *
     * @param openId
     * @return url
     */
    String getCustomerServiceUrl(String openId);

    /**
     * 获取openid
     *
     * @param code
     * @param config
     * @return GetCode2sessionModel
     */
    GetCode2sessionModel getCode2session(String code, TiktokOpenConfig config);

    /**
     * 同步退款审核结果
     *
     * @param param
     * @param config
     * @return
     */
    void merchantAuditCallback(MerchantAuditCallbackParam param, TiktokOpenConfig config);

    /**
     * 查询订单信息
     *
     * @param param
     * @param config
     * @return QueryOrderModel
     */
    QueryOrderModel queryOrder(QueryOrderParam param, TiktokOpenConfig config);

    /**
     * 查询订单信息（返回原始信息）
     *
     * @param param  请求参数
     * @param config 通道配置
     * @return 原始的订单对象
     */
    BaseModel<QueryOrderModel> queryOrderPrimitive(QueryOrderParam param, TiktokOpenConfig config);

    /**
     * 获取抖音AccessToken
     *
     * @return AccessToken
     */
    String getAccessToken();

    /**
     * 验券准备
     *
     * @param param
     * @param config
     * @return
     */
    PrepareDeliveryModel prepareDelivery(PrepareDeliveryParam param, TiktokOpenConfig config);

    /**
     * 验券
     *
     * @param param
     * @param config
     * @return
     */
    VerifyDeliveryModel verifyDelivery(VerifyDeliveryParam param, TiktokOpenConfig config);

    /**
     * 查看券状态信息
     *
     * @param orderId
     * @param itemOrderIdList
     * @param config
     * @return
     */
    MinaCouponListModel queryCouponInfo(String orderId, List<String> itemOrderIdList, TiktokOpenConfig config);

    /**
     * 撤销核销
     *
     * @param param
     * @param config
     */
    void cancelVerify(CancelVerifyParam param, TiktokOpenConfig config);

    /**
     * 服务商验券准备
     *
     * @param param
     * @return
     */
    @Nonnull
    ServiceCertificatesDataModel servicePrepareDelivery(PrepareDeliveryParam param);

    /**
     * 服务商验券准备
     *
     * @param param  验券参数
     * @param config 抖音配置
     * @return 验券准备参数
     */
    @Nonnull
    ServiceCertificatesDataModel servicePrepareDelivery(PrepareDeliveryParam param, @Nullable TiktokOpenConfig config);

    /**
     * 闭环订单查询
     *
     * @param queryParam
     * @return
     */
    CloseOrderQueryModel closeOrderQuery(CloseOrderQueryParam queryParam);

    /**
     * 交易订单查询（以闭环为主）
     *
     * @param param 查询参数
     * @return 订单查询结果
     */
    CloseOrderQueryModel tradeOrderQuery(DouyinTradeOrderQueryParam param);

    /**
     * 一单多品商品查询
     *
     * @param param 请求参数
     * @return 一单多品商品查询Model
     */
    AkteCloseOrderQueryModel akteCloseOrderQuery(CloseOrderQueryParam param);

    /**
     * 闭环/开环订单查询（服务授权后，支持查询开环订单）
     *
     * @param accountId 抖音来客Id
     * @param orderId   抖音订单Id
     * @return 订单详情
     */
    CloseOrderQueryModel closeOrderQuery(String accountId, String orderId);

    /**
     * 卡券状态查询
     *
     * @param encryptedCode 卡券的加密券码
     * @param outOrderId    抖音订单号（二选一）
     * @return
     */
    CloseCertificateQueryModel certificateQuery(@Nullable String encryptedCode, @Nullable String outOrderId);

    /**
     * 服务商验券
     *
     * @param param
     * @return
     */
    ServiceVerifyDeliveryModel serviceVerifyDelivery(ServiceVerifyDeliveryParam param);

    /**
     * 服务商取消验券
     *
     * @param param
     * @return
     */
    ServiceCancelVerifyModel serviceCancelVerify(ServiceCancelVerifyParam param);

    /**
     * 获取小程序抖音client_token
     *
     * @param config
     * @return
     */
    String getMinaClientToken(TiktokOpenConfig config);

    /**
     * 自配送-回传配送信息
     *
     * @param param
     */
    void configureSelfDelivery(ConfigureSelfDeliveryParam param);

    /**
     * 商家同意/拒绝退款接口
     *
     * @param param
     */
    void dealRefundOrder(DealRefundOrderParam param);

    /**
     * 商家确认订单接口
     *
     * @param param
     */
    void confirmTakeoutOrder(OperatePayOrderParam param);

    /**
     * 商家拒绝接单
     *
     * @param param
     */
    void rejectTakeoutOrder(OperatePayOrderParam param);

    /**
     * 商家取消订单接口
     *
     * @param param
     * @return
     */
    TakeoutRefundApplyModel refundApply(RefundApplyParam param);

    /**
     * 开发者发起退款
     *
     * @param param  参数
     * @param config 通道配置
     * @return
     */
    DouyinDeveloperOperateRefundModel developerOperateRefund(MinaOrderDeveloperOperateRefundParam param, TiktokOpenConfig config);


    /**
     * 原生券信息查询
     *
     * @param certificateIdList 要查询的券idList
     * @param channelCode       channel_api_config表的channel_code
     * @param orderIdList       要查询的订单id列表
     * @return 券信息列表
     */
    QueryCertificateInfoModel queryCertificateInfo(List<String> certificateIdList, String channelCode, List<String> orderIdList);

    /**
     * 设置小程序跳转path
     *
     * @param accountId 抖音来客 id
     */
    void updateMerchantPath(String accountId);

    /**
     * 推送服务完成
     *
     * @param verifyIdList 履约记录id列表
     * @param channelCode  channel_api_config表的channel_code
     * @param orderId      订单id
     */
    void pushServiceDone(String orderId, String channelCode, List<String> verifyIdList);

    /**
     * 获得商品线上数据
     *
     * @param accountId  抖音来客Id
     * @param productIds 商品Id列表
     * @return 线上数据信息
     */
    TiktokProductOnlineModel queryProductOnlineInfo(String accountId, String productIds);

    /**
     * 查询商品线上数据
     * @param channelCode 通道类型
     * @param accountId 抖音来客Id
     * @param productIds 商品Id
     * @return 在线商品信息
     */
    TiktokProductOnlineModel queryProductOnlineInfo(String channelCode, String accountId, String productIds);

    /**
     * 券状态查询
     *
     * @param param
     * @return
     */
    CertificateQueryStatusModel queryCertificateStatus(CertificateQueryStatusParam param);

    /**
     * 抖音餐饮订单售后单退款审核
     *
     * @param param 参数
     * @return 返回
     */
    ServiceAkteAfterSaleOrderAuditModel akteAfterSaleOrderAudit(DouyinAkteAfterSaleOrderAuditParam param);

    /**
     * 查询线上商品数据
     *
     * @param param 请求
     * @return 返回
     */
    JSONObject queryGoodProductOnlineList(QueryDouyinOnlineProductParam param);

    /**
     * 查询核销记录
     *
     * @param param 请求
     * @return 返回
     */
    JSONObject queryVerifyRecordList(QueryDouyinVerifyRecordParam param);


    /**
     * 商品操作
     *
     * @param param 参数
     */
    void productOperate(ProductOperateParam param);
}