/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler.startask;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.startask.common.StarTaskJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version InitIdentityrelationJobHandler.java, v 0.1 2024-01-25 5:10 PM ruanzy
 */
@Component
@Slf4j
@JobHandler("initIdentityRelationJobHandler")
@AllArgsConstructor
public class InitIdentityRelationJobHandler extends IJobHandler {

    private StarTaskJobService starTaskJobService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("InitIdentityRelationJobHandler.execute >> 初始化身份关联脚本执行开始：time = {}", DateUtil.now());
        starTaskJobService.initIdentityRelationJobHandler();
        XxlJobLogger.log("InitIdentityRelationJobHandler.execute >> 初始化身份关联脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}