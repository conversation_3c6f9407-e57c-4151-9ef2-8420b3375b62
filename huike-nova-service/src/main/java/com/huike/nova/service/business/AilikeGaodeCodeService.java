package com.huike.nova.service.business;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023年08月03日 11:06
 */
public interface AilikeGaodeCodeService {

    /**
     * 获取拼接地址名称
     *
     * @param codeList 地址编码列表
     * @return 地址名称
     */
    String getSplicingAreaName(List<String> codeList, String delimiter);

    /**
     * 获取地址名称
     *
     * @param codeList 地址编码列表
     * @return 地址名称
     */
    Map<String, String> findAreaName(List<String> codeList);
}
