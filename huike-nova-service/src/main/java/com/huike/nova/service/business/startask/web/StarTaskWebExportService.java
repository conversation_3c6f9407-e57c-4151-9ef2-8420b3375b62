package com.huike.nova.service.business.startask.web;

import com.huike.nova.common.enums.startask.web.OperateExportBusinessTypeEnum;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.dao.domain.param.startask.ApplyAuditRecordsPageListDTO;
import com.huike.nova.dao.domain.param.startask.BalanceDetailsPageListParamDTO;
import com.huike.nova.dao.domain.param.startask.WebApplyListParamDTO;
import com.huike.nova.dao.domain.param.startask.WebMerchantListParamDTO;
import com.huike.nova.dao.domain.param.startask.WebTaskListParamDTO;
import com.huike.nova.dao.domain.param.startask.WithdrawPageListParamDTO;
import com.huike.nova.dao.entity.StarTaskOcExportTaskDO;
import com.huike.nova.service.domain.model.startask.agentweb.login.StarTaskAgentWebLoginModel;
import com.huike.nova.service.domain.model.startask.web.export.StarTaskExportTaskModel;
import com.huike.nova.service.domain.model.startask.web.login.StarTaskWebLoginModel;
import com.huike.nova.service.domain.param.startask.web.export.StarTaskWebExportTaskListParam;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

/**
 * 达人任务广场，任务导出服务
 *
 * <AUTHOR> (<EMAIL>)
 * @version StarTaskWebExportService.java, v1.0 12/13/2023 14:46 John Exp$
 */
public interface StarTaskWebExportService {

    /**
     * 新增导出任务
     *
     * @param businessType 业务枚举
     * @param param        参数值
     * @param loginModel   登录态参数，可空
     */
    void addExportTask(@Nonnull OperateExportBusinessTypeEnum businessType, @Nullable Object param, @Nullable StarTaskWebLoginModel loginModel);

    /**
     * 新增导出任务
     *
     * @param businessType 业务枚举
     * @param param        参数值
     * @param loginModel   登录态参数，可空
     */
    void addAgentExportTask(@Nonnull OperateExportBusinessTypeEnum businessType, @Nullable Object param, @Nullable StarTaskAgentWebLoginModel loginModel);

    /**
     * 商家列表导出
     *
     * @param param      导出参数
     * @param loginModel 登录态参数，可空
     */
    void applyExportMerchantList(WebMerchantListParamDTO param, @Nullable StarTaskWebLoginModel loginModel);

    /**
     * 收益明细导出
     *
     * @param identityType 身份类型，见{@link com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum}
     * @param param        导出参数
     * @param loginModel   登录态参数，可空
     */
    void applyExportBalanceDetailsList(Integer identityType, BalanceDetailsPageListParamDTO param, @Nullable StarTaskWebLoginModel loginModel);

    /**
     * 达人信息导出
     *
     * @param param      导出参数
     * @param loginModel 登录态参数，可空
     */
    void applyExportStarList(WebMerchantListParamDTO param, @Nullable StarTaskWebLoginModel loginModel);

    /**
     * 提现记录导出
     *
     * @param param      导出参数
     * @param loginModel 登录态参数，可空
     */
    void applyExportWithdrawalList(WithdrawPageListParamDTO param, @Nullable StarTaskWebLoginModel loginModel);

    /**
     * 任务列表导出
     *
     * @param param      导出参数
     * @param loginModel 登录态参数，可空
     */
    void applyExportWebTaskList(WebTaskListParamDTO param, @Nullable StarTaskWebLoginModel loginModel);

    /**
     * 任务列表的报名清单导出
     *
     * @param param      导出参数
     * @param loginModel 登录态参数，可空
     */
    void applyExportWebTaskApplyList(WebApplyListParamDTO param, @Nullable StarTaskWebLoginModel loginModel);

    /**
     * 执行导出任务
     *
     * @param starTaskOcExportTaskDO 导出记录
     */
    void executeExportTask(StarTaskOcExportTaskDO starTaskOcExportTaskDO);

    /**
     * 分页请求导出记录
     *
     * @param param 请求参数
     * @return 分页列表导出
     */
    PageResult<StarTaskExportTaskModel> list(PageParam<StarTaskWebExportTaskListParam> param);

    /**
     * 删除任务
     *
     * @param taskId 任务Id
     */
    void deleteTask(String taskId);

    /**
     * 通知任务
     */
    void notifyExecuteTasks();

    /**
     * 清理文件
     */
    void cleanUp();

    /**
     * 申请审核记录导出
     *
     * @param requestParam 导出参数
     */
    void applyAuditRecordsExcel(ApplyAuditRecordsPageListDTO requestParam);
}
