/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.startask.agentweb.settle.GetAgentAccountModel;
import com.huike.nova.service.domain.model.startask.agentweb.settle.GetSettleDetailModel;
import com.huike.nova.service.domain.model.startask.agentweb.settle.PageSettleDetailModel;
import com.huike.nova.service.domain.model.startask.agentweb.settle.PageSettleListModel;
import com.huike.nova.service.domain.model.startask.agentweb.settle.PageWithdrawalListModel;
import com.huike.nova.service.domain.param.startask.agentweb.settle.ApplyAgentWithdrawalParam;
import com.huike.nova.service.domain.param.startask.agentweb.settle.GetSettleDetailParam;
import com.huike.nova.service.domain.param.startask.agentweb.settle.PageSettleListParam;

/**
 * <AUTHOR>
 * @version AreaAgentSettleService.java, v 0.1 2024-05-23 10:31 AM ruanzy
 */
public interface AreaAgentSettleService {

    /**
     * 账户信息
     *
     * @return
     */
    GetAgentAccountModel getAgentAccount();

    /**
     * 代理商申请提现
     *
     * @param param
     */
    void applyAgentWithdrawal(ApplyAgentWithdrawalParam param);

    /**
     * 分页查询结算列表
     *
     * @param param
     * @return
     */
    PageResult<PageSettleListModel> pageSettleList(PageParam<PageSettleListParam> param);

    /**
     * 结算详情
     *
     * @param param
     * @return
     */
    GetSettleDetailModel getSettleDetail(GetSettleDetailParam param);


    /**
     * 分页查询提现列表
     *
     * @param Param
     * @return
     */
    PageResult<PageWithdrawalListModel> pageWithdrawalList(PageParam Param);

    /**
     * 分页查询结算详情明细
     *
     * @param param
     * @return
     */
    PageResult<PageSettleDetailModel> pageSettleDetail(PageParam<GetSettleDetailParam> param);
}