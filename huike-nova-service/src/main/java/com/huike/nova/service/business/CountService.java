/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.dao.domain.param.PageStarRankParamDTO;
import com.huike.nova.dao.domain.result.PageStarRankResultDTO;
import com.huike.nova.service.domain.model.count.*;
import com.huike.nova.service.domain.param.count.*;

/**
 * <AUTHOR>
 * @version CountService.java, v 0.1 2022-09-01 8:13 PM ruanzy
 */
public interface CountService {

    /**
     * 活动数据统计
     *
     * @param param
     * @return
     */
    ActivityStatisticsModel statisticsActivity(ActivityStatisticsParam param);

    /**
     * 获取活动列表
     *
     * @param param
     * @return
     */
    FindActivityStoreListModel findActivityList(FindActivityListParam param);

    /**
     * 活动数据统计详情
     *
     * @param param
     * @return
     */
    ActivityStatisticsListModel statisticsActivityDetail(ActivityStatisticsParam param);

    /**
     * 门店首页数据统计
     *
     * @param param
     * @return
     */
    StoreHomeStatisticMapModel statisticsStoreHome(StoreHomeStatisticsParam param);

    /**
     * 活动详情页统计
     *
     * @param param
     * @return
     */
    CountActivityDetailDataModel statisticsActivityDetailData(StoreHomeStatisticsParam param);

    /**
     * 商品数据统计
     *
     * @param param
     * @return
     */
    GoodsStatisticsModel statisticsGoods(GoodsStatisticsParam param);

    /**
     * 核销数据统计
     *
     * @param param
     * @return
     */
    VerifyStatisticsModel statisticsVerify(VerifyStatisticsParam param);

    /**
     * 商品数据统计详情
     *
     * @param param
     * @return
     */
    TradeStatisticsListModel statisticsGoodsDetail(GoodsStatisticsParam param);

    /**
     * 核销数据统计详情
     *
     * @param param
     * @return
     */
    TradeStatisticsListModel statisticsVerifyDetail(VerifyStatisticsParam param);

    /**
     * 新版门店首页数据统计
     *
     * @param param
     * @return
     */
    CountAppStoreHomeDataModel countAppHomeData(CountAppHomeDataParam param);

    /**
     * 交易外卖统计
     *
     * @param param
     * @return
     */
    CountTakeoutOrderModel countTakeoutOrder(CountTakeoutOrderParam param);

    /**
     * 交易外卖统计详情
     *
     * @param param
     * @return
     */
    CountTakeoutOrderListModel countTakeoutOrderDetail(CountTakeoutOrderParam param);

    /**
     * 数据统计-推广页面数据概览
     *
     * @param param
     * @return
     */
    PromotionDataModel getPromotionData(PromotionDataParam param);

    /**
     * 推广数据-达人排行
     *
     * @param pageParam
     * @return
     */
    PageResult<PageStarRankModel> pageStarRank(PageParam<PageStarRankParam> pageParam);


    /**
     * 数据统计-oem小程序推广页面数据概览
     *
     * @param param
     * @return
     */
    CountAppStoreHomeDataModel getOemPromotionData(CountAppHomeDataParam param);
}