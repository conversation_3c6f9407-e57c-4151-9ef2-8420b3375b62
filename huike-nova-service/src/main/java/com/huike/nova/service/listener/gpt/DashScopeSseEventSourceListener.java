package com.huike.nova.service.listener.gpt;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.annimon.stream.function.Consumer;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.sdk.aliyun.dashscope.DashScopeConstants;
import com.huike.nova.sdk.aliyun.dashscope.model.DashScopeAigcSseResponse;
import com.huike.nova.service.enums.GPTServiceProviderEnum;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.redisson.api.RAtomicLong;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Objects;

/**
 * 灵积模型 - 通义的sse事件监听器
 *
 * <AUTHOR> (<EMAIL>)
 * @version DashScopeSseEventSourceListener.java, v1.0 11/29/2023 09:24 John Exp$
 */
@Slf4j
@Getter
public class DashScopeSseEventSourceListener extends AbsGptSseEventSourceListener {
    public DashScopeSseEventSourceListener(GPTServiceProviderEnum gptServiceProvider, SseEmitter sseEmitter, String contentId, String accountId, RAtomicLong atomicLong) {
        super(gptServiceProvider, sseEmitter, contentId, accountId, atomicLong);
    }

    /**
     * 数据处理重写
     *
     * @param data 从流中获得数据
     * @param processingDataHandler 结束回调
     * @return true-当前流已经结束 false-当前流未结束
     */
    @SneakyThrows
    @Override
    protected boolean processContent(String data, Consumer<String> processingDataHandler) {
        LogUtil.info(log, "DashScopeSseEventSourceListener.processContent >> SSE.{} >> contentId:{} data:{}", getGptServiceProvider(), contentId, data);
        // 转换为JSON对象
        val completionResponse = JSONObject.parseObject(data, DashScopeAigcSseResponse.class);
        if (Objects.isNull(completionResponse) || Objects.isNull(completionResponse.getOutput()))  {
            throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("三方返回数据为空");
        }
        val chatChoice = CollectionUtil.getFirst(completionResponse.getOutput().getChoices());
        if (Objects.isNull(chatChoice)) {
            return true;
        }
        // 如果包含非结束标识位("null)
        if (DashScopeConstants.NULL_STR.equalsIgnoreCase(chatChoice.getFinishReason())) {
            // 移除回车换行符号
            val content = removeCRLF(chatChoice.getMessage().getContent());
            processingDataHandler.accept(content);
            return false;
        }
        // 如果包含结束标志位，返回true,非结束返回false
        return DashScopeConstants.STOP_STR.equalsIgnoreCase(chatChoice.getFinishReason());
    }
}
