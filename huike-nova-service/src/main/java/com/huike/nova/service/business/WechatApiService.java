package com.huike.nova.service.business;

import com.huike.nova.service.domain.model.wx.WxAuthInfo;
import com.huike.nova.service.domain.model.wx.WxPhoneInfo;
import com.huike.nova.service.domain.model.wx.WxTransferBillResult;
import com.huike.nova.service.domain.model.wx.WxTransferDetailResult;
import com.huike.nova.service.domain.model.wx.WxTransferResult;
import com.huike.nova.service.domain.param.wechatapi.WxSubscribeMessageSendParam;
import com.huike.nova.service.domain.param.wechatapi.WxTransferParam;

/**
 * 微信API服务
 *
 * <AUTHOR>
 * @date 2024年02月19日 14:45
 */
public interface WechatApiService {

    /**
     * 推送订阅消息
     *
     * @param param 入参
     */
    void subscribeMessageSend(WxSubscribeMessageSendParam param);

    /**
     * 微信code2session授权
     *
     * @param appid  小程序appId
     * @param secret 小程序secret
     * @param code   授权code
     * @return 授权信息
     */
    WxAuthInfo code2Session(String appid, String secret, String code);

    /**
     * 获取手机号
     *
     * @param code  手机号获取凭证,动态令牌,可通过动态令牌换取用户手机号,有效期5分钟,一次有效
     * @param appid 小程序唯一凭证，即 AppID
     * @return 用户手机号信息
     * @see <a href="https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-info/phone-number/getPhoneNumber.html">微信文档</a>
     */
    WxPhoneInfo getPhoneNumber(String code, String appid);

    /**
     * 获取小程序全局唯一后台接口调用凭据
     *
     * @param appid 小程序唯一凭证，即 AppID
     * @return 接口调用凭据信息
     * @see <a href="https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-access-token/getAccessToken.html">微信文档</a>
     */
    String getAccessToken(String appid);

    /**
     * 商家转账到零钱
     *
     * @param param 转账参数
     * @param appid 小程序appId
     * @return 转账结果
     * @see <a href="https://pay.weixin.qq.com/doc/v3/merchant/4012458841">微信文档</a>
     */
    WxTransferResult transfer(WxTransferParam param, String appid);

    /**
     * 查询转账明细单
     *
     * @param batchId  微信批次单号
     * @param detailId 微信明细单号
     * @return 转账明细单信息
     * @see <a href="https://pay.weixin.qq.com/doc/v3/merchant/4012458886">微信文档</a>
     */
    WxTransferDetailResult getTransferDetail(String batchId, String detailId);

    // /**
    //  * 商家转账到零钱 使用微信 sdk
    //  *
    //  * @param param 转账参数
    //  * @param appid 小程序appId
    //  * @return 转账结果
    //  * @see <a href="https://github.com/wechatpay-apiv3/wechatpay-java/blob/main/service/src/main/java/com/wechat/pay/java/service/transferbatch/TransferBatchService.java:initiateBatchTransfer">微信 sdk</a>
    //  */
    // WxTransferResult transferSdk(WxTransferParam param, String appid);
    //
    // /**
    //  * 查询转账明细单 使用微信 sdk
    //  *
    //  * @param batchId  微信批次单号
    //  * @param detailId 微信明细单号
    //  * @return 转账明细单信息
    //  * @see <a href="https://github.com/wechatpay-apiv3/wechatpay-java/blob/main/service/src/main/java/com/wechat/pay/java/service/transferbatch/TransferBatchService.java:getTransferDetailByOutNo">微信 sdk</a>
    //  */
    // WxTransferDetailResult getTransferDetailSdk(String batchId, String detailId);
    //

    /**
     * 发起转账
     *
     * @param param 转账参数
     * @return 转账结果
     * @see <a href="https://pay.weixin.qq.com/doc/v3/merchant/4012716434">微信文档</a>
     */
    WxTransferResult transferBills(WxTransferParam param);

    /**
     * 根据商户单号查询转账单
     *
     * @param outBillNo 商户单号
     * @return 转账单详细信息
     * @see <a href="https://pay.weixin.qq.com/doc/v3/merchant/4012716437">微信文档</a>
     */
    WxTransferBillResult getTransferBillByOutBillNo(String outBillNo);

}
