package com.huike.nova.service.listener;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * ICE回调
 *
 * <AUTHOR> (<EMAIL>)
 * @version IceCallbackRepublishListener.java, v1.0 2025-05-23 14:27 John Exp$
 */
@Slf4j
@Component
public class IceCallbackRepublishListener implements MessageListener {
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        return Action.CommitMessage;
    }
}
