package com.huike.nova.service.business.appletProxyDevelop;

import com.huike.nova.service.domain.model.appletProxyDevelop.platform.AuthTokenModel;
import com.huike.nova.service.domain.model.appletProxyDevelop.platform.AuthorizerAppletCode2sessionModel;
import com.huike.nova.service.domain.model.appletProxyDevelop.platform.GetComponentAccessTokenModel;
import com.huike.nova.service.domain.model.appletProxyDevelop.platform.RetrieveAuthorizationCodeModel;

/**
 * <AUTHOR>
 */
public interface AppletProxyDevelopPlatformService {

    /**
     * 获取第三方小程序接口调用凭据
     *
     * @param componentAppId 三方应用appid
     * @return 回调结果
     */
    GetComponentAccessTokenModel getComponentAccessToken(String componentAppId);

    /**
     * 找回授权小程序授权码
     *
     * @param authorizationAppid 授权小程序appId
     * @return 授权小程序授权码
     */
    RetrieveAuthorizationCodeModel retrieveAuthorizationCode(String authorizationAppid);

    /**
     * 获取授权小程序token
     *
     * @param authorizationCode 授权码
     * @return 授权小程序token
     */
    AuthTokenModel getAuthToken(String authorizationCode);

    /**
     * 刷新授权小程序token
     *
     * @param authorizationAppid     授权小程序appId
     * @param authorizerRefreshToken 刷新令牌
     * @return 授权小程序token
     */
    AuthTokenModel refreshAuthToken(String authorizationAppid, String authorizerRefreshToken);

    /**
     * 授权小程序Code2session
     *
     * @param authorizationAppid 授权小程序appId
     * @param code     登录code
     * @return 小程序用户openId
     */
    AuthorizerAppletCode2sessionModel authorizerAppletCode2session(String authorizationAppid, String code);


}
