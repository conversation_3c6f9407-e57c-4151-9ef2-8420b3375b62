/*
 * ailike.com
 * Copyright (C) 2022-2023 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.service.domain.model.printer.BsjBaseResult;
import com.huike.nova.service.domain.model.printer.BsjCustomPrintResult;
import com.huike.nova.service.domain.model.printer.BsjFindDeviceResult;

/**
 * <AUTHOR>
 * @version BsjOpenService.java, v 0.1 2023-01-30 15:10 zhangling
 */
public interface BsjOpenService {

    /**
     * 设备注册云端
     * @param devName
     * @param cloudType
     * @return
     */
    boolean registerCloud(String devName, Integer cloudType);

    /**
     * 语音播报
     * @param devName
     * @param bizType
     * @param content
     * @return
     */
    boolean broadcastControl(String devName, Integer bizType, String content);

    /**
     * 自定义打印
     * @param devName
     * @param cn
     * @param data
     * @param voiceJson
     * @param isPrint
     * @return
     */
    BsjCustomPrintResult customPrint(String devName, Integer cn, String data, String voiceJson, boolean isPrint);

    /**
     * 备状态信息查询
     * @param devName
     * @return
     */
    BsjBaseResult<BsjFindDeviceResult> findDeviceInfo(String devName);
}