/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.service.domain.model.releasevideo.GetKsPublishVideoStatusModel;
import com.huike.nova.service.domain.model.releasevideo.GetUserInfoModel;
import com.huike.nova.service.domain.model.releasevideo.LandingPageModel;
import com.huike.nova.service.domain.model.releasevideo.SchemaCreateModel;
import com.huike.nova.service.domain.model.releasevideo.SignatureCalculateModel;
import com.huike.nova.service.domain.model.releasevideo.VideoStatusModel;
import com.huike.nova.service.domain.param.releasevideo.GetKsPublishVideoStatusParam;
import com.huike.nova.service.domain.param.releasevideo.GetUserInfoParam;
import com.huike.nova.service.domain.param.releasevideo.LandingPageParam;
import com.huike.nova.service.domain.param.releasevideo.ScanLogUpdateParam;
import com.huike.nova.service.domain.param.releasevideo.SchemaCreateParam;
import com.huike.nova.service.domain.param.releasevideo.SignatureCalculateParam;
import com.huike.nova.service.domain.param.releasevideo.VideoStatusParam;

/**
 * <AUTHOR>
 * @version ReleaseVideoService.java, v 0.1 2022-09-02 4:36 PM ruanzy
 */
public interface ReleaseVideoService {

    /**
     * 加签
     *
     * @param param
     * @return
     */
    SignatureCalculateModel calculateSignature(SignatureCalculateParam param);


    /**
     * 获取用户信息
     *
     * @param param
     * @return
     */
    GetUserInfoModel getUserInfo(GetUserInfoParam param);

    /**
     * 分享内容至抖音H5场景
     *
     * @param param
     * @return
     */
    SchemaCreateModel createSchema(SchemaCreateParam param);

    /**
     * 扫码流量更新
     *
     * @param param
     */
    void updateScanLog(ScanLogUpdateParam param);

    /**
     * 获取视频合成状态
     *
     * @param param
     * @return
     */
    VideoStatusModel getVideoStatus(VideoStatusParam param);

    /**
     * 落地页信息获取
     *
     * @param param
     * @return
     */
    LandingPageModel getLandingPageInfo(LandingPageParam param);

    /**
     * 获取快手发布视频状态
     *
     * @param param
     * @return
     */
    GetKsPublishVideoStatusModel getKsPublishVideoStatus(GetKsPublishVideoStatusParam param);
}