/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.customerdata.CustomerListModel;
import com.huike.nova.service.domain.model.customerdata.DetailsModel;
import com.huike.nova.service.domain.model.customerdata.ExportModel;
import com.huike.nova.service.domain.model.customerdata.PermissionModel;
import com.huike.nova.service.domain.param.customerdata.CustomerUpdateParam;
import com.huike.nova.service.domain.param.customerdata.DetailsParam;
import com.huike.nova.service.domain.param.customerdata.FilterCustomerParam;
import com.huike.nova.service.domain.param.customerdata.PermissionParam;

/**
 * <AUTHOR>
 * @version CustomerDataService.java, v 0.1 2023-03-27 5:17 PM ruanzy
 */
public interface CustomerDataService {

    /**
     * 团购商品权限
     *
     * @param param
     * @return
     */
    PermissionModel permission(PermissionParam param);

    /**
     * 更新用户信息
     *
     * @param param
     */
    void updateCustomerData(CustomerUpdateParam param);

    /**
     * 导出明细
     *
     * @param param
     * @return
     */
    ExportModel export(FilterCustomerParam param);

    /**
     * 详情页面
     *
     * @param param
     * @return
     */
    DetailsModel details(DetailsParam param);

    /**
     * 用户列表
     *
     * @param param
     * @return
     */
    PageResult<CustomerListModel> findCustomerDataList(PageParam<FilterCustomerParam> param);
}