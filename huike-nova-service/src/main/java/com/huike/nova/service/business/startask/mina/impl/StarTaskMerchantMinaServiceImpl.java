/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.mina.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.ExcelConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.constant.startask.StarTaskConstant;
import com.huike.nova.common.constant.startask.mina.StarTaskMinaRedisPrefixConstant;
import com.huike.nova.common.enums.AliOssEnum;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.PlatformTypeEnum;
import com.huike.nova.common.enums.ProduceEnum;
import com.huike.nova.common.enums.QuotaFullEnum;
import com.huike.nova.common.enums.TaskAmountTypeEnum;
import com.huike.nova.common.enums.oem.BooleanEnum;
import com.huike.nova.common.enums.startask.mina.ApplyListStatusEnum;
import com.huike.nova.common.enums.startask.mina.CommissionTypeEnum;
import com.huike.nova.common.enums.startask.mina.OperateApplyListTypeEnum;
import com.huike.nova.common.enums.startask.mina.OperateStarTaskTypeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskPaymentTypeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskPresetOperatorEnum;
import com.huike.nova.common.enums.startask.mina.SubscribeMessageTypeEnum;
import com.huike.nova.common.enums.startask.mina.TaskStatusEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogChangeTypeEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogRemarkTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.common.util.BizNoBuildUtil;
import com.huike.nova.common.util.ExceptionUtil;
import com.huike.nova.common.util.FsDateUtils;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.param.startask.ApplyListParamDTO;
import com.huike.nova.dao.domain.param.startask.PublishTaskListParamDTO;
import com.huike.nova.dao.domain.result.startask.ApplyListResultDTO;
import com.huike.nova.dao.domain.result.startask.PublishTaskListResultDTO;
import com.huike.nova.dao.entity.AilikeGaodeCodeDO;
import com.huike.nova.dao.entity.StarTaskAgentDO;
import com.huike.nova.dao.entity.StarTaskApplyListDO;
import com.huike.nova.dao.entity.StarTaskAreaLimitDO;
import com.huike.nova.dao.entity.StarTaskBalanceAccountDO;
import com.huike.nova.dao.entity.StarTaskBalanceLogDO;
import com.huike.nova.dao.entity.StarTaskCommissionPlanDO;
import com.huike.nova.dao.entity.StarTaskDO;
import com.huike.nova.dao.entity.StarTaskIdentityDO;
import com.huike.nova.dao.entity.StarTaskIdentityRelationDO;
import com.huike.nova.dao.entity.StarTaskUserDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.AilikeGaodeCodeDAO;
import com.huike.nova.dao.repository.StarTaskAgentDAO;
import com.huike.nova.dao.repository.StarTaskApplyListDAO;
import com.huike.nova.dao.repository.StarTaskAreaLimitDAO;
import com.huike.nova.dao.repository.StarTaskBalanceAccountDAO;
import com.huike.nova.dao.repository.StarTaskBalanceLogDAO;
import com.huike.nova.dao.repository.StarTaskCommissionPlanDAO;
import com.huike.nova.dao.repository.StarTaskDAO;
import com.huike.nova.dao.repository.StarTaskIdentityDAO;
import com.huike.nova.dao.repository.StarTaskIdentityRelationDAO;
import com.huike.nova.dao.repository.StarTaskUserDAO;
import com.huike.nova.service.business.CommonService;
import com.huike.nova.service.business.OssManager;
import com.huike.nova.service.business.common.DingDingCommonService;
import com.huike.nova.service.business.startask.common.StarTaskCommonService;
import com.huike.nova.service.business.startask.common.StarTaskJobService;
import com.huike.nova.service.business.startask.mina.StarTaskCommonMinaService;
import com.huike.nova.service.business.startask.mina.StarTaskMerchantMinaService;
import com.huike.nova.service.business.startask.mina.StarTaskWechatMinaService;
import com.huike.nova.service.business.startask.mina.SubscribeMessageService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.mina.StarTaskMerchantMinaServiceObjMapper;
import com.huike.nova.service.domain.mapper.startask.mina.StarTaskStarMinaServiceObjMapper;
import com.huike.nova.service.domain.model.startask.mina.login.StarTaskMinaLoginModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.ApplyListModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.BalancePayModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.CommissionPlanModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.ExportApplyListModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.FindMerchantCategoryListModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.PaymentModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.PublishStarAreaModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.PublishStarTaskModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.PublishTaskDetailModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.PublishTaskListModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.QueryMerchantInfoModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.QueryTaskMoneyModel;
import com.huike.nova.service.domain.param.startask.mina.merchant.ApplyListParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.BalancePaymentParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.ExportApplyListParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.OperateApplyListParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.OperateStarTaskParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.PublishCommissionPlanParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.PublishStarAreaParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.PublishStarTaskParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.PublishTaskDetailParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.PublishTaskListParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.QueryMerchantInfoParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.QueryTaskMoneyParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.StarTaskMinaPaymentParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.UpdateMerchantInfoParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.UpdateStarTaskParam;
import com.huike.nova.service.domain.param.startask.mina.pay.StarTaskMinaCloseUnpaidOrderParam;
import com.huike.nova.service.domain.result.excel.ApplyListExportResult;
import com.huike.nova.service.message.MsgProducer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version StarTaskMerchantMinaServiceImpl.java, v 0.1 2023-11-25 2:27 PM ruanzy
 */
@SuppressWarnings("JavadocDeclaration")
@Slf4j
@Service
@AllArgsConstructor
public class StarTaskMerchantMinaServiceImpl implements StarTaskMerchantMinaService {

    private StarTaskDAO starTaskDAO;

    private StarTaskAreaLimitDAO starTaskAreaLimitDAO;

    private StarTaskCommissionPlanDAO starTaskCommissionPlanDAO;

    private StarTaskApplyListDAO starTaskApplyListDAO;

    private StarTaskBalanceLogDAO starTaskBalanceLogDAO;

    private StarTaskUserDAO starTaskUserDao;

    private StarTaskBalanceAccountDAO starTaskBalanceAccountDAO;

    private TransactionTemplate transactionTemplate;

    private CommonService commonService;

    private StarTaskCommonService starTaskCommonService;

    private StarTaskIdentityRelationDAO starTaskIdentityRelationDAO;

    private DingDingCommonService dingDingCommonService;

    private StarTaskWechatMinaService starTaskWechatMinaService;

    private StarTaskMerchantMinaServiceObjMapper starTaskMerchantMinaServiceObjMapper;

    private StarTaskStarMinaServiceObjMapper starTaskStarMinaServiceObjMapper;

    private RedissonClient redissonClient;

    private OssManager ossManager;

    private MsgProducer msgProducer;

    private SysConfig sysConfig;

    private StarTaskIdentityDAO starTaskIdentityDAO;

    private AilikeGaodeCodeDAO ailikeGaodeCodeDAO;

    private StarTaskAgentDAO starTaskAgentDAO;

    private StarTaskCommonMinaService starTaskCommonMinaService;

    private SubscribeMessageService subscribeMessageService;

    private StarTaskJobService starTaskJobService;

    /**
     * 发布任务
     *
     * @param param
     * @return
     */
    @Override
    public PublishStarTaskModel publish(PublishStarTaskParam param) {
        // 登录态获取用户信息
        StarTaskMinaLoginModel loginBasicInfo = LoginUtil.getStarTaskLoginBasicInfo(StarTaskIdentityTypeEnum.MERCHANT.getValue());
        String identityId = loginBasicInfo.getIdentityId();
        StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(identityId);
        if (null == identityDO) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息异常");
        }
        // 快手平台不支持等级阶梯价结算方式
        if (PlatformTypeEnum.KS.getValue().equals(param.getPlatform())
                && CommissionTypeEnum.LEVEL_STEP.getValue().equals(param.getCommissionType())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("快手平台不支持等级阶梯价结算方式");
        }
        // 判断是否有区代
        StarTaskAgentDO agentDO = starTaskAgentDAO.checkAreaAgentByCity(identityDO.getCity());
        BigDecimal platformRate = identityDO.getPlatformRate();
        if (TaskAmountTypeEnum.DEFAULT.getValue().equals(identityDO.getTaskMoneyType())) {
            platformRate = Objects.isNull(agentDO) ? new BigDecimal(sysConfig.getStarTaskMinaPlatformRate()) : agentDO.getAgentRate();
        }
        String taskId = commonService.buildIncr();
        // 主表
        StarTaskDO starTaskDO = new StarTaskDO();
        starTaskDO.setTaskId(taskId);
        starTaskDO.setUserId(loginBasicInfo.getUserId());
        starTaskDO.setAppletId(loginBasicInfo.getAppletId());
        starTaskDO.setIdentityId(identityId);
        starTaskDO.setTaskTitle(param.getTaskTitle());
        starTaskDO.setTaskHeadUrl(param.getTaskHeadUrl());
        starTaskDO.setTaskType(param.getTaskType());
        Integer recruitmentCycle = param.getRecruitmentCycle();
        starTaskDO.setRecruitmentCycle(recruitmentCycle);
        if (BooleanEnum.YES.getValue().equals(recruitmentCycle)) {
            starTaskDO.setApplyStartTime(DateUtil.parse(param.getApplyStartTime() + " 00:00:00", FsDateUtils.SIMPLE_DATETIME_FORMAT));
            starTaskDO.setApplyEndTime(DateUtil.parse(param.getApplyEndTime() + " 23:59:59", FsDateUtils.SIMPLE_DATETIME_FORMAT));
            starTaskDO.setTaskClosingTime(DateUtil.parse(param.getTaskClosingTime() + " 23:59:59", FsDateUtils.SIMPLE_DATETIME_FORMAT));
        }
        starTaskDO.setPublishUrl(param.getPublishUrl());
        starTaskDO.setCombo(param.getCombo());
        starTaskDO.setOtherRequirements(param.getOtherRequirements());
        starTaskDO.setMaterialAddress(param.getMaterialAddress());
        starTaskDO.setTaskTopic(param.getTaskTopic());
        starTaskDO.setReferenceTitle(param.getReferenceTitle());
        starTaskDO.setStoreName(param.getStoreName());
        starTaskDO.setProvince(param.getProvince());
        starTaskDO.setCity(param.getCity());
        starTaskDO.setArea(param.getArea());
        starTaskDO.setCommissionType(param.getCommissionType());
        starTaskDO.setCommissionRate(param.getCommissionRate());
        starTaskDO.setIsQuotaFull(QuotaFullEnum.NOT_FULL.getValue());
        starTaskDO.setIsStarAreaLimit(param.getIsStarAreaLimit());
        starTaskDO.setFansNumber(param.getFansNumber());
        starTaskDO.setTaskStatus(TaskStatusEnum.WAITING_FOR_PAYMENT.getValue());
        starTaskDO.setTotalAmount(param.getTotalAmount());
        starTaskDO.setPlatformRate(platformRate);
        starTaskDO.setAreaName(param.getAreaName());
        // 发布平台、门店电话、示例样图
        starTaskDO.setPlatform(param.getPlatform());
        starTaskDO.setStorePhone(param.getStorePhone());
        starTaskDO.setSampleUrl(JSON.toJSONString(param.getSampleUrl()));
        starTaskDO.setStorePoi(param.getStorePoi());
        if (Objects.nonNull(agentDO)) {
            starTaskDO.setAgentId(agentDO.getAgentId());
            starTaskDO.setAgentCity(identityDO.getCity());
            starTaskDO.setAgentProvince(identityDO.getProvince());
        }
        // 是否公开
        if (ObjectUtil.isNotNull(param.getPublicFlag())) {
            starTaskDO.setPublicFlag(param.getPublicFlag());
        }
        String storePhone = param.getStorePhone();
        if (StringUtils.isNotBlank(storePhone)) {
            starTaskDO.setStorePhone(FieldEncryptUtil.encode(storePhone));
        }
        // 区域限制
        List<StarTaskAreaLimitDO> areaList = CollectionUtil.newArrayList();
        if (CollectionUtil.isNotEmpty(param.getStarAreaList())) {
            for (PublishStarAreaParam publishStarAreaParam : param.getStarAreaList()) {
                StarTaskAreaLimitDO starTaskAreaLimitDO = new StarTaskAreaLimitDO();
                starTaskAreaLimitDO.setTaskId(taskId);
                starTaskAreaLimitDO.setStarProvince(publishStarAreaParam.getStarProvince());
                String starCity = publishStarAreaParam.getStarCity();
                starTaskAreaLimitDO.setStarCity(starCity);
                areaList.add(starTaskAreaLimitDO);
            }
        }
        List<PublishCommissionPlanParam> planList = param.getCommissionPlanList();
        if (CollectionUtil.isEmpty(planList)) {
            throw new CommonException(ErrorCodeEnum.TIKTOK_OPEN_ERROR).detailMessage("达人佣金信息为空");
        }
        BigDecimal maxTaskMoney = BigDecimal.ZERO;
        // 佣金计划
        List<StarTaskCommissionPlanDO> commissionPlanList = CollectionUtil.newArrayList();
        for (PublishCommissionPlanParam publishCommissionPlanParam : planList) {
            BigDecimal totalAmount = publishCommissionPlanParam.getTaskMoney();
            StarTaskCommissionPlanDO starTaskCommissionPlanDO = new StarTaskCommissionPlanDO();
            starTaskCommissionPlanDO.setTaskId(taskId);
            starTaskCommissionPlanDO.setCommissionPlanId(commonService.buildIncr());
            starTaskCommissionPlanDO.setTotalAmount(totalAmount);
            // 公式：总任务金-（总任务金*平台比例/100）保留2位小数
            BigDecimal taskMoney = totalAmount.subtract(totalAmount.multiply(platformRate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            starTaskCommissionPlanDO.setTaskMoney(taskMoney);
            starTaskCommissionPlanDO.setNeededCount(publishCommissionPlanParam.getNeededCount());
            starTaskCommissionPlanDO.setStarLevelLimit(publishCommissionPlanParam.getStarLevelLimit());
            if (taskMoney.compareTo(maxTaskMoney) > 0) {
                maxTaskMoney = taskMoney;
            }
            commissionPlanList.add(starTaskCommissionPlanDO);
        }
        // 最高任务金
        starTaskDO.setMaxTaskMoney(maxTaskMoney);
        LogUtil.info(log, "StarTaskMerchantMinaServiceImpl.publish >>>>> 发布任务,starTaskDO={}", JSONObject.toJSONString(starTaskDO));
        // 事物
        transactionTemplate.execute(status -> {
            starTaskDAO.saveStarTask(starTaskDO);
            if (BooleanEnum.YES.getValue().equals(param.getIsStarAreaLimit())) {
                starTaskAreaLimitDAO.batchStarTaskAreaLimit(areaList);
            }
            starTaskCommissionPlanDAO.batchStarTaskCommissionPlan(commissionPlanList);
            return Boolean.TRUE;
        });
        // 发送一个延时mq
        msgProducer.sendDelayMessage(ProduceEnum.STAR_TASK_PAY_TIME, taskId, 30 * 60 * 1000 + 5000);
        PublishStarTaskModel model = new PublishStarTaskModel();
        model.setTaskId(taskId);
        return model;
    }

    /**
     * 商家发布任务列表
     *
     * @param param
     * @return
     */
    @Override
    public PageResult<PublishTaskListModel> list(PageParam<PublishTaskListParam> param) {
        PageParam<PublishTaskListParamDTO> pageParam = starTaskMerchantMinaServiceObjMapper.toPublishTaskListParamDTOPage(param);
        // 登录态获取登录信息
        StarTaskMinaLoginModel basicInfo = LoginUtil.getStarTaskLoginBasicInfo(StarTaskIdentityTypeEnum.MERCHANT.getValue());
        PublishTaskListParamDTO query = pageParam.getQuery();
        query.setUserId(basicInfo.getUserId());
        query.setIdentityId(basicInfo.getIdentityId());
        Page<PublishTaskListResultDTO> resultDTOPage = starTaskDAO.findPublishTaskPage(pageParam);
        List<PublishTaskListResultDTO> records = resultDTOPage.getRecords();
        List<PublishTaskListModel> list = CollectionUtil.newArrayList();
        if (CollectionUtil.isNotEmpty(records)) {
            // 查询佣金计划信息
            List<String> taskIdList = records.stream().map(PublishTaskListResultDTO::getTaskId).collect(Collectors.toList());
            List<StarTaskApplyListDO> applyList = starTaskApplyListDAO.findApplyListByTaskIdList(taskIdList);
            Map<String, List<StarTaskApplyListDO>> applyMap = applyList.stream().collect(Collectors.groupingBy(StarTaskApplyListDO::getTaskId));
            List<StarTaskCommissionPlanDO> commissionPlanByTaskIds = starTaskCommissionPlanDAO.findCommissionPlanByTaskIdList(taskIdList);
            Map<String, List<StarTaskCommissionPlanDO>> commissionPlanMap = commissionPlanByTaskIds.stream().collect(Collectors.groupingBy(StarTaskCommissionPlanDO::getTaskId));
            for (PublishTaskListResultDTO record : records) {
                String taskId = record.getTaskId();
                PublishTaskListModel publishTaskListModel = starTaskMerchantMinaServiceObjMapper.toPublishTaskListModel(record);
                List<StarTaskCommissionPlanDO> commissionPlanDOList = commissionPlanMap.getOrDefault(taskId, CollectionUtil.newArrayList());
                // 报名进度
                if (CollectionUtil.isNotEmpty(commissionPlanDOList)) {
                    this.buildCommissionPlan(publishTaskListModel, commissionPlanDOList);
                }
                // 报名结束时间
                Date applyEndTime = record.getApplyEndTime();
                if (BooleanEnum.YES.getValue().equals(record.getRecruitmentCycle())) {
                    publishTaskListModel.setApplyEndTimeHour((Duration.between(Instant.now(), applyEndTime.toInstant()).toMinutes() + 59) / 60);
                }
                // 处理未到报名时间的状态
                if (BooleanEnum.YES.getValue().equals(record.getRecruitmentCycle()) && TaskStatusEnum.IN_PROGRESS.getValue().equals(record.getTaskStatus()) && record.getApplyStartTime().after(new Date())) {
                    publishTaskListModel.setTaskStatus(TaskStatusEnum.BEFORE_APPLY_TIME.getValue());
                }
                // 任务清单人数统计
                this.buildApply(applyMap, taskId, publishTaskListModel);
                // 区域限制
                if (BooleanEnum.YES.getValue().equals(record.getIsStarAreaLimit())) {
                    publishTaskListModel.setStarAreaName(starTaskCommonMinaService.findStarAreaNameList(taskId));
                }
                list.add(publishTaskListModel);
            }
        }
        PageResult<PublishTaskListModel> pageResult = new PageResult<>();
        pageResult.setRecords(list);
        pageResult.setCurrent(resultDTOPage.getCurrent());
        pageResult.setTotal(resultDTOPage.getTotal());
        pageResult.setSize(resultDTOPage.getSize());
        return pageResult;
    }

    /**
     * 任务列表详情
     *
     * @param param
     * @return
     */
    @Override
    public PublishTaskDetailModel detail(PublishTaskDetailParam param) {
        String taskId = param.getTaskId();
        StarTaskDO starTask = starTaskDAO.getStarTaskByTaskId(taskId);
        if (Objects.isNull(starTask)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务信息为空");
        }
        PublishTaskDetailModel model = starTaskMerchantMinaServiceObjMapper.toPublishTaskDetailModel(starTask);
        // 设置支付金额
        if (Objects.equals(TaskStatusEnum.WAITING_FOR_PAYMENT.getValue(), starTask.getTaskType())) {
            model.setCashierPayAmount(starTask.getTotalAmount().subtract(starTask.getFrozenBalanceAmount()));
        } else {
            model.setCashierPayAmount(starTask.getTotalAmount().subtract(starTask.getBalanceDeductionAmount()));
        }
        if (BooleanEnum.NO.getValue().equals(starTask.getRecruitmentCycle())) {
            model.setApplyStartTime(StringPool.EMPTY);
            model.setApplyEndTime(StringPool.EMPTY);
            model.setTaskClosingTime(StringPool.EMPTY);
        }

        // 获取区域限制
        if (BooleanEnum.YES.getValue().equals(starTask.getIsStarAreaLimit())) {
            model.setStarAreaName(starTaskCommonMinaService.findStarAreaNameList(taskId));
            List<StarTaskAreaLimitDO> starAreaList = starTaskAreaLimitDAO.findStarAreaList(taskId);
            List<PublishStarAreaModel> starAreaModelList = starTaskMerchantMinaServiceObjMapper.toPublishStarAreaModelList(starAreaList);
            List<AilikeGaodeCodeDO> provinceList = ailikeGaodeCodeDAO.findAllList();
            Map<String, String> orderRefundTypeMap = provinceList.stream().collect(Collectors.toMap(AilikeGaodeCodeDO::getAdcode, AilikeGaodeCodeDO::getAdname));
            for (PublishStarAreaModel item : starAreaModelList) {
                item.setStarCityName(orderRefundTypeMap.getOrDefault(item.getStarCity(), StringPool.EMPTY));
                item.setStarProvinceName(orderRefundTypeMap.getOrDefault(item.getStarProvince(), StringPool.EMPTY));
            }
            model.setStarAreaList(starAreaModelList);
        }
        // 获取佣金计划
        List<StarTaskCommissionPlanDO> commissionPlanList = starTaskCommissionPlanDAO.findCommissionPlanByTaskId(taskId);
        List<CommissionPlanModel> commissionPlanModelList = CollectionUtil.newArrayList();
        List<Integer> starLevelLimitList = CollectionUtil.newArrayList();
        for (StarTaskCommissionPlanDO starTaskCommissionPlanDO : commissionPlanList) {
            commissionPlanModelList.add(starTaskMerchantMinaServiceObjMapper.toCommissionPlanModel(starTaskCommissionPlanDO));
            starLevelLimitList.add(starTaskCommissionPlanDO.getStarLevelLimit());
        }
        model.setCommissionPlanList(commissionPlanModelList);
        model.setStarLevelLimitList(starLevelLimitList);
        // 处理未到报名时间的状态
        if (BooleanEnum.YES.getValue().equals(starTask.getRecruitmentCycle()) && TaskStatusEnum.IN_PROGRESS.getValue().equals(starTask.getTaskStatus()) && starTask.getApplyStartTime().after(new Date())) {
            model.setTaskStatus(TaskStatusEnum.BEFORE_APPLY_TIME.getValue());
        }
        if (StringUtils.isNotBlank(model.getStorePhone())) {
            model.setStorePhone(FieldEncryptUtil.decode(model.getStorePhone()));
        }
        // 参考样例的图片或视频地址
        if (StringUtils.isNotBlank(starTask.getSampleUrl())) {
            model.setSampleUrlList(JSON.parseArray(starTask.getSampleUrl(), String.class));
        }
        // 至高收益
        model.setParentInCome(starTask.getMaxTaskMoney().multiply(sysConfig.getStarTaskMinaStarDistributionRate()).divide(BigDecimal.valueOf(CommonConstant.INTEGER_HUNDRED)).setScale(CommonConstant.INTEGER_TWO, BigDecimal.ROUND_HALF_UP).toString());
        return model;
    }

    /**
     * 发布任务修改
     *
     * @param param
     */
    @Override
    public void update(UpdateStarTaskParam param) {
        // 幂等
        RLock lock = redissonClient.getLock(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_UPDATE_STAR_TASK, param.getTaskId()));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                LogUtil.error(log, "StarTaskMerchantMinaServiceImpl" + "." + "update" + " >>>>> " + "锁获取失败");
                throw new CommonException(ErrorCodeEnum.GET_LOCK_ERROR).detailMessage("StarTaskMerchantMinaServiceImpl" + " >>>>> " + "update" + "  锁获取失败");
            }
            // 更新操作
            this.updateStarTask(param);
        } catch (CommonException commonException) {
            throw commonException;
        } catch (Exception e) {
            LogUtil.error(log, "StarTaskMerchantMinaServiceImpl" + "." + "update" + " >>>>> " + "执行加锁方法失败", e);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("StarTaskMerchantMinaServiceImpl" + " >>>>> " + "update" + "  执行加锁方法失败");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.error(log, "StarTaskMerchantMinaServiceImpl" + " >>>>> " + "update" + "  锁释放失败", e);
            }
        }
    }

    /**
     * 发布任务操作
     *
     * @param param
     */
    @Override
    public void operate(OperateStarTaskParam param) {
        String taskId = param.getTaskId();
        OperateStarTaskTypeEnum typeEnum = OperateStarTaskTypeEnum.getByValue(param.getOperateType());
        if (Objects.isNull(typeEnum)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("操作类型不合法");
        }
        RLock lock = redissonClient.getLock(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_STAR_TASK, taskId));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                LogUtil.error(log, "StarTaskMerchantMinaServiceImpl" + "." + "operate" + " >>>>> " + "锁获取失败");
                throw new CommonException(ErrorCodeEnum.GET_LOCK_ERROR).detailMessage("StarTaskMerchantMinaServiceImpl" + " >>>>> " + "operate" + "  锁获取失败");
            }
            StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(taskId);
            if (Objects.isNull(starTaskDO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("发布任务信息为空");
            }
            Integer taskStatus = starTaskDO.getTaskStatus();
            Integer recruitmentCycle = starTaskDO.getRecruitmentCycle();
            switch (typeEnum) {
                case CANCEL_TASK:
                    // 待支付、平台审核中、平台审核驳回、未到报名时间支持取消任务
                    boolean f1 = TaskStatusEnum.WAITING_FOR_PAYMENT.getValue().equals(taskStatus) || TaskStatusEnum.PLATFORM_UNDER_REVIEW.getValue().equals(taskStatus) || TaskStatusEnum.PLATFORM_REJECTED.getValue().equals(taskStatus);
                    boolean f2 = BooleanEnum.YES.getValue().equals(recruitmentCycle) && TaskStatusEnum.IN_PROGRESS.getValue().equals(taskStatus) && starTaskDO.getApplyStartTime().after(new Date());
                    if (!f1 && !f2) {
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("操作失败，任务状态已改变");
                    }
                    String identityLock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_APPLY_WITHDRAWAL, starTaskDO.getIdentityId());
                    try (val redisLockHelper = new RedisLockHelper(identityLock, redissonClient)) {
                        redisLockHelper.tryLock();
                        // 事物
                        transactionTemplate.execute(status -> {
                            starTaskDAO.updateStarTaskStatus(taskId, TaskStatusEnum.CANCELED.getValue());
                            // 取消任务，返回金额
                            starTaskCommonService.cancelTaskToRefund(starTaskDO);
                            return Boolean.TRUE;
                        });
                    } catch (Exception e) {
                        LogUtil.error(log, "StarTaskMerchantMinaServiceImpl" + "." + "operate" + " >>>>> " + "执行加锁方法失败", e);
                        throw ExceptionUtil.toCommonException(e);
                    }
                    break;
                case END_TASK:
                    // 只有进行中（排除未到报名时间）才能结束报名
                    boolean f3 = TaskStatusEnum.IN_PROGRESS.getValue().equals(taskStatus) && BooleanEnum.YES.getValue().equals(recruitmentCycle) && starTaskDO.getApplyStartTime().before(new Date());
                    boolean f4 = TaskStatusEnum.IN_PROGRESS.getValue().equals(taskStatus) && BooleanEnum.NO.getValue().equals(recruitmentCycle);
                    if (!f3 && !f4) {
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("操作失败，任务状态已改变");
                    }

                    // 提前结束报名打标
                    starTaskDO.setAheadEndApplyFlag(BooleanEnum.YES.getValue());
                    starTaskDO.setAheadEndApplyTime(DateUtil.date());
                    starTaskDO.setTaskStatus(TaskStatusEnum.SETTLEMENT_IN_PROCESS.getValue());

                    // 如果任务为短期、且报名时间未开始，设置任务为取消操作
                    if (BooleanEnum.YES.getValue().equals(starTaskDO.getRecruitmentCycle()) && starTaskDO.getApplyStartTime().compareTo(DateUtil.date()) > 0) {
                        // 取消任务打表
                        starTaskDO.setCancelTime(DateUtil.date());
                        starTaskDO.setTaskStatus(TaskStatusEnum.CANCELED.getValue());
                        // 取消任务，返回金额
                        starTaskCommonService.cancelTaskToRefund(starTaskDO);
                    }

                    // 更新任务状态
                    starTaskDAO.updateStarTask(starTaskDO);

                    // 手动取消任务需要修改
                    //starTaskCommonService.updateStatusToCanceled(taskId, CommonConstant.INTEGER_TWO);
                    break;
                default:
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("操作类型不合法");
            }
        } catch (CommonException commonException) {
            throw commonException;
        } catch (Exception e) {
            LogUtil.error(log, "StarTaskMerchantMinaServiceImpl" + "." + "operate" + " >>>>> " + "执行加锁方法失败", e);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("StarTaskMerchantMinaServiceImpl" + " >>>>> " + "operate" + "  执行加锁方法失败");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.error(log, "StarTaskMerchantMinaServiceImpl" + " >>>>> " + "operate" + "  锁释放失败", e);
            }
        }
    }

    /**
     * 更新任务信息
     *
     * @param param
     */
    private void updateStarTask(UpdateStarTaskParam param) {
        String taskId = param.getTaskId();
        // 校验是否是平台审核驳回状态，只有平台审核驳回才可以修改
        StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(taskId);
        if (Objects.isNull(starTaskDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务信息为空");
        }
        if (!TaskStatusEnum.PLATFORM_REJECTED.getValue().equals(starTaskDO.getTaskStatus())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务信息状态已改变");
        }
        Integer fansNumber = param.getFansNumber();
        starTaskDO.setTaskStatus(TaskStatusEnum.PLATFORM_UNDER_REVIEW.getValue());
        starTaskDO.setTaskTitle(param.getTaskTitle());
        starTaskDO.setTaskHeadUrl(param.getTaskHeadUrl());
        starTaskDO.setOtherRequirements(param.getOtherRequirements());
        starTaskDO.setMaterialAddress(param.getMaterialAddress());
        starTaskDO.setCombo(param.getCombo());
        starTaskDO.setTaskTopic(param.getTaskTopic());
        starTaskDO.setReferenceTitle(param.getReferenceTitle());
        starTaskDO.setStoreName(param.getStoreName());
        starTaskDO.setProvince(param.getProvince());
        starTaskDO.setCity(param.getCity());
        starTaskDO.setArea(param.getArea());
        starTaskDO.setPublishUrl(param.getPublishUrl());
        starTaskDO.setIsStarAreaLimit(param.getIsStarAreaLimit());
        starTaskDO.setFansNumber(fansNumber);
        if (null == fansNumber) {
            starTaskDO.setFansNumber(CommonConstant.ZERO);
        }
        starTaskDO.setStorePoi(param.getStorePoi());
        String storePhone = param.getStorePhone();
        if (StringUtils.isNotBlank(storePhone)) {
            starTaskDO.setStorePhone(FieldEncryptUtil.encode(storePhone));
        }


        // 区域限制
        List<StarTaskAreaLimitDO> list = CollectionUtil.newArrayList();
        for (PublishStarAreaParam publishStarAreaParam : param.getStarAreaList()) {
            StarTaskAreaLimitDO starTaskAreaLimitDO = new StarTaskAreaLimitDO();
            starTaskAreaLimitDO.setTaskId(taskId);
            starTaskAreaLimitDO.setStarProvince(publishStarAreaParam.getStarProvince());
            String starCity = publishStarAreaParam.getStarCity();
            starTaskAreaLimitDO.setStarCity(starCity);
            list.add(starTaskAreaLimitDO);
        }

        // 事物
        transactionTemplate.execute(status -> {
            starTaskDAO.updateStarTask(starTaskDO);
            // 先删除在新增区域信息
            starTaskAreaLimitDAO.deleteStarTaskAreaLimitByTaskId(taskId);
            if (BooleanEnum.YES.getValue().equals(param.getIsStarAreaLimit())) {
                starTaskAreaLimitDAO.batchStarTaskAreaLimit(list);
            }
            return Boolean.TRUE;
        });
    }

    /**
     * 报名清单列表
     *
     * @param param
     * @return
     */
    @Override
    public PageResult<ApplyListModel> applyList(PageParam<ApplyListParam> param) {
        Page<ApplyListResultDTO> applyListPage = starTaskApplyListDAO.findApplyListPage(starTaskMerchantMinaServiceObjMapper.toApplyListParamDTOPage(param));
        PageResult<ApplyListModel> modelPage = starTaskMerchantMinaServiceObjMapper.toApplyListModelPage(applyListPage);
        for (ApplyListModel record : modelPage.getRecords()) {
            record.setStarPhoneNumber(FieldEncryptUtil.decode(record.getStarPhoneNumber()));
        }
        return modelPage;
    }

    /**
     * 报名清单操作
     *
     * @param param
     */
    @Override
    public void operateApplyList(OperateApplyListParam param) {
        OperateApplyListTypeEnum typeEnum = OperateApplyListTypeEnum.getByValue(param.getOperateType());
        if (Objects.isNull(typeEnum)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("操作类型不合法");
        }
        // 做幂等处理
        RLock lock = redissonClient.getLock(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_APPLY_LIST, param.getApplyId()));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                LogUtil.error(log, "StarTaskMerchantMinaServiceImpl" + "." + "operateApplyList" + " >>>>> " + "锁获取失败");
                throw new CommonException(ErrorCodeEnum.GET_LOCK_ERROR).detailMessage("StarTaskMerchantMinaServiceImpl" + " >>>>> " + "operateApplyList" + "  锁获取失败");
            }
            // 报名清单信息操作
            this.operateApplyListInfo(param, typeEnum);
        } catch (CommonException commonException) {
            throw commonException;
        } catch (Exception e) {
            LogUtil.error(log, "StarTaskMerchantMinaServiceImpl" + "." + "operateApplyList" + " >>>>> " + "执行加锁方法失败", e);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("StarTaskMerchantMinaServiceImpl" + " >>>>> " + "operateApplyList" + "  执行加锁方法失败");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.error(log, "StarTaskMerchantMinaServiceImpl" + " >>>>> " + "operateApplyList" + "  锁释放失败", e);
            }
        }
    }

    /**
     * 报名清单导出
     *
     * @param param
     * @return
     */
    @Override
    public ExportApplyListModel exportApplyList(ExportApplyListParam param) {
        // 数据查询
        PageParam<ApplyListParamDTO> pageParam = new PageParam<>();
        pageParam.setPage(1);
        pageParam.setPageSize(CommonConstant.EXPORT_MAX_COUNT);
        ApplyListParamDTO applyListParamDTO = new ApplyListParamDTO();
        applyListParamDTO.setTaskId(param.getTaskId());
        applyListParamDTO.setStatus(param.getStatus());
        pageParam.setQuery(applyListParamDTO);
        Page<ApplyListResultDTO> applyListPage = starTaskApplyListDAO.findApplyListPage(pageParam);
        List<ApplyListResultDTO> records = applyListPage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("导出数据为空");
        }
        String downloadFile = ossManager.downloadFile(ExcelConstant.TEMPLATE_APPLY_LIST_FILE_NAME, AliOssEnum.EXCEL_EXPORT_TEMPLATE);
        // 查询任务名称
        StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(param.getTaskId());
        if (Objects.isNull(starTaskDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务查询为空");
        }
        String fileName = starTaskDO.getTaskTitle() + StringPool.DASH + System.currentTimeMillis() + ".xlsx";
        List<ApplyListExportResult> list = CollectionUtil.newArrayList();
        List<String> userIds = records.stream().map(ApplyListResultDTO::getUserId).collect(Collectors.toList());
        List<StarTaskUserDO> userDOList = starTaskUserDao.findUserByUserIds(userIds);
        Map<String, StarTaskUserDO> map = userDOList.stream().collect(Collectors.toMap(StarTaskUserDO::getUserId, Function.identity(), (k, v) -> v));
        for (ApplyListResultDTO record : records) {
            ApplyListExportResult exportResult = starTaskMerchantMinaServiceObjMapper.toApplyListExportResult(record);
            // 等级
            exportResult.setLevel("Lv" + record.getStarLevel());
            if (PlatformTypeEnum.KS.getValue().equals(record.getPlatform())) {
                exportResult.setLevel(StringPool.EMPTY);
            }
            // 状态
            ApplyListStatusEnum statusEnum = ApplyListStatusEnum.getByValue(record.getApplyStatus());
            exportResult.setStatus(Objects.isNull(statusEnum) ? StringPool.EMPTY : statusEnum.getName());
            // 手机号
            StarTaskUserDO starTaskUserDO = map.getOrDefault(record.getUserId(), new StarTaskUserDO());
            exportResult.setPhoneNumber(FieldEncryptUtil.decode(starTaskUserDO.getPhoneNumber()));
            list.add(exportResult);
        }
        try (ExcelWriter excelWriter = EasyExcel.write(fileName).withTemplate(downloadFile).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
            excelWriter.fill(list, fillConfig, writeSheet);
        }
        File excelFile = new File(downloadFile);
        if (!excelFile.delete()) {
            log.error("delete" + fileName + "error");
        }
        String fileUrl = commonService.uploadExcelFile(fileName);
        ExportApplyListModel model = new ExportApplyListModel();
        model.setUrl(commonService.httpToHttps(fileUrl));
        return model;
    }

    /**
     * 余额支付
     *
     * @param param
     * @deprecated 已过时，使用{@link #payment}代替
     */
    @Deprecated
    @Override
    public void balancePayment(BalancePaymentParam param) {
        RLock lock = redissonClient.getLock(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_STAR_TASK, param.getTaskId()));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                LogUtil.error(log, "StarTaskMerchantMinaServiceImpl" + "." + "balancePayment" + " >>>>> " + "锁获取失败");
                throw new CommonException(ErrorCodeEnum.GET_LOCK_ERROR).detailMessage("StarTaskMerchantMinaServiceImpl" + " >>>>> " + "balancePayment" + "  锁获取失败");
            }
            StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(param.getTaskId());
            if (Objects.isNull(starTaskDO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("查询任务为空");
            }
            // 余额支付
            this.balancePaymentDetail(param, starTaskDO, LoginUtil.getStarTaskLoginBasicInfo(StarTaskIdentityTypeEnum.MERCHANT.getValue()));
        } catch (CommonException commonException) {
            throw commonException;
        } catch (Exception e) {
            LogUtil.error(log, "StarTaskMerchantMinaServiceImpl" + "." + "balancePayment" + " >>>>> " + "执行加锁方法失败", e);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("StarTaskMerchantMinaServiceImpl" + " >>>>> " + "balancePayment" + "  执行加锁方法失败");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.error(log, "StarTaskMerchantMinaServiceImpl" + " >>>>> " + "balancePayment" + "  锁释放失败", e);
            }
        }
    }

    /**
     * 余额支付 / 组合支付
     *
     * @param param 参数
     * @return 支付参数
     */
    @Override
    public PaymentModel payment(StarTaskMinaPaymentParam param) {
        LogUtil.info(log, "StarTaskMerchantMinaServiceImpl.payment >> 支付开始, 参数:{}", JSONObject.toJSONString(param));
        val taskId = param.getTaskId();
        // 登录态获取数据
        val loginInfo = LoginUtil.getStarTaskLoginBasicInfo(StarTaskIdentityTypeEnum.MERCHANT.getValue());
        val operateTaskKey = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_STAR_TASK, taskId);
        try (val redisHelper = new RedisLockHelper(operateTaskKey, redissonClient)) {
            redisHelper.tryLock();
            val starTaskDO = starTaskDAO.getStarTaskByTaskId(taskId);
            if (!TaskStatusEnum.WAITING_FOR_PAYMENT.getValue().equals(starTaskDO.getTaskStatus())) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("参数错误，任务状态不为待支付");
            }
            if (param.getTotalAmount().compareTo(starTaskDO.getTotalAmount()) != 0) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("参数错误，总金额不匹配");
            }

            return routePayment(param, starTaskDO, loginInfo);
        } catch (Exception e) {
            LogUtil.warn(log, "StarTaskMerchantMinaServiceImpl.payment >> 支付失败, 错误:", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }

    /**
     * 查询任务金额
     *
     * @param param 入参
     * @return 任务金限制
     */
    @Override
    public QueryTaskMoneyModel queryTaskMoney(QueryTaskMoneyParam param) {
        StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(param.getIdentityId());
        if (null == identityDO) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息异常");
        }
        QueryTaskMoneyModel model = new QueryTaskMoneyModel();
        if (CommonConstant.INTEGER_ONE.equals(identityDO.getTaskMoneyType())) {
            model.setMinTaskMoney(new BigDecimal(sysConfig.getStarTaskMinaMinTaskMoney()));
            model.setPlatformRate(new BigDecimal(sysConfig.getStarTaskMinaPlatformRate()));
            return model;
        }
        model.setMinTaskMoney(identityDO.getMinTaskMoney());
        model.setPlatformRate(identityDO.getPlatformRate());
        return model;
    }

    /**
     * 查询商家信息
     *
     * @param param 入参
     * @return 出参
     */
    @Override
    public QueryMerchantInfoModel queryMerchantInfo(QueryMerchantInfoParam param) {
        StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(param.getIdentityId());
        if (null == identityDO || !StarTaskIdentityTypeEnum.MERCHANT.getValue().equals(identityDO.getIdentityType())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息有误，请刷新后再试");
        }
        return buildMerchantInfoModel(identityDO);
    }

    /**
     * 构建商家信息model
     *
     * @param identityDO 身份信息
     * @return 出商家身份信息model
     */
    private QueryMerchantInfoModel buildMerchantInfoModel(StarTaskIdentityDO identityDO) {
        QueryMerchantInfoModel model = starTaskMerchantMinaServiceObjMapper.toQueryMerchantInfoModel(identityDO);
        String province = model.getProvince();
        String city = model.getCity();
        String area = model.getArea();
        List<String> adCodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(province)) {
            adCodeList.add(province);
        }
        if (StringUtils.isNotBlank(city)) {
            adCodeList.add(city);
        }
        if (StringUtils.isNotBlank(area)) {
            adCodeList.add(area);
        }
        if (CollectionUtil.isNotEmpty(adCodeList)) {
            List<AilikeGaodeCodeDO> gaodeCodeList = ailikeGaodeCodeDAO.findCodeInfoList(adCodeList);
            Map<String, String> gaodeCodeMap = gaodeCodeList.stream().collect(Collectors.toMap(AilikeGaodeCodeDO::getAdcode, AilikeGaodeCodeDO::getAdname));
            model.setProvinceName(gaodeCodeMap.getOrDefault(province, StrUtil.EMPTY));
            model.setCityName(gaodeCodeMap.getOrDefault(city, StrUtil.EMPTY));
            model.setAreaName(gaodeCodeMap.getOrDefault(area, StrUtil.EMPTY));
        }
        return model;
    }

    /**
     * 查询商家类目列表
     *
     * @return 出参
     */
    @Override
    public FindMerchantCategoryListModel findMerchantCategoryList() {
        List<String> categoryList = JSONObject.parseArray(sysConfig.getStarTaskMinaMerchantCategoryList(), String.class);
        return new FindMerchantCategoryListModel(categoryList);
    }

    /**
     * 更新商家信息
     *
     * @param param 入参
     */
    @Override
    public void updateMerchantInfo(UpdateMerchantInfoParam param) {
        String identityId = param.getIdentityId();
        StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(identityId);
        if (null == identityDO || !StarTaskIdentityTypeEnum.MERCHANT.getValue().equals(identityDO.getIdentityType())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息有误，请刷新后再试");
        }
        if (StringUtils.isNotBlank(param.getMerchantName())) {
            identityDO.setMerchantName(param.getMerchantName());
        }
        if (StringUtils.isNotBlank(param.getCategoryName())) {
            identityDO.setCategoryName(param.getCategoryName());
        }
        if (StringUtils.isNotBlank(param.getProvince())) {
            identityDO.setProvince(param.getProvince());
        }
        if (StringUtils.isNotBlank(param.getCity())) {
            if (!param.getCity().equals(identityDO.getCity())) {
                identityDO.setAreaUpdateTime((int) DateUtil.currentSeconds());
            }
            identityDO.setCity(param.getCity());
        }
        identityDO.setUpdateTime(null);
        identityDO.setArea(param.getArea());
        starTaskIdentityDAO.updateById(identityDO);
    }

    /**
     * 检查手机号是否在白名单中
     *
     * @param phoneNumber 手机号
     * @return 是否在白名单中
     */
    @Override
    public Boolean checkAreaPhoneWhiteList(String phoneNumber) {
        LogUtil.info(log, "StarTaskMerchantMinaServiceImpl.checkAreaPhoneWhiteList >> 接口开始 >> phoneNumber = {}", phoneNumber);
        List<String> phoneList = JSONObject.parseArray(sysConfig.getStarTaskMinaAreaWhiteList(), String.class);
        return phoneList.contains(phoneNumber);
    }

    /**
     * 余额校验
     *
     * @param param      支付参数
     * @param starTaskDO 任务记录
     * @param loginInfo  登录态
     */
    private PaymentModel routePayment(StarTaskMinaPaymentParam param, StarTaskDO starTaskDO, StarTaskMinaLoginModel loginInfo) {
        // 任务总金额
        val totalAmount = param.getTotalAmount();
        val key = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_APPLY_WITHDRAWAL, loginInfo.getIdentityId());
        try (val redisLocker = new RedisLockHelper(key, redissonClient)) {
            redisLocker.tryLock();
            val paymentModel = new PaymentModel();
            // 查询是否有冻结的余额
            val frozenBalanceLog = starTaskBalanceLogDAO.queryLastByRelationNo(BalanceLogRemarkTypeEnum.BALANCE_FROZEN.getValue(), param.getTaskId());

            // 之前冻结的余额金额
            // 获得任务中的冻结金额
            BigDecimal frozenBalanceAmount = starTaskDO.getFrozenBalanceAmount();
            if (frozenBalanceAmount.compareTo(BigDecimal.ZERO) > 0) {
                if (Objects.isNull(frozenBalanceLog)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("未找到冻结余额记录")
                            .extra("未找到余额冻结记录.. 任务Id:{}, 冻结金额:{}", starTaskDO.getTaskId(), starTaskDO.getFrozenBalanceAmount());
                }

                // 冻结记录存在，且冻结金额不一致
                if (!Objects.equals(starTaskDO.getFrozenBalanceAmount(), frozenBalanceLog.getFrozenAmount())) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("余额状态检验失败")
                            .extra("余额状态检验失败，任务冻结余额和余额记录中冻结金额不一致，任务Id:{}, 任务余额:{}, 余额记录:{}", starTaskDO.getTaskId(), starTaskDO.getFrozenBalanceAmount(), frozenBalanceLog.getFrozenAmount());
                }
            }
            // 是否有冻结金额
            boolean hasFrozenAmount = frozenBalanceAmount.compareTo(BigDecimal.ZERO) > 0;

            // ! 剩余支付金额 = 总金额 - 已经冻结金额
            val remainAmount = totalAmount.subtract(frozenBalanceAmount);
            if (remainAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("发布任务总金额错误，请关闭任务后重新创建");
            }
            // 获得余额
            val balance = starTaskBalanceAccountDAO.getBalanceAccountByIdentityId(loginInfo.getIdentityId());

            // * >> 余额支付
            // * 没有冻结金额，并且余额金是否大于支付金额
            if (!hasFrozenAmount && balance.getAvailableBalance().compareTo(totalAmount) >= 0) {
                // 走余额支付逻辑
                val balancePaymentParam = new BalancePaymentParam().setTaskId(starTaskDO.getTaskId()).setTotalAmount(totalAmount);
                val balancePayModel = balancePaymentDetail(balancePaymentParam, starTaskDO, loginInfo);
                paymentModel.setBalance(balancePayModel);
                paymentModel.setType(StarTaskPaymentTypeEnum.BALANCE.getValue());

                // * >> 混合支付
                // * 余额金额小于应支付金额
            } else {
                // 获得应支付的金额
                final BigDecimal[] payAmount = {remainAmount};

                // 获得支付订单号: 正式环境为P，Beta环境为B 用于区分环境
                val rechargeOrderSn = BizNoBuildUtil.buildV2(sysConfig.isProd() ? "P" : "B", StarTaskConstant.LAI_TAN_BEI_PAY);

                // 当前已经有了冻结记录，则本次不会再冻结，仅更新充值单号
                if (hasFrozenAmount) {
                    // 仅更新支付订单信息
                    frozenBalanceLog.setRechargeOrderSn(rechargeOrderSn);
                    frozenBalanceLog.setUpdateTime(new Date());
                    starTaskBalanceLogDAO.updateById(frozenBalanceLog);

                    // 当前没有冻结记录，冻结所有可用余额
                } else {
                    // 如果当前余额大于0，进行冻结
                    if (balance.getAvailableBalance().compareTo(BigDecimal.ZERO) > 0) {
                        // 对当前所有余额进行冻结
                        transactionTemplate.execute(status -> {
                            val balanceAccountForUpdate = starTaskBalanceAccountDAO.getBalanceAccountForUpdate(loginInfo.getIdentityId());
                            // 即将冻结的余额
                            val frozenBalance = balanceAccountForUpdate.getAvailableBalance();
                            val afterExpenditureTotalIncome = balanceAccountForUpdate.getExpenditureTotalIncome().add(totalAmount);
                            val balanceLogDO = new StarTaskBalanceLogDO();
                            balanceLogDO.setUserId(loginInfo.getUserId());
                            balanceLogDO.setIdentityId(loginInfo.getIdentityId());
                            balanceLogDO.setChangeType(BalanceLogChangeTypeEnum.REDUCED.getValue());
                            balanceLogDO.setRemarkType(BalanceLogRemarkTypeEnum.BALANCE_FROZEN.getValue());
                            balanceLogDO.setChangeRemark("余额冻结" + StringPool.DASH + starTaskDO.getTaskTitle());
                            balanceLogDO.setChangeAmount(frozenBalance);
                            balanceLogDO.setChangeAvailableBalance(frozenBalance);
                            balanceLogDO.setAfterAvailableBalance(BigDecimal.ZERO);
                            balanceLogDO.setChangeExpenditureTotalIncome(balanceAccountForUpdate.getExpenditureTotalIncome());
                            balanceLogDO.setAfterExpenditureTotalIncome(afterExpenditureTotalIncome);
                            balanceLogDO.setChangeIncomeTotalIncome(balanceAccountForUpdate.getIncomeTotalIncome());
                            balanceLogDO.setAfterIncomeTotalIncome(balanceAccountForUpdate.getIncomeTotalIncome());
                            balanceLogDO.setChangeWithdrawalFrozenBalance(balanceAccountForUpdate.getWithdrawalFrozenBalance());
                            balanceLogDO.setAfterWithdrawalFrozenBalance(balanceAccountForUpdate.getWithdrawalFrozenBalance());
                            balanceLogDO.setRelationNumber(starTaskDO.getTaskId());
                            balanceLogDO.setFrozenAmount(frozenBalance);
                            balanceLogDO.setRechargeOrderSn(rechargeOrderSn);
                            balanceLogDO.setOperatorId(StarTaskPresetOperatorEnum.SYSTEM.getOperatorId());
                            balanceLogDO.setOperatorName(StarTaskPresetOperatorEnum.SYSTEM.getName());
                            starTaskBalanceLogDAO.saveBalanceLog(balanceLogDO);

                            balanceAccountForUpdate.setAvailableBalance(BigDecimal.ZERO);
                            balanceAccountForUpdate.setExpenditureTotalIncome(afterExpenditureTotalIncome);
                            balanceAccountForUpdate.setUpdateTime(new Date());
                            // 更新余额
                            starTaskBalanceAccountDAO.updateById(balanceAccountForUpdate);

                            // 设置任务记录中的冻结余额
                            starTaskDO.setFrozenBalanceAmount(frozenBalance);
                            starTaskDO.setUpdateTime(new Date());
                            starTaskDAO.updateById(starTaskDO);

                            // 获得剩余支付的金额
                            payAmount[0] = totalAmount.subtract(frozenBalance);
                            return Boolean.TRUE;
                        });
                        val balancePayModel = new BalancePayModel();
                        balancePayModel.setDeductionAmount(balance.getAvailableBalance());
                        paymentModel.setBalance(balancePayModel);
                    }
                }
                // 取消所有未支付的订单
                val unpaidOrderParam = new StarTaskMinaCloseUnpaidOrderParam();
                unpaidOrderParam.setTaskId(starTaskDO.getTaskId());
                val excludeOrders = CollectionUtil.newArrayList(rechargeOrderSn);
                unpaidOrderParam.setExcludeOrderSn(excludeOrders);
                starTaskWechatMinaService.asyncCloseUnpaidOrder(unpaidOrderParam);

                val prepayParam = starTaskStarMinaServiceObjMapper.toStarTaskMinaPrepayParam(param);
                prepayParam.setPayAmount(payAmount[0]);
                prepayParam.setOrderSn(rechargeOrderSn);
                val prePayModel = starTaskWechatMinaService.prepay(prepayParam);
                paymentModel.setPrepay(prePayModel);
                paymentModel.setType(StarTaskPaymentTypeEnum.WECHAT.getValue());
            }
            return paymentModel;
        } catch (Exception ex) {
            LogUtil.warn(log, "StarTaskMerchantMinaServiceImpl.routePayment >> 支付失败, 错误原因: ", ex);
            throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage(ex.getMessage());
        }
    }

    /**
     * 余额支付
     *
     * @param param      支付参数
     * @param starTaskDO 任务
     * @param loginInfo  获取账号登录态信息
     * @return 余额抵扣金额
     */
    private BalancePayModel balancePaymentDetail(BalancePaymentParam param, StarTaskDO starTaskDO, StarTaskMinaLoginModel loginInfo) {
        String taskId = param.getTaskId();
        // 登录态获取数据
        String identityId = loginInfo.getIdentityId();
        // 剩余支付余额
        BigDecimal remainAmount = param.getTotalAmount();
        // 事物
        transactionTemplate.execute(status -> {
            // 查询数据库数据
            StarTaskBalanceAccountDO balanceAccount = starTaskBalanceAccountDAO.getBalanceAccountForUpdate(identityId);
            if (Objects.isNull(balanceAccount)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账户信息不存在");
            }
            // 判断余额是否足够
            if (balanceAccount.getAvailableBalance().compareTo(remainAmount) < 0) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("余额不足");
            }
            // 扣除余额
            starTaskBalanceAccountDAO.reduceAvailableBalance(identityId, remainAmount);
            // 修改任务状态
            // starTaskDAO.updateStarTaskStatus(taskId, TaskStatusEnum.PLATFORM_UNDER_REVIEW.getValue());
            // 设置状态
            starTaskDO.setTaskStatus(TaskStatusEnum.PLATFORM_UNDER_REVIEW.getValue());
            // 设置余额抵扣金额
            starTaskDO.setBalanceDeductionAmount(remainAmount);
            starTaskDO.setUpdateTime(new Date());
            starTaskDAO.updateById(starTaskDO);

            // 新增打款纪录表
            StarTaskBalanceLogDO balanceLogDO = new StarTaskBalanceLogDO();
            balanceLogDO.setUserId(loginInfo.getUserId());
            balanceLogDO.setIdentityId(identityId);
            balanceLogDO.setChangeType(BalanceLogChangeTypeEnum.REDUCED.getValue());
            balanceLogDO.setRemarkType(BalanceLogRemarkTypeEnum.BALANCE_PAY.getValue());
            balanceLogDO.setChangeRemark("支付任务金" + StringPool.DASH + starTaskDO.getTaskTitle());
            // 余额变动，加上之前的冻结余额
            balanceLogDO.setChangeAmount(remainAmount);
            balanceLogDO.setChangeAvailableBalance(balanceAccount.getAvailableBalance());
            balanceLogDO.setAfterAvailableBalance(balanceAccount.getAvailableBalance().subtract(remainAmount));
            // 支出累计，加上之前的冻结金额
            balanceLogDO.setChangeExpenditureTotalIncome(balanceAccount.getExpenditureTotalIncome());
            balanceLogDO.setAfterExpenditureTotalIncome(balanceAccount.getExpenditureTotalIncome().add(remainAmount));

            balanceLogDO.setChangeIncomeTotalIncome(balanceAccount.getIncomeTotalIncome());
            balanceLogDO.setAfterIncomeTotalIncome(balanceAccount.getIncomeTotalIncome());
            balanceLogDO.setChangeWithdrawalFrozenBalance(balanceAccount.getWithdrawalFrozenBalance());
            balanceLogDO.setAfterWithdrawalFrozenBalance(balanceAccount.getWithdrawalFrozenBalance());
            balanceLogDO.setRelationNumber(taskId);
            balanceLogDO.setOperatorId(StarTaskPresetOperatorEnum.MERCHANT.getOperatorId());
            balanceLogDO.setOperatorName(StarTaskPresetOperatorEnum.MERCHANT.getName());
            starTaskBalanceLogDAO.saveBalanceLog(balanceLogDO);
            return Boolean.TRUE;
        });
        // 发送一个钉钉消息
        val message = StrUtil.format("有新发布的任务支付成功，请及时审核。任务Id: {}, 任务名称: {}", starTaskDO.getTaskId(), starTaskDO.getTaskTitle());
        dingDingCommonService.sendStarTaskDingDingAudit("新任务发布", message, "");


        val balancePayModel = new BalancePayModel();
        balancePayModel.setDeductionAmount(remainAmount);
        return balancePayModel;
    }

    /**
     * 报名清单信息操作
     *
     * @param param
     * @param typeEnum
     */
    private void operateApplyListInfo(OperateApplyListParam param, OperateApplyListTypeEnum typeEnum) {
        // 查询信息
        StarTaskApplyListDO applyList = starTaskApplyListDAO.getApplyListByApplyId(param.getApplyId());
        if (Objects.isNull(applyList)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("报名清单为空");
        }
        Integer applyStatus = applyList.getApplyStatus();
        // 判断状态是否是链接审核、平台审核、商家前置审核
        if (!ApplyListStatusEnum.LINK_AUDIT.getValue().equals(applyStatus)
                && !ApplyListStatusEnum.PLATFORM_AUDIT.getValue().equals(applyStatus)
                && !ApplyListStatusEnum.MERCHANT_PRE_AUDIT.getValue().equals(applyStatus)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("报名清单状态异常");
        }
        StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(applyList.getTaskId());
        switch (typeEnum) {
            case APPROVE:
                // 根据任务id查询任务信息
                // 根据报名id更新报名状态为已完成，直接发送任务金给达人
                applyList.setApplyStatus(ApplyListStatusEnum.COMPLETED.getValue());
                String identityLock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_APPLY_WITHDRAWAL, applyList.getIdentityId());
                if (Objects.isNull(starTaskDO)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("商家任务信息不存在");
                }
                // 查询达人佣金计划列表
                StarTaskCommissionPlanDO commissionPlanDO = starTaskCommissionPlanDAO.getCommissionPlanByCommissionPlanId(applyList.getCommissionPlanId());
                if (Objects.isNull(commissionPlanDO)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("达人佣金信息为空");
                }
                // 如果达人报名的状态为商家待审核，则修改为待提交状态、其它状态修改为完成状态
                if (ApplyListStatusEnum.MERCHANT_PRE_AUDIT.getValue().equals(applyStatus)) {
                    // 判断任务是否已达上线
                    if (QuotaFullEnum.IS_FULL.getValue().equals(starTaskDO.getIsQuotaFull())) {
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("当前任务已达上限");
                    }
                    // 判断报名的单个计划是否达上线
                    if (commissionPlanDO.getParticipationCount() + 1 > commissionPlanDO.getNeededCount()) {
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("当前计划报名已达上限");
                    }
                    commissionPlanDO.setParticipationCount(commissionPlanDO.getParticipationCount() + 1);
                    commissionPlanDO.setUpdateTime(new Date());

                    // 计算总任务是否满员
                    // 统计任务的所有的佣金计划
                    List<StarTaskCommissionPlanDO> commissionPlanList = starTaskCommissionPlanDAO.findCommissionPlanByTaskId(starTaskDO.getTaskId());
                    if (CollectionUtil.isEmpty(commissionPlanList)) {
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("达人佣金信息为空");
                    }
                    // 剩余名额,如剩余1个 设置任务为满员
                    int sum = commissionPlanList.stream().mapToInt(plan -> plan.getNeededCount() - plan.getParticipationCount()).sum();
                    if (CommonConstant.INTEGER_ONE.equals(sum)) {
                        starTaskDO.setIsQuotaFull(QuotaFullEnum.IS_FULL.getValue());
                        starTaskDO.setUpdateTime(new Date());
                    }
                    applyList.setApplyStatus(ApplyListStatusEnum.WAITING_SUBMIT.getValue());
                } else {
                    // 根据报名id更新报名状态为已完成，直接发送任务金给达人
                    applyList.setApplyStatus(ApplyListStatusEnum.COMPLETED.getValue());
                }

                try (val redisLockHelper = new RedisLockHelper(identityLock, redissonClient)) {
                    redisLockHelper.tryLock();
                    // 事物
                    transactionTemplate.execute(status -> {
                        starTaskCommissionPlanDAO.updateById(commissionPlanDO);
                        starTaskDAO.updateById(starTaskDO);
                        starTaskApplyListDAO.updateApplyList(applyList);

                        //只有任务完成的时候才修改达人的余额
                        if (applyList.getApplyStatus().equals(ApplyListStatusEnum.COMPLETED.getValue())) {
                            starTaskCommonService.updateTaskMoney(applyList.getIdentityId(), applyList, starTaskDO, String.valueOf(StarTaskPresetOperatorEnum.SYSTEM.getOperatorId()), StarTaskPresetOperatorEnum.SYSTEM.getName());
                        }
                        return Boolean.TRUE;
                    });
                } catch (Exception e) {
                    LogUtil.error(log, "StarTaskMerchantMinaServiceImpl" + "." + "operateApplyListInfo" + " >>>>> " + "执行加锁方法失败", e);
                    throw ExceptionUtil.toCommonException(e);
                }
                // 分销佣金处理
                if (applyList.getApplyStatus().equals(ApplyListStatusEnum.COMPLETED.getValue())) {
                    // 查询是否存在上级团长
                    StarTaskIdentityRelationDO identityRelationDO = starTaskIdentityRelationDAO.findListByIdentityId(applyList.getIdentityId());
                    // 存在，团长需要更新余额，增加一条返还奖励明细
                    if (Objects.nonNull(identityRelationDO) && StringUtils.isNotBlank(identityRelationDO.getParentId())) {
                        starTaskCommonService.buildStarReturnReward(applyList, starTaskDO, identityRelationDO);
                    }
                }
                // 统计达人量，如果完成就设置任务为完成状态
                starTaskJobService.statisticsTaskIsFinish(applyList.getTaskId());
                break;
            case REJECT:
                if (ApplyListStatusEnum.MERCHANT_PRE_AUDIT.getValue().equals(applyStatus)) {
                    // 如果是前置审核设置为 前置审核驳回
                    applyList.setApplyStatus(ApplyListStatusEnum.MERCHANT_PRE_REFUND.getValue());
                } else {
                    // 根据报名id更新报名状态为已驳回，并且删除关联链接，更新驳回理由
                    applyList.setApplyStatus(ApplyListStatusEnum.REJECTED.getValue());
                }
                applyList.setRejectReason(param.getRejectReason());
                starTaskApplyListDAO.updateApplyList(applyList);
                break;
            default:
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("操作类型不存在");
        }
        List<String> templateDataList = new ArrayList<>();
        templateDataList.add(starTaskDO.getTaskTitle().length() > CommonConstant.INTEGER_TWENTY ? starTaskDO.getTaskTitle().substring(0, CommonConstant.INTEGER_SEVENTEEN) + StringPool.ELLIPSIS : starTaskDO.getTaskTitle());
        templateDataList.add(typeEnum.equals(OperateApplyListTypeEnum.APPROVE) ? OperateApplyListTypeEnum.APPROVE.getName() : OperateApplyListTypeEnum.REJECT.getName());
        templateDataList.add(DateUtil.format(new Date(), FsDateUtils.SIMPLE_DATETIME_FORMAT));
        templateDataList.add(typeEnum.equals(OperateApplyListTypeEnum.APPROVE) ? "您提交的作品已通过审核，快来查看吧" : (param.getRejectReason().length() > CommonConstant.INTEGER_TWENTY ? param.getRejectReason().substring(0, CommonConstant.INTEGER_SEVENTEEN) + StringPool.ELLIPSIS : param.getRejectReason()));
        subscribeMessageService.sendSubscribeMessage(applyList.getApplyId(), starTaskDO.getAppletId(), sysConfig.getWxSubscribeMessageTaskAuditResult(), "master/taskSquare/index?active=masterApplyList&enterScene=1", templateDataList, SubscribeMessageTypeEnum.SUBMIT_LINK.getValue());
    }

    /**
     * 任务清单人数统计
     *
     * @param applyMap
     * @param taskId
     * @param publishTaskListModel
     */
    private void buildApply(Map<String, List<StarTaskApplyListDO>> applyMap, String taskId, PublishTaskListModel publishTaskListModel) {
        int applyNumber = CommonConstant.ZERO;
        int linkAuditNumber = CommonConstant.ZERO;
        int finishedNumber = CommonConstant.ZERO;
        List<StarTaskApplyListDO> applyListDOList = applyMap.getOrDefault(taskId, CollectionUtil.newArrayList());
        for (StarTaskApplyListDO starTaskApplyListDO : applyListDOList) {
            Integer applyStatus = starTaskApplyListDO.getApplyStatus();
            if (!ApplyListStatusEnum.CANCELLED.getValue().equals(applyStatus)) {
                applyNumber += 1;
            }
            if (ApplyListStatusEnum.LINK_AUDIT.getValue().equals(applyStatus) || ApplyListStatusEnum.PLATFORM_AUDIT.getValue().equals(applyStatus)) {
                linkAuditNumber += 1;
            }
            if (ApplyListStatusEnum.COMPLETED.getValue().equals(applyStatus) || ApplyListStatusEnum.PLATFORM_COMPLETED.getValue().equals(applyStatus)) {
                finishedNumber += 1;
            }
        }
        publishTaskListModel.setApplyNumber(applyNumber);
        publishTaskListModel.setLinkAuditNumber(linkAuditNumber);
        publishTaskListModel.setFinishedNumber(finishedNumber);
    }

    /**
     * 构建佣金计划
     *
     * @param publishTaskListModel
     * @param commissionPlanDOList
     */
    private void buildCommissionPlan(PublishTaskListModel publishTaskListModel, List<StarTaskCommissionPlanDO> commissionPlanDOList) {
        Integer totalParticipationCount = CommonConstant.ZERO;
        Integer totalNeededCount = CommonConstant.ZERO;
        List<Integer> starLevelLimitList = CollectionUtil.newArrayList();
        List<CommissionPlanModel> commissionPlanList = CollectionUtil.newArrayList();
        for (StarTaskCommissionPlanDO starTaskCommissionPlanDO : commissionPlanDOList) {
            totalParticipationCount += starTaskCommissionPlanDO.getParticipationCount();
            totalNeededCount += starTaskCommissionPlanDO.getNeededCount();
            starLevelLimitList.add(starTaskCommissionPlanDO.getStarLevelLimit());
            commissionPlanList.add(starTaskMerchantMinaServiceObjMapper.toCommissionPlanModel(starTaskCommissionPlanDO));
        }
        publishTaskListModel.setTotalParticipationCount(totalParticipationCount);
        publishTaskListModel.setTotalNeededCount(totalNeededCount);
        publishTaskListModel.setStarLevelLimitList(starLevelLimitList);
        publishTaskListModel.setCommissionPlanList(commissionPlanList);
    }
}