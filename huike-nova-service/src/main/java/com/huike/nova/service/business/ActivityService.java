/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.activity.*;
import com.huike.nova.service.domain.model.tiktokopen.TiktokTopicListModel;
import com.huike.nova.service.domain.model.videocheck.SynthesisRateModel;
import com.huike.nova.service.domain.param.activity.*;
import com.huike.nova.service.domain.param.materialgroup.GetMaterialGroupCountParam;
import com.huike.nova.service.domain.param.videoCheck.SynthesisRateParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version TpHkActivityService.java, v 0.1 2022-08-31 16:35 zhangling
 */
public interface ActivityService {

    /**
     * 根据活动ID获取活动下素材数量
     *
     * @param activityId
     * @return
     */
    ActivityDetailModel getActivityByActivityId(String activityId);


    /**
     * 新建活动
     *
     * @param param
     * @return
     */
    ActivitySaveModel createActivity(ActivityCreateParam param);

    /**
     * 关闭活动
     *
     * @param activityId
     * @param activityStatus
     * @return
     */
    boolean closeActivity(String activityId, Integer activityStatus);

    /**
     * 分页查询活动列表
     *
     * @param param
     * @return
     */
    PageResult<ActivityDetailModel> findActivityInfoPage(PageParam<QueryActivityParam> param);

    /**
     * 抖音话题
     *
     * @param param
     * @return
     */
    TiktokTopicListModel findTopicList(TiktokTopicListParam param);


    /**
     * 获取门店下素材组数
     *
     * @param param
     * @return
     */
    Integer getMaterialGroupCount(GetMaterialGroupCountParam param);

    /**
     * 更新素材组信息
     *
     * @param param
     * @return
     */
    boolean updateActivity(UpdateActivityParam param);

    /**
     * 视频合成进度查询
     *
     * @param param
     * @return
     */
    SynthesisRateModel getSynthesisRate(SynthesisRateParam param);

    /**
     * 视频删除
     *
     * @param param
     */
    void deleteMaterialGroup(DeleteMaterialGroupParam param);

    /**
     * 删除预先合成的视频
     *
     * @param param
     * @return
     */
    boolean deleteVideo(VideoDeleteParam param);

    /**
     * 检测模块
     *
     * @param param
     * @return
     */
    TaskDetectionModel taskDetection(TaskDetectionParam param);

    /**
     * 合成模式选择
     *
     * @param param
     * @return
     */
    void chooseModel(ModelChooseParam param);

    /**
     * 手动合成视频
     *
     * @param param {@link ManualSyntheticVideoParam}
     * @return {@link Boolean}
     */
    Boolean manualSyntheticVideo(ManualSyntheticVideoParam param);

    /**
     * 合成记录和预览-数量统计
     *
     * @param param
     * @return {@link VideoCountStatisticsModel}
     * <AUTHOR>
     */
    VideoCountStatisticsModel videoCountStatistics(QuerySynthesisParam param);

    /**
     * 分页查询已合成视频列表
     *
     * @param param
     * @return {@link PageResult<SuccessSynthesisModel>}
     * <AUTHOR>
     */
    PageResult<SuccessSynthesisModel> pageListSuccessSynthesis(PageParam<QuerySynthesisParam> param);

    /**
     * 预合成视频分页列表
     *
     * @param param
     * @return {@link PageResult<PreSynthesisModel>}
     * <AUTHOR>
     */
    PageResult<PreSynthesisModel> pageListPreSynthesis(PageParam<QuerySynthesisParam> param);

    /**
     * 查询可用合成额度
     *
     * @param param
     * @return {@link QueryMerchantVideoConfigModel}
     * <AUTHOR>
     */
    QueryMerchantVideoConfigModel findMerchantVideoConfig(QueryMerchantVideoConfigParam param);

    /**
     * 自动更新视频
     *
     * @param param
     */
    void automaticUpdateVideo(AutomaticUpdateVideoParam param);

    /**
     * 是否显示自动更新视频按钮
     *
     * @param param
     * @return
     */
    AutomaticUpdateVideoModel showAutomaticButton(ShowAutomaticButtonParam param);

    /**
     * 预合成列表前置校验
     *
     * @param param 剪辑任务id
     * @return 结果
     */
    Integer preSynthesisListCheck(PreSynthesisListCheckParam param);

    /**
     * 预合成视频详情
     *
     * @param presetGroupId 预设组id
     * @return json
     */
    String preSynthesisDetail(String presetGroupId);

    /**
     * 获取活动基本信息
     *
     * @param param
     * @return
     */
    BasicActivityInfoModel getBasicActivityInfo(BasicActivityInfoParam param);

    /**
     * 更新商户视频配置
     *
     * @param param 入参
     */
    void updateMerchantVideoConfig(UpdateMerchantVideoConfigParam param);

    /**
     * 删除预合成视频
     *
     * @param param 入参
     */
    void deletePreSynthesisVideo(DeletePreSynthesisVideoParam param);
}