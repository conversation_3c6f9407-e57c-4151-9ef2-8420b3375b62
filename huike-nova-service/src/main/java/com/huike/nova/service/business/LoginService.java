/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.service.domain.model.login.FindProtocolListModel;
import com.huike.nova.service.domain.model.login.LoginModel;
import com.huike.nova.service.domain.model.login.MerchantChooseModel;
import com.huike.nova.service.domain.param.login.AccountCancelParam;
import com.huike.nova.service.domain.param.login.CaptchaCheckParam;
import com.huike.nova.service.domain.param.login.CaptchaSendParam;
import com.huike.nova.service.domain.param.login.GetAccountParam;
import com.huike.nova.service.domain.param.login.LoginChooseParam;
import com.huike.nova.service.domain.param.login.LoginParam;
import com.huike.nova.service.domain.param.login.LogoutParam;
import com.huike.nova.service.domain.param.login.MerchantChooseParam;
import com.huike.nova.service.domain.param.login.PasswordUpdateParam;

/**
 * <AUTHOR>
 * @version LoginService.java, v 0.1 2022-09-03 2:18 PM ruanzy
 */
public interface LoginService {

    /**
     * 登录
     *
     * @param param
     * @return
     */
    LoginModel login(LoginParam param);

    /**
     * 退出登录
     *
     * @param param
     */
    void logout(LogoutParam param);

    /**
     * 获取协议列表
     *
     * @return
     */
    FindProtocolListModel findProtocolList();

    /**
     * 输入账号
     *
     * @param param
     * @return
     */
    String getAccount(GetAccountParam param);

    /**
     * 验证码校验
     *
     * @param param
     */
    void checkCaptcha(CaptchaCheckParam param);


    /**
     * 发送验证码
     *
     * @param param
     */
    void sendCaptcha(CaptchaSendParam param);

    /**
     * 修改密码
     *
     * @param param
     */
    void updatePassword(PasswordUpdateParam param);

    /**
     * 登录校验
     *
     * @param param
     * @return
     */
    LoginModel chooseLogin(LoginChooseParam param);

    /**
     * 注销账号
     *
     * @param param
     */
    void cancelAccount(AccountCancelParam param);

    /**
     * 切换商户
     *
     * @param param
     * @return
     */
    MerchantChooseModel chooseMerchant(MerchantChooseParam param);

    /**
     * 初始化智荐服务商白名单
     *
     * @param
     * <AUTHOR>
     */
    void initManagerAccount();
}