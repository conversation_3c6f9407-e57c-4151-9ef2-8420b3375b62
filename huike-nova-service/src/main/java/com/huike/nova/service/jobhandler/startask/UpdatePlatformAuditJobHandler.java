/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler.startask;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.startask.common.StarTaskJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version UpdatePlatformAuditJobHandler.java, v 0.1 2023-12-12 3:11 PM ruanzy
 */
@Component
@Slf4j
@JobHandler("updatePlatformAuditJobHandler")
@AllArgsConstructor
public class UpdatePlatformAuditJobHandler extends IJobHandler {

    private StarTaskJobService starTaskJobService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("StarTaskEndTimeDayJobHandler.execute >> 更新平台审核脚本执行开始：time = {}", DateUtil.now());
        starTaskJobService.updatePlatformAudit();
        XxlJobLogger.log("StarTaskEndTimeDayJobHandler.execute >> 更新平台审核脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}