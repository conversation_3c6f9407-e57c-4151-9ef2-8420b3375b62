/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler.startask;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.startask.common.StarTaskJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version UpdateStarTaskByEndTimeJobHandler.java, v 0.1 2023-12-10 11:38 AM ruanzy
 */
@Component
@Slf4j
@JobHandler("updateStarTaskByEndTimeJobHandler")
@AllArgsConstructor
public class UpdateStarTaskByEndTimeJobHandler extends IJobHandler {

    private StarTaskJobService starTaskJobService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("StarTaskEndTimeDayJobHandler.execute >> 任务结束时间状态修改脚本执行开始：time = {}", DateUtil.now());
        starTaskJobService.updateStarTaskByEndTime();
        XxlJobLogger.log("StarTaskEndTimeDayJobHandler.execute >> 任务结束时间状态修改脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}