package com.huike.nova.service.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.enums.CommentStatusEnum;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.ProduceEnum;
import com.huike.nova.common.enums.ZbtLiveMessageTypeEnum;
import com.huike.nova.common.enums.ZbtPushTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.result.FindReplyScriptContentListDTO;
import com.huike.nova.dao.entity.AilikeZbtCommentDO;
import com.huike.nova.dao.entity.AilikeZbtCommentScriptContentDO;
import com.huike.nova.dao.entity.AilikeZbtCommentScriptDO;
import com.huike.nova.dao.entity.AilikeZbtReplyScriptDO;
import com.huike.nova.dao.entity.AilikeZbtStoreConfigDO;
import com.huike.nova.dao.entity.AilikeZbtStoreLiveRecordDO;
import com.huike.nova.dao.repository.AilikeZbtCommentDAO;
import com.huike.nova.dao.repository.AilikeZbtCommentScriptContentDAO;
import com.huike.nova.dao.repository.AilikeZbtCommentScriptDAO;
import com.huike.nova.dao.repository.AilikeZbtReplyScriptContentKeywordDAO;
import com.huike.nova.dao.repository.AilikeZbtReplyScriptDAO;
import com.huike.nova.dao.repository.AilikeZbtStoreConfigDAO;
import com.huike.nova.dao.repository.AilikeZbtStoreLiveRecordDAO;
import com.huike.nova.service.business.CommonService;
import com.huike.nova.service.business.ZbtService;
import com.huike.nova.service.domain.dto.zbt.ZbtCommentPushDTO;
import com.huike.nova.service.domain.dto.zbt.ZbtLiveListenerDTO;
import com.huike.nova.service.domain.dto.zbt.ZbtLivePushDTO;
import com.huike.nova.service.domain.param.zbt.CloseZbtParam;
import com.huike.nova.service.message.MsgProducer;
import com.huike.nova.service.websocket.ZbtWebSocket;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023年09月05日 15:19
 */
@Slf4j(topic = "mq")
@Component
@AllArgsConstructor
public class ZbtLiveListener implements MessageListener {

    private AilikeZbtStoreLiveRecordDAO ailikeZbtStoreLiveRecordDAO;

    private AilikeZbtCommentScriptDAO ailikeZbtCommentScriptDAO;

    private AilikeZbtCommentScriptContentDAO ailikeZbtCommentScriptContentDAO;

    private RedissonClient redissonClient;

    private MsgProducer msgProducer;

    private ZbtService zbtService;

    private SysConfig sysConfig;

    private ZbtWebSocket zbtWebSocket;

    private AilikeZbtStoreConfigDAO ailikeZbtStoreConfigDAO;

    private AilikeZbtReplyScriptContentKeywordDAO ailikeZbtReplyScriptContentKeywordDAO;

    private AilikeZbtCommentDAO ailikeZbtCommentDAO;

    private CommonService commonService;

    private AilikeZbtReplyScriptDAO ailikeZbtReplyScriptDAO;


    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        LogUtil.info(log, "ZbtLiveListener.consume >> 消费开始 >> message = {}", message);
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        ZbtLiveListenerDTO liveListenerDTO = JSON.parseObject(body, ZbtLiveListenerDTO.class);
        LogUtil.info(log, "ZbtLiveListener.consume >> liveListenerDTO内容 >> liveListenerDTO = {},body = {}", liveListenerDTO, body);
        if (null == liveListenerDTO) {
            LogUtil.info(log, "ZbtLiveListener.consume >> mq推送消息为空");
            return Action.CommitMessage;
        }
        Integer messageType = liveListenerDTO.getMessageType();
        String storeId = liveListenerDTO.getStoreId();
        ZbtLiveMessageTypeEnum zbtLiveMessageTypeEnum = ZbtLiveMessageTypeEnum.getByValue(messageType);
        if (null == zbtLiveMessageTypeEnum) {
            LogUtil.info(log, "ZbtLiveListener.consume >> mq推送类型错误 >> liveListenerDTO = {}", liveListenerDTO);
            return Action.CommitMessage;
        }
        // 校验直播状态
        AilikeZbtStoreLiveRecordDO liveRecord = ailikeZbtStoreLiveRecordDAO.getLiveRecordByStoreId(storeId);
        if (null == liveRecord) {
            LogUtil.info(log, "ZbtLiveListener.consume >> 智播通已关播 >> liveListenerDTO = {},liveRecord = {}", liveListenerDTO, liveRecord);
            return Action.CommitMessage;
        }

        switch (zbtLiveMessageTypeEnum) {
            case AUTOMATIC_COMMENTS:
                this.automaticComments(liveListenerDTO);
                break;
            case SOUND_REPLY:
                this.soundReply(liveListenerDTO);
                break;
            case LIVE_STATUS_CHECK:
                this.liveStatusCheck(liveListenerDTO);
                break;
            default:
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("直播数据交互类型不合法");
        }
        return Action.CommitMessage;
    }

    /**
     * 直播状态校验
     *
     * @param liveListenerDTO 智播通mq信息
     */
    private void liveStatusCheck(ZbtLiveListenerDTO liveListenerDTO) {
        String storeId = liveListenerDTO.getStoreId();
        // 设置一分钟保活
        RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.ZBT_STORE_KEEP_ALIVE_KEY, storeId));
        if (bucket.isExists()) {
            // 构建mq 一分钟检测一次（保活缓存有效期五分钟）
            ZbtLiveListenerDTO zbtLiveListenerDTO = ZbtLiveListenerDTO.init();
            zbtLiveListenerDTO.setStoreId(storeId);
            zbtLiveListenerDTO.setMessageType(ZbtLiveMessageTypeEnum.LIVE_STATUS_CHECK.getValue());
            msgProducer.sendDelayMessage(ProduceEnum.ZBT_LIVE, JSON.toJSONString(zbtLiveListenerDTO), 60 * 1000);
            return;
        }
        //     关闭助手直播
        zbtService.closeZbt(new CloseZbtParam(storeId));
    }

    /**
     * 口播回复
     *
     * @param liveListenerDTO 智播通mq信息
     */
    private void soundReply(ZbtLiveListenerDTO liveListenerDTO) {
        LogUtil.info(log, "ZbtLiveListener.soundReply >> 接口开始 >> liveListenerDTO = {}", liveListenerDTO);
        String storeId = liveListenerDTO.getStoreId();
        String roomId = liveListenerDTO.getRoomId();
        // 评论列表
        List<ZbtCommentPushDTO> commentPushDTOList = liveListenerDTO.getComments();
        if (CollectionUtil.isEmpty(commentPushDTOList)) {
            LogUtil.info(log, "ZbtLiveListener.soundReply >> 评论为空 >> liveListenerDTO = {}", liveListenerDTO);
            return;
        }
        // 间隔时间
        Integer soundReplyInterval = sysConfig.getZbtSoundReplyInterval();
        LogUtil.info(log, "ZbtLiveListener.soundReply >> soundReplyInterval >> soundReplyInterval = {}", soundReplyInterval);
        //     根据门店id获取当前选中的口播脚本
        LogUtil.info(log, "ZbtLiveListener.soundReply >> getUseSoundScriptByStoreId查询前 >> storeId = {}", storeId);
        AilikeZbtReplyScriptDO soundScriptDO = ailikeZbtReplyScriptDAO.getUseReplyScriptByStoreId(storeId);
        LogUtil.info(log, "ZbtLiveListener.soundReply >> getUseSoundScriptByStoreId查询后 >> soundScriptDO = {}", soundScriptDO);
        if (null == soundScriptDO) {
            LogUtil.info(log, "ZbtLiveListener.soundReply >> 当前选中的口播脚本为空 >> liveListenerDTO = {}", liveListenerDTO);
            return;
        }
        AilikeZbtStoreConfigDO storeConfig = ailikeZbtStoreConfigDAO.getStoreConfig(storeId);
        if (null == storeConfig || StringUtils.isBlank(storeConfig.getRpaId())) {
            LogUtil.info(log, "ZbtLiveListener.automaticComments >> rpaId为空 liveListenerDTO = {}", liveListenerDTO);
            return;
        }
        String rpaId = storeConfig.getRpaId();
        String scriptId = soundScriptDO.getScriptId();
        String scriptName = soundScriptDO.getScriptName();
        List<AilikeZbtCommentDO> commentList = new ArrayList<>();
        // 匹配关键词
        for (ZbtCommentPushDTO item : commentPushDTOList) {
            item.setOriginMsgContent(item.getCommentContent());
            item.setAtNickname(item.getNickname());
            item.setOriginMsgId(item.getMsgId());
            String commentContent = item.getCommentContent();
            // 实时评论
            AilikeZbtCommentDO insertComment = new AilikeZbtCommentDO();
            insertComment.setStoreId(storeId);
            insertComment.setRoomId(roomId);
            insertComment.setCommentId(commonService.buildIncr());
            insertComment.setCommentContent(commentContent);
            insertComment.setCommentStatus(CommentStatusEnum.NOT_COLLECT.getValue());
            List<FindReplyScriptContentListDTO> keywordList = ailikeZbtReplyScriptContentKeywordDAO.findReplyScriptContentListByKeyword(scriptId, commentContent);
            LogUtil.info(log, "ZbtLiveListener.soundReply >> 关键词匹配查询结束 >> keywordList = {}", keywordList);
            // 脚本id
            if (CollectionUtil.isEmpty(keywordList)) {
                LogUtil.info(log, "ZbtLiveListener.soundReply >> 关键词列表continue >> soundKeywordList = {}", keywordList);
                commentList.add(insertComment);
                continue;
            }
            Collections.shuffle(keywordList);
            FindReplyScriptContentListDTO replyScriptContentDTO = keywordList.get(0);
            item.setComment(replyScriptContentDTO.getSoundContent());
            LogUtil.info(log, "ZbtLiveListener.soundReply >> 单条回复选择完毕 >> replyScriptContentDTO = {}", replyScriptContentDTO);
            String keyword = replyScriptContentDTO.getKeyword();
            insertComment.setHitKeyword(keyword);
            insertComment.setCommentStatus(CommentStatusEnum.IGNORED.getValue());

            // 推送给碴哥 @评论回复
            ZbtLivePushDTO zbtLivePushDTO = new ZbtLivePushDTO(rpaId, JSON.toJSONString(item), ZbtPushTypeEnum.TEXT_REPLY.getValue());
            msgProducer.sendMessage(ProduceEnum.ZBT_LIVE_PUSH, JSON.toJSONString(zbtLivePushDTO));

            // 设置间隔缓存
            RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.ZBT_SOUND_REPLY_INTERVAL_KEY, storeId));
            if (bucket.isExists()) {
                LogUtil.info(log, "ZbtLiveListener.soundReply >> 缓存存在 >> liveListenerDTO = {}", liveListenerDTO);
                commentList.add(insertComment);
                continue;
            }
            // 评论回复
            insertComment.setCommentStatus(CommentStatusEnum.REPLIED.getValue());
            String shortKeyword = CommonConstant.INTEGER_FIVE > keyword.length() ? keyword : keyword.substring(0, 5);
            replyScriptContentDTO.setPlayingContent(scriptName + StringPool.DASH + shortKeyword);
            bucket.set(storeId, soundReplyInterval, TimeUnit.SECONDS);
            RBucket<String> soundBucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.ZBT_SOUND_REPLY_CONTENT_KEY, storeId));
            soundBucket.set(JSON.toJSONString(new ArrayList<>(Collections.singleton(replyScriptContentDTO))), 15, TimeUnit.SECONDS);
            commentList.add(insertComment);
        }
        ailikeZbtCommentDAO.batchSaveComment(commentList);
        LogUtil.info(log, "ZbtLiveListener.soundReply >> 接口结束 >> liveListenerDTO = {}", liveListenerDTO);
    }

    /**
     * 评论场控
     *
     * @param liveListenerDTO 智播通mq信息
     */
    private void automaticComments(ZbtLiveListenerDTO liveListenerDTO) {
        String storeId = liveListenerDTO.getStoreId();
        // 查询门店下选中状态的脚本
        AilikeZbtCommentScriptDO commentScript = ailikeZbtCommentScriptDAO.getUseCommentScriptByStoreId(storeId);
        if (null == commentScript) {
            LogUtil.info(log, "ZbtLiveListener.automaticComments >> commentScript为空 liveListenerDTO = {}", liveListenerDTO);
            return;
        }
        //     根据脚本id获取脚本内容
        List<AilikeZbtCommentScriptContentDO> commentList = ailikeZbtCommentScriptContentDAO.findCommentListByScriptId(commentScript.getScriptId());
        if (CollectionUtil.isEmpty(commentList)) {
            LogUtil.info(log, "ZbtLiveListener.automaticComments >> commentList为空 liveListenerDTO = {}", liveListenerDTO);
            return;
        }
        Collections.shuffle(commentList);
        String content = commentList.get(0).getContent();
        // 设置间隔缓存
//        bucket.set(storeId, commentScript.getSendFrequency(), TimeUnit.SECONDS);
        AilikeZbtStoreConfigDO storeConfig = ailikeZbtStoreConfigDAO.getStoreConfig(storeId);
        if (null == storeConfig || StringUtils.isBlank(storeConfig.getRpaId())) {
            LogUtil.info(log, "ZbtLiveListener.automaticComments >> rpaId为空 liveListenerDTO = {}", liveListenerDTO);
            return;
        }
        // 推送给碴哥
        ZbtLivePushDTO zbtLivePushDTO = new ZbtLivePushDTO(storeConfig.getRpaId(), content, ZbtPushTypeEnum.COMMENT.getValue());
        msgProducer.sendMessage(ProduceEnum.ZBT_LIVE_PUSH, JSON.toJSONString(zbtLivePushDTO));

        // 构建mq 推送给自己
        ZbtLiveListenerDTO zbtLiveListenerDTO = ZbtLiveListenerDTO.init();
        zbtLiveListenerDTO.setStoreId(storeId);
        zbtLiveListenerDTO.setMessageType(ZbtLiveMessageTypeEnum.AUTOMATIC_COMMENTS.getValue());
        msgProducer.sendDelayMessage(ProduceEnum.ZBT_LIVE, JSON.toJSONString(zbtLiveListenerDTO), commentScript.getSendFrequency() * 1000);
        LogUtil.info(log, "ZbtLiveListener.automaticComments >> automaticComments结束 >> liveListenerDTO = {}", liveListenerDTO);
    }


}
