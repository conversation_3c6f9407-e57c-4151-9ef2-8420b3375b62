/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version TiktokRefreshTokenJobHandler.java, v 0.1 2023-02-10 11:10 AM ruanzy
 */
@Component
@Slf4j
@JobHandler("tiktokRefreshTokenJobHandler")
@AllArgsConstructor
public class TiktokRefreshTokenJobHandler extends IJobHandler {

    private TaskService taskService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("TiktokRefreshTokenJobHandler.execute >> 刷新抖音refresh_token脚本执行开始：time = {}", DateUtil.now());
        taskService.tiktokRefreshToken();
        XxlJobLogger.log("TiktokRefreshTokenJobHandler.execute >> 刷新抖音refresh_token脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}