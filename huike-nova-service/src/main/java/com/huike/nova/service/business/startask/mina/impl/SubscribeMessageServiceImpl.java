/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.mina.impl;


import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fshows.fsframework.common.exception.CommonException;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.startask.mina.SubscribeMessageTypeEnum;
import com.huike.nova.common.util.FsDateUtils;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.result.StarTaskMinaTemplateListDTO;
import com.huike.nova.dao.entity.StarTaskWechatMinaConfigDO;
import com.huike.nova.dao.repository.StarTaskUserDAO;
import com.huike.nova.dao.repository.StarTaskWechatMinaConfigDAO;
import com.huike.nova.service.business.WechatApiService;
import com.huike.nova.service.business.startask.mina.StarTaskWechatMinaService;
import com.huike.nova.service.business.startask.mina.SubscribeMessageService;
import com.huike.nova.service.domain.model.startask.mina.templatemessage.WxSubscribeMessageListQueryModel;
import com.huike.nova.service.domain.param.startask.mina.templatemessage.WxSubscribeMessageListQueryParam;
import com.huike.nova.service.domain.param.startask.mina.templatemessage.WxSubscribeMessageReportParam;
import com.huike.nova.service.domain.param.wechatapi.WxSubscribeMessageSendParam;
import com.huike.nova.service.domain.param.wechatapi.WxSubscribeMessageTemplateDataModel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 订阅消息
 *
 * <AUTHOR>
 * @version SubscribeMessageServiceImpl.java, v 0.1 2019-06-15 11:09 wangyi
 */
@Slf4j
@Service
@AllArgsConstructor
public class SubscribeMessageServiceImpl implements SubscribeMessageService {

    /**
     * 过期时间
     */
    private static final int EXPIRED_DAY = 29;
    private static final String WECHAT_SUBSCRIBE_MESSAGE_REDIS_KEY = "wechat.subscribe.message.list.";

    /**
     * redis 客户端
     */
    private RedissonClient redisson;

    private SysConfig sysConfig;

    private StarTaskUserDAO starTaskUserDAO;

    private StarTaskWechatMinaConfigDAO starTaskWechatMinaConfigDAO;

    private StarTaskWechatMinaService starTaskWechatMinaService;

    private WechatApiService wechatApiService;


    /**
     * 获取redis 的SortedSet对象
     *
     * @param key KEY
     * @return org.redisson.api.RScoredSortedSet
     */
    private RScoredSortedSet<String> getSortedSet(String key) {
        return redisson.getScoredSortedSet(key, StringCodec.INSTANCE);
    }

    /**
     * 订阅消息记录上报
     *
     * @param openId      用户ID
     * @param templateIds 模版id列表
     */
    @Override
    public void wxSubscribeMessageReport(String openId, List<String> templateIds, Integer type, String businessId) {
        LogUtil.info(log, "SubscribeMessageServiceImpl.wxSubscribeMessageReport >> 接口开始 >> openId = {},templateIds = {}", openId, templateIds);
        // 过期时间
        Date now = new Date();
        Date expiredDate = DateUtils.addDays(now, EXPIRED_DAY);
        String expiredDateStr = DateUtil.format(expiredDate, FsDateUtils.NUMBER_DATE_FORMAT);
        int expiredDateNumber = Integer.parseInt(expiredDateStr);
        // 添加集合
        for (String templateId : templateIds) {
            // 创建scoredsortedSet对象
//            RScoredSortedSet<String> sortedSet = getSortedSet(WECHAT_SUBSCRIBE_MESSAGE_REDIS_KEY + type + "." + openId + "." + templateId);
//            sortedSet.add(expiredDateNumber, String.valueOf(System.currentTimeMillis() / 1000));
//            // 更新整个集合的过期时间
//            sortedSet.expireAt(expiredDate);
            RBucket<String> bucket = redisson.getBucket(WECHAT_SUBSCRIBE_MESSAGE_REDIS_KEY + type + "." + businessId + "." + templateId);
            if (!bucket.isExists()) {
                bucket.set(openId);
                bucket.expire(60 * 60 * 24 * 30, TimeUnit.SECONDS);
            }
            LogUtil.info(log, "更新微信小程序用户订阅记录 >> wxSubscribeMessageReport, openId={}，templateIds={},content = {}", openId, templateIds, bucket.get());
        }
    }

    /**
     * 清除订阅记录
     *
     * @param templateId 模版id
     * @param businessId 业务id
     */
    @Override
    public void removeRecord(String templateId, String businessId, Integer type) {
        LogUtil.info(log, "删除微信小程序用户订阅记录 >> removeRecord, templateId={}", templateId);
        RBucket<String> bucket = redisson.getBucket(WECHAT_SUBSCRIBE_MESSAGE_REDIS_KEY + type + "." + businessId + "." + templateId);
        if (bucket.isExists()) {
            bucket.delete();
        }
    }

    /**
     * 查询微信小程序用户订阅记录
     *
     * @param businessId 业务id
     * @return templateId
     */
    private String queryTemplateIdList(String businessId, String templateId, Integer type) {
        RBucket<String> bucket = redisson.getBucket(WECHAT_SUBSCRIBE_MESSAGE_REDIS_KEY + type + "." + businessId + "." + templateId);
        return bucket.get();
    }

    /**
     * 查询订阅记录
     *
     * @param businessId 业务id
     * @param templateId 模版id
     * @return 时间字符串
     */
    @Override
    public String queryRecord(String businessId, String templateId, Integer type) {
        LogUtil.info(log, "SubscribeMessageServiceImpl.queryRecord >> 查询微信小程序用户订阅记录 >> businessId = {},templateId = {}", businessId, templateId);
        return queryTemplateIdList(businessId, templateId, type);
    }

    /**
     * 获取订阅消息发送消息体
     *
     * @param messageId 消息id
     * @param data      数据
     * @return 数据
     */
    @Override
    public List<WxSubscribeMessageTemplateDataModel> getSubscribeMessageData(String messageId, List<String> data) {
        List<WxSubscribeMessageTemplateDataModel> listData = new ArrayList<>();
        try {
            List<StarTaskMinaTemplateListDTO> templateList = JSONObject.parseArray(sysConfig.getStarTaskMinaTemplateList(), StarTaskMinaTemplateListDTO.class);
            Map<String, String> orderRefundTypeMap = templateList.stream().collect(Collectors.toMap(StarTaskMinaTemplateListDTO::getTemplateId, StarTaskMinaTemplateListDTO::getTemplateData));
            LogUtil.info(log, "SubscribeMessageServiceImpl.getSubscribeMessageData >> 获取订阅消息发送消息体 >> messageId = {},data={}", messageId, data);
            // 根据消息id查询messageJson
            String messageJsonStr = orderRefundTypeMap.get(messageId);
            LogUtil.info(log, "SubscribeMessageServiceImpl.getSubscribeMessageData >> 获取订阅消息发送消息体 >> messageJsonStr = {}", messageJsonStr);
            if (StringUtils.isBlank(messageJsonStr)) {
                throw CommonException.INVALID_PARAM_ERROR.newInstance("未查询到messageJson");
            }
            // 对象转换
            List<WxSubscribeMessageTemplateDataModel> listBox = JSON.parseArray(messageJsonStr, WxSubscribeMessageTemplateDataModel.class);
            // value赋值
            int index = 0;
            for (WxSubscribeMessageTemplateDataModel box : listBox) {
                box.setValue(data.get(index));
                listData.add(box);
                index++;
            }
        } catch (Exception e) {
            com.fshows.fsframework.core.utils.LogUtil.error(log, "getSubscribeMessageData >> 获取订阅消息发送消息体失败 >> error = {}", e.getMessage());
        }
        return listData;
    }

    /**
     * 发送订阅消息
     *
     * @param businessId 业务io
     * @param appletId   小程序id
     * @param templateId 模版id
     * @param page       跳转页面
     * @param data       数据
     */
    @Override
    public void sendSubscribeMessage(String businessId, String appletId, String templateId, String page, List<String> data, Integer type) {
        LogUtil.info(log, "SubscribeMessageServiceImpl.sendSubscribeMessage >> 接口开始 >> businessId = {},appletId = {},templateId = {},page = {},data = {}", businessId, appletId, templateId, page, data);
        try {

            String openId = this.queryRecord(businessId, templateId, type);
            if (StringUtils.isEmpty(openId)) {
                LogUtil.info(log, "SubscribeMessageServiceImpl.sendSubscribeMessage >>  无订阅记录 >> businessId = {}", businessId);
                return;
            }
            StarTaskWechatMinaConfigDO configDO = starTaskWechatMinaConfigDAO.queryByAppId(appletId);
            if (null == configDO) {
                return;
            }
            String accessToken = starTaskWechatMinaService.getCachedAccessTokenByAppId(appletId);
            if (StringUtils.isBlank(accessToken)) {
                return;
            }
            List<WxSubscribeMessageTemplateDataModel> templateDataModelList = this.getSubscribeMessageData(templateId, data);
            WxSubscribeMessageSendParam sendParam = new WxSubscribeMessageSendParam();
            sendParam.setOpenId(openId);
            sendParam.setAppId(appletId);
            sendParam.setAppSecret(configDO.getAppSecret());
            sendParam.setAccessToken(accessToken);
            sendParam.setTemplateId(templateId);
            sendParam.setPage(page);
            sendParam.setData(templateDataModelList);
            wechatApiService.subscribeMessageSend(sendParam);
            this.removeRecord(templateId, businessId, type);
        } catch (Exception e) {
            LogUtil.error(log, "SubscribeMessageServiceImpl.sendSubscribeMessage >> 全局异常 >> appletId = {},templateId = {},page = {},data = {}", e, appletId, templateId, page, data);
        }
    }

    /**
     * 微信订阅消息列表查询
     *
     * @param param 查询参数
     * @return 订阅消息列表
     */
    @Override
    public WxSubscribeMessageListQueryModel wxSubscribeMessageListQuery(WxSubscribeMessageListQueryParam param) {
        LogUtil.info(log, "StarTaskWechatMinaServiceImpl.wxSubscribeMessageListQuery >> 接口开始 >> param = {}", JSON.toJSONString(param));
        SubscribeMessageTypeEnum subscribeMessageTypeEnum = SubscribeMessageTypeEnum.getByValue(param.getType());
        if (null == subscribeMessageTypeEnum) {
            throw new com.huike.nova.common.exception.CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("获取类型错误");
        }
        List<String> templateIdList = new ArrayList<>();
        switch (subscribeMessageTypeEnum) {
            case SUBMIT_TASK:
                templateIdList.add(sysConfig.getWxSubscribeMessageTaskAuditResult());
                break;
            case SUBMIT_LINK:
                templateIdList.add(sysConfig.getWxSubscribeMessageWorksAuditResult());
                break;
            default:
                throw new com.huike.nova.common.exception.CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("获取类型错误");
        }
        LogUtil.info(log, "StarTaskWechatMinaServiceImpl.wxSubscribeMessageListQuery >> 接口结束 >> templateIdList = {}", templateIdList);
        return new WxSubscribeMessageListQueryModel(templateIdList);
    }

    /**
     * 微信订阅消息上报
     *
     * @param param 上报参数
     */
    @Override
    public void wxSubscribeMessageReport(WxSubscribeMessageReportParam param) {
        LogUtil.info(log, "StarTaskWechatMinaServiceImpl.wxSubscribeMessageReport >> 接口开始 >> param = {}", JSON.toJSONString(param));
        this.wxSubscribeMessageReport(param.getOpenId(), param.getTemplateIds(), param.getType(), param.getBusinessId());
    }
}