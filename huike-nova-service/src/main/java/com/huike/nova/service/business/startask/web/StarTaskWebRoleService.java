/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.web;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.oem.operation.employee.GetRoleDetailModel;
import com.huike.nova.service.domain.model.oem.operation.employee.PageQueryRoleModel;
import com.huike.nova.service.domain.model.oem.operation.employee.QueryAllGrantModel;
import com.huike.nova.service.domain.model.oem.operation.employee.SelectRoleListModel;
import com.huike.nova.service.domain.model.startask.web.role.FindEmployeeListModel;
import com.huike.nova.service.domain.model.startask.web.role.StarTaskEmployeeDetailModel;
import com.huike.nova.service.domain.model.startask.web.role.StarTaskPageEmployeeListModel;
import com.huike.nova.service.domain.param.oem.operation.employee.AddOrUpdateRoleParam;
import com.huike.nova.service.domain.param.oem.operation.employee.DeleteRoleParam;
import com.huike.nova.service.domain.param.oem.operation.employee.GetRoleDetailParam;
import com.huike.nova.service.domain.param.startask.web.role.FindEmployeeListParam;
import com.huike.nova.service.domain.param.startask.web.role.StarTaskAddEmployeeParam;
import com.huike.nova.service.domain.param.startask.web.role.StarTaskEmployeeDetailParam;
import com.huike.nova.service.domain.param.startask.web.role.StarTaskOperateEmployeeParam;
import com.huike.nova.service.domain.param.startask.web.role.StarTaskPageEmployeeListParam;
import com.huike.nova.service.domain.param.startask.web.role.StarTaskUpdateEmployeeParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version StarTaskWebRoleService.java, v 0.1 2023-12-05 8:16 PM ruanzy
 */
public interface StarTaskWebRoleService {

    /**
     * 新增员工
     *
     * @param param
     */
    void addEmployee(StarTaskAddEmployeeParam param);

    /**
     * 修改员工
     *
     * @param param
     */
    void updateEmployee(StarTaskUpdateEmployeeParam param);

    /**
     * 分页查询员工列表
     *
     * @param param
     * @return
     */
    PageResult<StarTaskPageEmployeeListModel> pageEmployeeList(PageParam<StarTaskPageEmployeeListParam> param);

    /**
     * 员工详情
     *
     * @param param
     * @return
     */
    StarTaskEmployeeDetailModel getEmployeeDetail(StarTaskEmployeeDetailParam param);

    /**
     * 员工操作
     *
     * @param param
     */
    void operateEmployee(StarTaskOperateEmployeeParam param);

    /**
     * 分页查看角色列表
     *
     * @param param
     * @return
     */
    PageResult<PageQueryRoleModel> pageQueryRole(PageParam param);

    /**
     * 角色详情
     *
     * @param param
     * @return
     */
    GetRoleDetailModel getRoleDetail(GetRoleDetailParam param);

    /**
     * 角色权限列表
     *
     * @return
     */
    QueryAllGrantModel queryAllGrant();

    /**
     * 新增或修改角色
     *
     * @param param
     */
    void addOrUpdateRole(AddOrUpdateRoleParam param);

    /**
     * 角色删除
     *
     * @param param
     */
    void deleteRole(DeleteRoleParam param);

    /**
     * 下拉获取角色列表
     *
     * @return
     */
    SelectRoleListModel selectRoleList();

    /**
     * 查询员工列表
     *
     * @param findEmployeeListParam
     * @return
     */
    List<FindEmployeeListModel> findEmployeeList(FindEmployeeListParam findEmployeeListParam);
}