/**
 * <AUTHOR>
 * @date 2024/12/4 9:28
 * @version 1.0 MallCooReducePointsJobHandler
 */
package com.huike.nova.service.jobhandler;

import com.alibaba.fastjson.JSONObject;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.service.business.TaskService;
import com.huike.nova.service.domain.param.job.AutoReissueMerchantCardJobParam;
import com.huike.nova.service.domain.param.verifyTool.MallCooReducePointsJobParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

/**
 * 猫酷会员退款订单积分扣减
 *
 * <AUTHOR>
 * @version MallCooReducePointsJobHandler.java, v 0.1 2024-12-04 9:28 tuyuwei
 */

@Component
@Slf4j
@JobHandler("mallCooReducePointsJobHandler")
@AllArgsConstructor
public class MallCooReducePointsJobHandler extends IJobHandler {

    TaskService taskService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("猫酷会员退款订单积分扣减脚本开始：请求参数:{}", s);
        LogUtil.warn(log, "MallCooReducePointsJobHandler.execute >> 猫酷会员退款订单积分扣减脚本开始, 请求参数: ", s);
        MallCooReducePointsJobParam param = JSONObject.parseObject(s, MallCooReducePointsJobParam.class);
        taskService.mallCooReduceAddPoints(param.getStartTime());
        return ReturnT.SUCCESS;
    }
}