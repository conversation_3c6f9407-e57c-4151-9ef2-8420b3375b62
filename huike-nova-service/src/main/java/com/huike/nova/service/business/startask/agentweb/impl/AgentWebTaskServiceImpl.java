/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.enums.oem.BooleanEnum;
import com.huike.nova.common.enums.startask.mina.ApplyListStatusEnum;
import com.huike.nova.common.enums.startask.web.OperateExportBusinessTypeEnum;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.common.util.FsDateUtils;
import com.huike.nova.dao.domain.param.startask.WebTaskListParamDTO;
import com.huike.nova.dao.domain.result.startask.WebTaskListResultDTO;
import com.huike.nova.dao.entity.StarTaskApplyListDO;
import com.huike.nova.dao.entity.StarTaskCommissionPlanDO;
import com.huike.nova.dao.entity.StarTaskOperatingRecordDO;
import com.huike.nova.dao.entity.StarTaskOperatorDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.StarTaskAgentAreaDAO;
import com.huike.nova.dao.repository.StarTaskApplyListDAO;
import com.huike.nova.dao.repository.StarTaskCommissionPlanDAO;
import com.huike.nova.dao.repository.StarTaskDAO;
import com.huike.nova.dao.repository.StarTaskOperatingRecordDAO;
import com.huike.nova.dao.repository.StarTaskOperatorDAO;
import com.huike.nova.service.business.startask.agentweb.AgentWebTaskService;
import com.huike.nova.service.business.startask.mina.StarTaskCommonMinaService;
import com.huike.nova.service.business.startask.web.StarTaskWebExportService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.web.StarTaskWebTaskServiceObjMapper;
import com.huike.nova.service.domain.model.startask.agentweb.login.StarTaskAgentWebLoginModel;
import com.huike.nova.service.domain.model.startask.web.task.WebTaskListModel;
import com.huike.nova.service.domain.param.startask.web.task.WebApplyListParam;
import com.huike.nova.service.domain.param.startask.web.task.WebTaskListParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version AgentWebTaskServiceImpl.java, v 0.1 2024-05-22 4:34 PM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class AgentWebTaskServiceImpl implements AgentWebTaskService {

    private StarTaskWebTaskServiceObjMapper starTaskWebTaskServiceObjMapper;

    private StarTaskDAO starTaskDAO;

    private StarTaskCommissionPlanDAO starTaskCommissionPlanDAO;

    private StarTaskApplyListDAO starTaskApplyListDAO;

    private StarTaskOperatingRecordDAO starTaskOperatingRecordDAO;

    private StarTaskOperatorDAO starTaskOperatorDao;

    private StarTaskCommonMinaService starTaskCommonMinaService;

    private StarTaskAgentAreaDAO starTaskAgentAreaDAO;

    private StarTaskWebExportService starTaskWebExportService;


    /**
     * 任务管理列表
     *
     * @param param 入参
     * @return 出参
     */
    @Override
    public PageResult<WebTaskListModel> list(PageParam<WebTaskListParam> param) {
        // 获取登录态信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        PageParam<WebTaskListParamDTO> pageParam = starTaskWebTaskServiceObjMapper.toWebTaskListParamDTO(param);
        WebTaskListParamDTO query = pageParam.getQuery();
        String phoneNumber = query.getPhoneNumber();
        query.setAgentId(basicInfo.getAgentId());
        if (StringUtils.isNotEmpty(phoneNumber)) {
            query.setPhoneNumber(FieldEncryptUtil.encode(phoneNumber));
        }
        String createEndDate = query.getCreateEndDate();
        String createStartDate = query.getCreateStartDate();
        if (StringUtils.isNotBlank(createEndDate) && StringUtils.isNotBlank(createStartDate)) {
            query.setCreateStartDate(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(createStartDate, DatePattern.NORM_DATE_PATTERN))));
            query.setCreateEndDate(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(createEndDate, DatePattern.NORM_DATE_PATTERN))));
        }
        String auditEndDate = query.getAuditEndDate();
        String auditStartDate = query.getAuditStartDate();
        if (StringUtils.isNotBlank(auditEndDate) && StringUtils.isNotBlank(auditStartDate)) {
            query.setAuditStartDate(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(auditStartDate, DatePattern.NORM_DATE_PATTERN))));
            query.setAuditEndDate(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(auditEndDate, DatePattern.NORM_DATE_PATTERN))));
        }
        // 分页查询
        Page<WebTaskListResultDTO> activityPage = starTaskDAO.pageList(pageParam);
        List<WebTaskListResultDTO> resultDTOList = activityPage.getRecords();
        if (CollectionUtils.isEmpty(resultDTOList)) {
            return starTaskWebTaskServiceObjMapper.toWebTaskListModel(activityPage);
        }
        List<String> taskIdList = resultDTOList.stream().map(WebTaskListResultDTO::getTaskId).collect(Collectors.toList());
        List<StarTaskApplyListDO> applyList = starTaskApplyListDAO.findApplyListByTaskIdList(taskIdList);
        List<StarTaskCommissionPlanDO> commissionPlanByTaskIds = starTaskCommissionPlanDAO.findCommissionPlanByTaskIdList(taskIdList);
        // 根据taskId查询操作记录数量
        List<StarTaskOperatingRecordDO> starTaskOperatingRecordDOS = starTaskOperatingRecordDAO.findListByTaskId(taskIdList);
        Map<String, List<StarTaskOperatingRecordDO>> recordMap = starTaskOperatingRecordDOS.stream().collect(Collectors.groupingBy(StarTaskOperatingRecordDO::getTaskId));
        // 根据operatorId查询操作人姓名
        List<String> followUpPersonIdList = resultDTOList.stream().map(WebTaskListResultDTO::getFollowUpPersonId).collect(Collectors.toList());
        List<StarTaskOperatorDO> starTaskOperatorDOList = starTaskOperatorDao.findListByOperatorIdList(followUpPersonIdList);
        Map<String, StarTaskOperatorDO> operatorDOMap = starTaskOperatorDOList.stream().collect(Collectors.toMap(StarTaskOperatorDO::getOperatorId, Function.identity(), (v1, v2) -> v2));
        //使用stream根据taskId分组
        Map<String, List<StarTaskApplyListDO>> applyListMap = applyList.stream().collect(Collectors.groupingBy(StarTaskApplyListDO::getTaskId));
        Map<String, List<StarTaskCommissionPlanDO>> commissionPlanMap = commissionPlanByTaskIds.stream().collect(Collectors.groupingBy(StarTaskCommissionPlanDO::getTaskId));
        for (WebTaskListResultDTO record : resultDTOList) {
            String taskId = record.getTaskId();
            // 构建任务的进度数据
            this.buildListTaskProgressQuantity(record, applyListMap.getOrDefault(taskId, Collections.emptyList()), commissionPlanMap.getOrDefault(taskId, Collections.emptyList()));
            record.setStarAreaName(starTaskCommonMinaService.findStarAreaNameList(taskId));
            record.setPhoneNumber(FieldEncryptUtil.decode(record.getPhoneNumber()));
            record.setApplyStartTime(DateUtil.format(record.getApplyStartDate(), FsDateUtils.SIMPLE_DATE_FORMAT));
            record.setApplyEndTime(DateUtil.format(record.getApplyEndDate(), FsDateUtils.SIMPLE_DATE_FORMAT));
            record.setCancelTimeStr(DateUtil.format(record.getCancelTime(), FsDateUtils.SIMPLE_DATETIME_FORMAT));
            // 判断是否提前结束报名任务
            if (BooleanEnum.YES.getValue().equals(record.getAheadEndApplyFlag())) {
                record.setAheadEndApplyTimeStr(DateUtil.format(record.getAheadEndApplyTime(), FsDateUtils.SIMPLE_DATETIME_FORMAT));
            }
            if (record.getCancelTimeStr().contains(CommonConstant.DEFAULT_TIME)) {
                record.setCancelTimeStr(StringUtils.EMPTY);
            }
            String auditTime = record.getAuditTime();
            if (CommonConstant.INITIAL_TIME_STR.equals(auditTime)) {
                record.setAuditTime(StringPool.EMPTY);
            }
            // 记录数量
            List<StarTaskOperatingRecordDO> list = recordMap.getOrDefault(taskId, Lists.newArrayList());
            record.setRecordNumber(list.size());
            // 联系人姓名
            StarTaskOperatorDO operatorDO = operatorDOMap.getOrDefault(record.getFollowUpPersonId(), new StarTaskOperatorDO());
            record.setFollowUpPersonName(operatorDO.getContactName());
        }
        return starTaskWebTaskServiceObjMapper.toWebTaskListModel(activityPage);
    }

    /**
     * 列表导出
     *
     * @param param 入参
     */
    @Override
    public void requestExport(WebTaskListParam param) {
        // 获取登录态信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        WebTaskListParamDTO query = starTaskWebTaskServiceObjMapper.toWebTaskListParamDTO(param);
        String phoneNumber = query.getPhoneNumber();
        query.setAgentId(basicInfo.getAgentId());
        if (StringUtils.isNotEmpty(phoneNumber)) {
            query.setPhoneNumber(FieldEncryptUtil.encode(phoneNumber));
        }
        String createEndDate = query.getCreateEndDate();
        String createStartDate = query.getCreateStartDate();
        if (StringUtils.isNotBlank(createEndDate) && StringUtils.isNotBlank(createStartDate)) {
            query.setCreateStartDate(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(createStartDate, DatePattern.NORM_DATE_PATTERN))));
            query.setCreateEndDate(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(createEndDate, DatePattern.NORM_DATE_PATTERN))));
        }
        String auditEndDate = query.getAuditEndDate();
        String auditStartDate = query.getAuditStartDate();
        if (StringUtils.isNotBlank(auditEndDate) && StringUtils.isNotBlank(auditStartDate)) {
            query.setAuditStartDate(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(auditStartDate, DatePattern.NORM_DATE_PATTERN))));
            query.setAuditEndDate(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(auditEndDate, DatePattern.NORM_DATE_PATTERN))));
        }
        // 异步导出
        starTaskWebExportService.addAgentExportTask(OperateExportBusinessTypeEnum.AGENT_WEB_TASK_LIST, query, basicInfo);
    }

    /**
     * 报名清单导出
     *
     * @param param
     */
    @Override
    public void requestExportApplyList(WebApplyListParam param) {
        // 获取登录态信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        val query = starTaskWebTaskServiceObjMapper.toWebApplyListParamDTO(param);
        if (StringUtils.isNotEmpty(param.getPhoneNumber())) {
            query.setPhoneNumber(FieldEncryptUtil.encode(param.getPhoneNumber()));
        }
        // 异步导出
        starTaskWebExportService.addAgentExportTask(OperateExportBusinessTypeEnum.AGENT_WEB_TASK_TASK_APPLY_LIST, query, basicInfo);
    }

    /**
     * 构建任务进度数量
     *
     * @param record         任务信息
     * @param applyList      报名清单信息
     * @param commissionPlan 佣金计划
     */
    private void buildListTaskProgressQuantity(WebTaskListResultDTO record, List<StarTaskApplyListDO> applyList, List<StarTaskCommissionPlanDO> commissionPlan) {
        // 总需求人数
        int totalNeededCount = CommonConstant.ZERO;
        // 报名人数
        int applyNumber = CommonConstant.ZERO;
        // 提交链接数量
        int numberOfSubmittedLinks = CommonConstant.ZERO;
        // 已完成人数
        int finishedNumber = CommonConstant.ZERO;

        for (StarTaskCommissionPlanDO item : commissionPlan) {
            totalNeededCount += item.getNeededCount();
        }
        for (StarTaskApplyListDO item : applyList) {
            Integer applyStatus = item.getApplyStatus();
            if (!ApplyListStatusEnum.getNotApplyCount.contains(applyStatus)) {
                applyNumber += 1;
            }
            if (!ApplyListStatusEnum.CANCELLED.getValue().equals(applyStatus) && !ApplyListStatusEnum.WAITING_SUBMIT.getValue().equals(applyStatus)
                    && !ApplyListStatusEnum.MERCHANT_PRE_AUDIT.getValue().equals(applyStatus) && !ApplyListStatusEnum.MERCHANT_PRE_REFUND.getValue().equals(applyStatus)) {
                numberOfSubmittedLinks += 1;
            }
            if (ApplyListStatusEnum.COMPLETED.getValue().equals(applyStatus) || ApplyListStatusEnum.PLATFORM_COMPLETED.getValue().equals(applyStatus)) {
                finishedNumber += 1;
            }
        }
        record.setTotalApplyNumber(applyList.size());
        record.setTotalNeededCount(totalNeededCount);
        record.setApplyNumber(applyNumber);
        record.setNumberOfSubmittedLinks(numberOfSubmittedLinks);
        record.setFinishedNumber(finishedNumber);
    }
}