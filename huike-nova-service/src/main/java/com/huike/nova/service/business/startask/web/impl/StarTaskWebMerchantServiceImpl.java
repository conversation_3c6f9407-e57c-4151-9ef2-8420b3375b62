package com.huike.nova.service.business.startask.web.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.startask.mina.StarTaskMinaRedisPrefixConstant;
import com.huike.nova.common.constant.startask.web.StarTaskWebCommonConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.SmsTypeEnum;
import com.huike.nova.common.enums.startask.IdentityWithdrawStatusEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskAccountStatusEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogChangeTypeEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogRemarkTypeEnum;
import com.huike.nova.common.enums.startask.web.DistributionTypeEnum;
import com.huike.nova.common.enums.startask.web.OperateEmployeeTypeEnum;
import com.huike.nova.common.enums.startask.web.WebRechargeTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.common.util.ExcelWriterHelper;
import com.huike.nova.common.util.ExceptionUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.param.startask.PageFindInviterListDTO;
import com.huike.nova.dao.domain.param.startask.WebMerchantListParamDTO;
import com.huike.nova.dao.domain.result.StarTaskBalanceLogResultDTO;
import com.huike.nova.dao.domain.result.startask.FindInviterListByInviterPhoneListDTO;
import com.huike.nova.dao.domain.result.startask.PageFindInviterListResultDTO;
import com.huike.nova.dao.domain.result.startask.WebMerchantListResultDTO;
import com.huike.nova.dao.entity.AilikeGaodeCodeDO;
import com.huike.nova.dao.entity.StarTaskAgentDO;
import com.huike.nova.dao.entity.StarTaskBalanceAccountDO;
import com.huike.nova.dao.entity.StarTaskBalanceLogDO;
import com.huike.nova.dao.entity.StarTaskIdentityDO;
import com.huike.nova.dao.entity.StarTaskIdentityRelationDO;
import com.huike.nova.dao.entity.StarTaskUserDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.AilikeGaodeCodeDAO;
import com.huike.nova.dao.repository.StarTaskAgentDAO;
import com.huike.nova.dao.repository.StarTaskBalanceAccountDAO;
import com.huike.nova.dao.repository.StarTaskBalanceLogDAO;
import com.huike.nova.dao.repository.StarTaskIdentityDAO;
import com.huike.nova.dao.repository.StarTaskIdentityRelationDAO;
import com.huike.nova.dao.repository.StarTaskUserDAO;
import com.huike.nova.service.business.AilikeGaodeCodeService;
import com.huike.nova.service.business.startask.web.StarTaskWebExportService;
import com.huike.nova.service.business.startask.web.StarTaskWebMerchantService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.web.StarTaskWebMerchantServiceObjMapper;
import com.huike.nova.service.domain.model.startask.web.login.StarTaskWebLoginModel;
import com.huike.nova.service.domain.model.startask.web.merchant.DefaultDistributionRateModel;
import com.huike.nova.service.domain.model.startask.web.merchant.PageFindInviterListModel;
import com.huike.nova.service.domain.model.startask.web.merchant.QueryDefaultTaskMoneyModel;
import com.huike.nova.service.domain.model.startask.web.merchant.WebMerchantListModel;
import com.huike.nova.service.domain.param.startask.web.merchant.ModifyWithdrawalPermissionParam;
import com.huike.nova.service.domain.param.startask.web.merchant.PageFindInviterListParam;
import com.huike.nova.service.domain.param.startask.web.merchant.UpdateDistributionRateParam;
import com.huike.nova.service.domain.param.startask.web.merchant.UpdateInviterParam;
import com.huike.nova.service.domain.param.startask.web.merchant.UpdateTaskMoneyParam;
import com.huike.nova.service.domain.param.startask.web.merchant.WebMerchantListParam;
import com.huike.nova.service.domain.param.startask.web.merchant.WebMerchantOperateParam;
import com.huike.nova.service.domain.param.startask.web.merchant.WebMerchantRechargeParam;
import com.huike.nova.service.domain.result.startask.web.StarTaskMerchantResult;
import io.reactivex.Observable;
import jodd.util.StringPool;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年12月06日 17:05
 */
@Slf4j
@Service
@AllArgsConstructor
public class StarTaskWebMerchantServiceImpl implements StarTaskWebMerchantService {

    private StarTaskWebMerchantServiceObjMapper starTaskWebMerchantServiceObjMapper;

    private StarTaskBalanceAccountDAO starTaskBalanceAccountDAO;

    private StarTaskBalanceLogDAO starTaskBalanceLogDAO;

    private StarTaskIdentityDAO starTaskIdentityDAO;

    private TransactionTemplate transactionTemplate;

    private RedissonClient redissonClient;

    private StarTaskWebExportService starTaskWebExportService;

    @Autowired
    private SysConfig sysConfig;

    private StarTaskUserDAO starTaskUserDAO;

    private StarTaskIdentityRelationDAO starTaskIdentityRelationDAO;

    private AilikeGaodeCodeDAO ailikeGaodeCodeDAO;

    private StarTaskAgentDAO starTaskAgentDAO;

    private AilikeGaodeCodeService ailikeGaodeCodeService;


    /**
     * 商家列表
     *
     * @param param 入参
     * @return 出参
     */
    @Override
    public PageResult<WebMerchantListModel> list(PageParam<WebMerchantListParam> param) {
        // 获取登录信息
        StarTaskWebLoginModel loginBasicInfo = LoginUtil.getWebStarTaskLoginBasicInfo();
        WebMerchantListParam paramQuery = param.getQuery();
        PageParam<WebMerchantListParamDTO> pageParam = starTaskWebMerchantServiceObjMapper.toWebMerchantListParamDTO(param);
        WebMerchantListParamDTO query = pageParam.getQuery();
        String startDate = paramQuery.getStartDate();
        String endDate = paramQuery.getEndDate();
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            query.setStartTime(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(startDate, DatePattern.NORM_DATE_PATTERN))));
            query.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(endDate, DatePattern.NORM_DATE_PATTERN))));
        }
        String merchantAccountNumber = paramQuery.getMerchantAccountNumber();
        if (StringUtils.isNotBlank(merchantAccountNumber)) {
            query.setPhoneNumber(FieldEncryptUtil.encode(merchantAccountNumber));
            query.setMerchantName(merchantAccountNumber);
        }
        String inviter = paramQuery.getInviter();
        if (StringUtils.isNotBlank(inviter)) {
            query.setInviterPhone(FieldEncryptUtil.encode(inviter));
            query.setInviterMerchantName(inviter);
        }
        query.setAppletId(loginBasicInfo.getAppletId());
        query.setIdentityType(StarTaskIdentityTypeEnum.MERCHANT.getValue());
        //查询商家信息
        Page<WebMerchantListResultDTO> activityPage = starTaskIdentityDAO.pageList(pageParam);
        List<WebMerchantListResultDTO> dtoList = activityPage.getRecords();
        List<String> inviterPhoneList = dtoList.stream().map(WebMerchantListResultDTO::getInviterPhone).collect(Collectors.toList());
        List<FindInviterListByInviterPhoneListDTO> inviterList = starTaskIdentityDAO.findInviterListByInviterPhoneList(inviterPhoneList);
        //查询邀请商家列表数据
        List<String> identityIdList = dtoList.stream().map(WebMerchantListResultDTO::getIdentityId).collect(Collectors.toList());
        List<StarTaskIdentityRelationDO> listByParentIdList = CollectionUtil.newArrayList();
        Map<String, StarTaskBalanceLogResultDTO> distributionMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(identityIdList)) {
            listByParentIdList = starTaskIdentityRelationDAO.findListByParentIdList(identityIdList);
            List<StarTaskBalanceLogResultDTO> list = starTaskBalanceLogDAO.statisticsDistributionRewardsAmount(identityIdList);
            if (CollectionUtils.isNotEmpty(list)) {
                distributionMap = list.stream().collect(Collectors.toMap(StarTaskBalanceLogResultDTO::getIdentityId, Function.identity()));
            }
        }
        // 省市区
        List<String> adCodeList = dtoList.stream().map(WebMerchantListResultDTO::getProvince).distinct().collect(Collectors.toList());
        adCodeList.addAll(dtoList.stream().map(WebMerchantListResultDTO::getCity).distinct().collect(Collectors.toList()));
        List<AilikeGaodeCodeDO> codeInfoList = ailikeGaodeCodeDAO.findCodeInfoList(adCodeList);
        Map<String, AilikeGaodeCodeDO> adCodeMap = Maps.newConcurrentMap();
        if (CollectionUtil.isNotEmpty(codeInfoList)) {
            adCodeMap = codeInfoList.stream().collect(Collectors.toMap(AilikeGaodeCodeDO::getAdcode, Function.identity()));
        }

        Map<String, String> orderRefundTypeMap = inviterList.stream().collect(Collectors.toMap(FindInviterListByInviterPhoneListDTO::getInviterPhone, FindInviterListByInviterPhoneListDTO::getInviterName));
        for (WebMerchantListResultDTO record : dtoList) {
            record.setPhoneNumber(FieldEncryptUtil.decode(record.getPhoneNumber()));
            if (CommonConstant.INTEGER_ONE.equals(record.getTaskMoneyType())) {
                record.setMinTaskMoney(new BigDecimal(sysConfig.getStarTaskMinaMinTaskMoney()));
                record.setPlatformRate(new BigDecimal(sysConfig.getStarTaskMinaPlatformRate()));
            }
            record.setInviterName(orderRefundTypeMap.getOrDefault(record.getInviterPhone(), StringPool.EMPTY));
            record.setInviterPhone(FieldEncryptUtil.decode(record.getInviterPhone()));
            //邀请商家数
            record.setInviterMerchantCount(CommonConstant.ZERO);
            if (CollectionUtil.isNotEmpty(listByParentIdList)) {
                record.setInviterMerchantCount(listByParentIdList.stream().filter(item -> item.getParentId().equals(record.getIdentityId())).collect(Collectors.toList()).size());
            }
            // 商户分销奖励
            record.setDistributionAwardAmount(CommonConstant.ZERO.toString());
            if (Objects.nonNull(distributionMap.get(record.getIdentityId()))) {
                record.setDistributionAwardAmount(distributionMap.get(record.getIdentityId()).getDistributionRewardsAmount().toString());
            }
            //分销比例
            if (DistributionTypeEnum.DEFAULT.getValue().equals(record.getDistributionType())) {
                record.setDistributionRate(sysConfig.getStarTaskMinaMerchantDistributionRate());
            }
            // 省市区
            AilikeGaodeCodeDO provinceInfo = adCodeMap.get(record.getProvince());
            AilikeGaodeCodeDO cityInfo = adCodeMap.get(record.getCity());
            record.setProvinceName(Objects.nonNull(provinceInfo) ? provinceInfo.getAdname() : StrUtil.EMPTY);
            record.setCityName(Objects.nonNull(cityInfo) ? cityInfo.getAdname() : StrUtil.EMPTY);
        }
        return starTaskWebMerchantServiceObjMapper.toActivityDetailModel(activityPage);
    }

    /**
     * 商家操作
     *
     * @param param 入参
     */
    @Override
    public void operate(WebMerchantOperateParam param) {
        Integer operateType = param.getOperateType();
        StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(param.getIdentityId());
        if (null == identityDO) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息异常");
        }
        if (identityDO.getAccountStatus().equals(operateType)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("状态已变更，请稍后再试");
        }
        starTaskIdentityDAO.updateAccountStatusByIdentityId(param.getIdentityId(), operateType);
        if (OperateEmployeeTypeEnum.STOPPED.getValue().equals(operateType)) {
            // 退出登录
            StpUtil.logout(identityDO.getUserId());
        }
    }

    /**
     * 商家充值
     *
     * @param param 入参
     */
    @Override
    public void recharge(WebMerchantRechargeParam param) {
        WebRechargeTypeEnum typeEnum = WebRechargeTypeEnum.getByValue(param.getType());
        if (Objects.isNull(typeEnum)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("充值类型异常");
        }
        // 加锁
        String lock = StrUtil.format(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_APPLY_WITHDRAWAL, param.getIdentityId()));
        try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
            redisLockHelper.tryLock();
            // 输入的验证码
            String code = param.getCode();
            String identityId = param.getIdentityId();
            BigDecimal rechargeAmount = param.getRechargeAmount();
            // 获取登录信息
            StarTaskWebLoginModel loginBasicInfo = LoginUtil.getWebStarTaskLoginBasicInfo();
            // 获取发送的验证码
            final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_SMS_VERIFY,
                    loginBasicInfo.getAppletId(), SmsTypeEnum.STAR_TASK_WEB_MERCHANT_RECHARGE.getValue(), param.getPhoneNumber()), StringCodec.INSTANCE);
            String smdCode = bucket.get();
            if (StringUtils.isBlank(smdCode)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("验证码已失效，请重新发送");
            }
            if (Objects.isNull(code) || !smdCode.equals(code)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("验证码错误，请重新输入");
            }
            // 事务
            transactionTemplate.execute(status -> {
                StarTaskBalanceAccountDO accountDO = starTaskBalanceAccountDAO.getBalanceAccountForUpdate(identityId);
                if (null == accountDO) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账户信息异常");
                }
                // 如果充值的金额是负数，需要分别校验余额
                if (accountDO.getAvailableBalance().add(param.getRechargeAmount()).compareTo(BigDecimal.ZERO) == -1) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("余额不足");
                }
                StarTaskBalanceLogDO insertDO;
                switch (typeEnum) {
                    case TASK_GOLD:
                        param.setRemarkType(BalanceLogRemarkTypeEnum.ADMIN_RECHARGE.getValue());
                        param.setChangeRemark(param.getApprovalNumber());
                        // 构建充值日志
                        insertDO = this.buildBalanceLog(accountDO, param, loginBasicInfo);
                        // 充值
                        starTaskBalanceAccountDAO.rechargeByIdentityId(identityId, rechargeAmount);
                        break;
                    case RETURN_REWARD:
                        param.setRemarkType(BalanceLogRemarkTypeEnum.RETURN_REWARD.getValue());
                        param.setChangeRemark(param.getApprovalNumber());
                        // 构建返还奖励金日志
                        insertDO = this.buildBalanceLog(accountDO, param, loginBasicInfo);
                        if (accountDO.getRewardBalance().add(param.getRechargeAmount()).compareTo(BigDecimal.ZERO) == -1) {
                            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("余额不足");
                        }
                        starTaskBalanceAccountDAO.updateReturnReward(identityId, rechargeAmount);
                        break;
                    case INVITATION_REWARD:
                        param.setRemarkType(BalanceLogRemarkTypeEnum.INVITE_REWARD.getValue());
                        param.setChangeRemark(param.getApprovalNumber());
                        // 构建邀请奖励金日志
                        insertDO = this.buildBalanceLog(accountDO, param, loginBasicInfo);
                        if (accountDO.getRewardBalance().add(param.getRechargeAmount()).compareTo(BigDecimal.ZERO) == -1) {
                            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("余额不足");
                        }
                        starTaskBalanceAccountDAO.updateReturnReward(identityId, rechargeAmount);
                        break;
                    default:
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("充值类型异常");
                }
                starTaskBalanceLogDAO.saveBalanceLog(insertDO);
                return Boolean.TRUE;
            });
            // 删除缓存，一个验证码只能使用一次
            deleteBucket(bucket, StarTaskWebCommonConstant.DELETE_BUCKET_MAX_RETRY_COUNT, StarTaskWebCommonConstant.NETWORK_EXCEPTION_SLEEP_TIME);
        } catch (Exception e) {
            LogUtil.warn(log, "StarTaskWebMerchantServiceImpl" + " >>>>> " + "recharge" + "  锁释放失败", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }

    /**
     * 商家导出
     *
     * @param param 入参
     */
    @Override
    public void requestExport(WebMerchantListParam param) {
        val queryParam = starTaskWebMerchantServiceObjMapper.toWebMerchantListParamDTO(param);
        String startDate = param.getStartDate();
        String endDate = param.getEndDate();
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            queryParam.setStartTime(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(startDate, DatePattern.NORM_DATE_PATTERN))));
            queryParam.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(endDate, DatePattern.NORM_DATE_PATTERN))));
        }
        String merchantAccountNumber = param.getMerchantAccountNumber();
        if (StringUtils.isNotBlank(merchantAccountNumber)) {
            queryParam.setPhoneNumber(FieldEncryptUtil.encode(merchantAccountNumber));
            queryParam.setMerchantName(merchantAccountNumber);
        }
        String inviter = param.getInviter();
        if (StringUtils.isNotBlank(inviter)) {
            String inviterPhone = FieldEncryptUtil.encode(inviter);
            queryParam.setInviterPhone(inviterPhone);
            queryParam.setInviterMerchantName(inviter);
        }
        val loginModel = LoginUtil.getWebStarTaskLoginBasicInfo();
        queryParam.setAppletId(loginModel.getAppletId());
        queryParam.setIdentityType(StarTaskIdentityTypeEnum.MERCHANT.getValue());
        starTaskWebExportService.applyExportMerchantList(queryParam, loginModel);
    }

    @Override
    public Observable<File> export(String fileName, WebMerchantListParamDTO param) {

        return Observable.create(emitter -> {
            try (val writer = ExcelWriterHelper.create(fileName, StarTaskMerchantResult.class)) {
                // 设置读取参数
                starTaskIdentityDAO.export(param, resultContext -> {
                    if (Objects.nonNull(resultContext)) {
                        val data = resultContext.getResultObject();
                        String identityId = data.getIdentityId();
                        StarTaskIdentityDO identityInfo = starTaskIdentityDAO.getIdentityByPhone(data.getPhoneNumber(), StarTaskIdentityTypeEnum.MERCHANT.getValue(), param.getAppletId());
                        List<StarTaskIdentityRelationDO> listByParentIdList = starTaskIdentityRelationDAO.findListByParentId(identityId);
                        StarTaskBalanceLogResultDTO starTaskBalanceLogResultDTO = starTaskBalanceLogDAO.statisticsDistributionRewardsAmountByIdentityId(identityId);
                        BigDecimal merchantDistributionRate = sysConfig.getStarTaskMinaMerchantDistributionRate();
                        // 查询代理商信息
                        if (StringUtils.isNotBlank(param.getAgentId())) {
                            StarTaskAgentDO agentInfo = starTaskAgentDAO.getAgentInfo(param.getAgentId());
                            if (Objects.nonNull(agentInfo)) {
                                merchantDistributionRate = agentInfo.getMerchantRate();
                            }
                        }
                        // 查询省市名称
                        List<String> list = Lists.newArrayList();
                        list.add(data.getProvince());
                        list.add(data.getCity());
                        String fullAddress = ailikeGaodeCodeService.getSplicingAreaName(list, StringPool.DASH);
                        // 提现权限状态
                        IdentityWithdrawStatusEnum withdrawStatusEnum = IdentityWithdrawStatusEnum.getByValue(data.getHasWithdraw());
                        val result = new StarTaskMerchantResult()
                                .setPhoneNumber(FieldEncryptUtil.decode(data.getPhoneNumber()))
                                .setCreateTime(data.getCreateTime())
                                .setTotalIncome(data.getTotalIncome().toString())
                                .setAccountStatus(StarTaskAccountStatusEnum.getByStatusCode(data.getAccountStatus()).getDesc())
                                .setInviterName(null == identityInfo ? StringPool.EMPTY : identityInfo.getMerchantName())
                                .setInviterPhone(StringUtils.isNotBlank(data.getInviterPhone()) ? FieldEncryptUtil.decode(data.getInviterPhone()) : StringPool.EMPTY)
                                .setDistributionType(data.getDistributionType())
                                .setDistributionRate(DistributionTypeEnum.DEFAULT.getValue().equals(data.getDistributionType()) ? merchantDistributionRate.toString() : data.getDistributionRate().toString())
                                .setInviterMerchantCount(CollectionUtil.isNotEmpty(listByParentIdList) ? (int) listByParentIdList.stream().filter(item -> item.getParentId().equals(identityId)).count() : CommonConstant.ZERO)
                                .setDistributionAwardAmount(null != starTaskBalanceLogResultDTO ? starTaskBalanceLogResultDTO.getDistributionRewardsAmount().toString() : StringPool.ZERO)
                                .setWithdrawableBalance(data.getWithdrawableBalance().toString())
                                .setHasWithdraw(Objects.isNull(withdrawStatusEnum) ? StringPool.EMPTY : withdrawStatusEnum.getName())
                                .setCityName(StringUtils.isNotBlank(fullAddress) ? fullAddress : StringPool.EMPTY);
                        writer.write(result);
                    }
                });
                emitter.onNext(writer.finish());
                emitter.onComplete();
            } catch (Exception ex) {
                LogUtil.info(log, "StarTaskWebMerchantServiceImpl.export >> 商家导出全局异常 >> fileName = {}", ex, fileName);
                emitter.onError(ex);
            }
        });
    }

    /**
     * 更新任务金额
     *
     * @param param 入参
     */
    @Override
    public void updateTaskMoney(UpdateTaskMoneyParam param) {
        String identityId = param.getIdentityId();
        Integer taskMoneyType = param.getTaskMoneyType();
        // 加锁
        RLock lock = redissonClient.getLock(StrUtil.format(StarTaskWebCommonConstant.CHANGE_MERCHANT_TASK_MONEY_LOCK, identityId));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                LogUtil.info(log, "StarTaskWebMerchantServiceImpl.updateTaskMoney >>    锁获取失败  >> param = {}", param);
                throw new CommonException(ErrorCodeEnum.GET_LOCK_ERROR).detailMessage("锁获取失败");
            }

            StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(identityId);
            if (null == identityDO) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息异常");
            }
            if (CommonConstant.INTEGER_ONE.equals(taskMoneyType)) {
//                默认值
                param.setMinTaskMoney(new BigDecimal(sysConfig.getStarTaskMinaMinTaskMoney()));
                param.setPlatformRate(new BigDecimal(sysConfig.getStarTaskMinaPlatformRate()));
            } else {
//                校验任务金是否在0-10000之间并且最多只有两位小数
                validateMinTaskMoney(param.getMinTaskMoney());
//                校验任务金
                validatePlatformCommissionRate(param.getPlatformRate());
            }
            starTaskIdentityDAO.updateTaskMoney(identityId, param.getMinTaskMoney(), param.getPlatformRate(), taskMoneyType);

        } catch (CommonException e) {
            LogUtil.info(log, "StarTaskWebMerchantServiceImpl.updateTaskMoney >> 通用异常 >> param = {}", param);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage(e.getMsg());
        } catch (Exception e) {
            LogUtil.info(log, "StarTaskWebMerchantServiceImpl.updateTaskMoney >> 全局异常 >> param = {}", e, param);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("全局异常");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.info(log, "StarTaskWebMerchantServiceImpl.updateTaskMoney >> 锁释放失败 >> param = {}", param);
            }
        }
    }

    /**
     * 校验平台佣金比例
     *
     * @param platformCommissionRate 平台佣金比例
     */
    private void validatePlatformCommissionRate(BigDecimal platformCommissionRate) {
        // 检查数值范围是否在0到100之间
        boolean withinRange = platformCommissionRate.compareTo(BigDecimal.ZERO) >= 0 && platformCommissionRate.compareTo(new BigDecimal("100")) <= 0;

        // 检查小数位数是否不超过两位
        boolean validDecimalPlaces = platformCommissionRate.scale() <= 2;

        if (!withinRange || !validDecimalPlaces) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("平台佣金比例超出范围，当前范围为0.00-100.00");
        }
    }

    /**
     * 校验任务金额
     *
     * @param minTaskMoney 任务金额
     */
    public void validateMinTaskMoney(BigDecimal minTaskMoney) {
        // 检查数值范围是否在0到10000之间
        boolean withinRange = minTaskMoney.compareTo(BigDecimal.ZERO) >= 0 && minTaskMoney.compareTo(new BigDecimal("10000")) <= 0;

        // 检查小数位数是否不超过两位
        boolean validDecimalPlaces = minTaskMoney.scale() <= 2;

        if (!withinRange || !validDecimalPlaces) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("最小任务金取值超出范围，当前范围为0.00-10000.00");
        }

    }

    /**
     * 查询默认任务金额
     *
     * @return 出参
     */
    @Override
    public QueryDefaultTaskMoneyModel queryDefaultTaskMoney() {
        QueryDefaultTaskMoneyModel model = new QueryDefaultTaskMoneyModel();
        model.setMinTaskMoney(new BigDecimal(sysConfig.getStarTaskMinaMinTaskMoney()));
        model.setPlatformRate(new BigDecimal(sysConfig.getStarTaskMinaPlatformRate()));
        return model;
    }

    /**
     * 邀请人分页列表
     *
     * @param param 入参
     * @return 出参
     */
    @Override
    public PageResult<PageFindInviterListModel> pageFindInviterList(PageParam<PageFindInviterListParam> param) {
        PageParam<PageFindInviterListDTO> pageDTO = starTaskWebMerchantServiceObjMapper.toPageFindInviterListDTO(param);
        PageFindInviterListDTO query = pageDTO.getQuery();
        String inviter = query.getInviter();
        if (StringUtils.isNotBlank(inviter)) {
            query.setInviterPhone(FieldEncryptUtil.encode(inviter));
        }
        Page<PageFindInviterListResultDTO> inviterPage = starTaskIdentityDAO.pageFindInviterList(pageDTO);
        PageResult<PageFindInviterListModel> result = starTaskWebMerchantServiceObjMapper.toPageFindInviterListModel(inviterPage);
        for (PageFindInviterListModel item : result.getRecords()) {
            item.setInviterPhone(FieldEncryptUtil.decode(item.getInviterPhone()));
        }
        return result;
    }

    /**
     * 修改邀请人
     *
     * @param param 入参
     */
    @Override
    public void updateInviter(UpdateInviterParam param) {
        String identityId = param.getIdentityId();
        StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(identityId);
        if (null == identityDO) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息异常，请刷新后再试");
        }
        StarTaskUserDO starTaskUserDO = starTaskUserDAO.getUserInfoByUserId(identityDO.getUserId());
        if (null == starTaskUserDO) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("用户信息异常，请刷新后再试");
        }
        if (FieldEncryptUtil.decode(starTaskUserDO.getPhoneNumber()).equals(param.getInviterPhone())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("归属邀请人不能选择本人");
        }
        StarTaskIdentityDO inviterIdentityDo = starTaskIdentityDAO.getIdentityByIdentityId(param.getInviterIdentityId());
        if (Objects.isNull(inviterIdentityDo)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("邀请人的身份不存在");
        }

        StarTaskIdentityRelationDO identityRelationDO = starTaskIdentityRelationDAO.findListByIdentityId(identityId);
        if (Objects.isNull(identityRelationDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("达人身份关系信息不存在");
        }
        identityRelationDO.setParentId(param.getInviterIdentityId());
        identityRelationDO.setUpdateTime(new Date());
        starTaskIdentityRelationDAO.updateById(identityRelationDO);
    }

    /**
     * 查询商家和达人的默认分销比例
     */
    @Override
    public DefaultDistributionRateModel findDefaultDistributionRate() {
        DefaultDistributionRateModel model = new DefaultDistributionRateModel();
        model.setMerchantDefaultDistributionRate(sysConfig.getStarTaskMinaMerchantDistributionRate());
        model.setStarDefaultDistributionRate(sysConfig.getStarTaskMinaStarDistributionRate());
        return model;
    }

    /**
     * 更新分销比例
     *
     * @param param 入参
     */
    @Override
    public void updateDistributionRate(UpdateDistributionRateParam param) {
        String identityId = param.getIdentityId();
        // 加锁
        RLock lock = redissonClient.getLock(StrUtil.format(StrUtil.format(StarTaskWebCommonConstant.CHANGE_DISTRIBUTION_RATE_LOCK, identityId)));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                LogUtil.info(log, "StarTaskWebMerchantServiceImpl.updateDistributionRate >>    锁获取失败  >> param = {}", param);
                throw new CommonException(ErrorCodeEnum.GET_LOCK_ERROR).detailMessage("锁获取失败");
            }
            //查询身份关系数据
            StarTaskIdentityRelationDO identityRelationDO = starTaskIdentityRelationDAO.findListByIdentityId(param.getIdentityId());
            if (Objects.isNull(identityRelationDO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份关系数据不存在");
            }
            DistributionTypeEnum distributionTypeEnum = DistributionTypeEnum.getByValue(param.getDistributionType());
            if (Objects.isNull(distributionTypeEnum)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("分销类型参数错误");
            }
            //修改分销比例
            identityRelationDO.setDistributionType(param.getDistributionType());
            identityRelationDO.setDistributionRate(param.getDistributionRate());
            identityRelationDO.setUpdateTime(new Date());
            starTaskIdentityRelationDAO.updateById(identityRelationDO);
        } catch (CommonException e) {
            LogUtil.info(log, "StarTaskWebMerchantServiceImpl.updateDistributionRate >> 通用异常 >> param = {}", param);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage(e.getMsg());
        } catch (Exception e) {
            LogUtil.info(log, "StarTaskWebMerchantServiceImpl.updateDistributionRate >> 全局异常 >> param = {}", e, param);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("全局异常");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.info(log, "StarTaskWebMerchantServiceImpl.updateDistributionRate >> 锁释放失败 >> param = {}", param);
            }
        }
    }

    /**
     * 提现权限修改
     *
     * @param param
     */
    @Override
    public void modifyWithdrawalPermission(ModifyWithdrawalPermissionParam param) {
        String identityId = param.getIdentityId();
        // 查询身份信息
        StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(identityId);
        if (Objects.isNull(identityDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息不存在");
        }
        // 修改提现权限
        starTaskIdentityDAO.updateWithdrawalPermission(identityId, param.getHasWithdraw());
    }


    /**
     * 删除缓存(重试机制)
     *
     * @param bucket     缓存
     * @param maxRetries 最大重试次数
     * @param sleepTime  线程休眠时间（秒）
     */
    private void deleteBucket(RBucket<String> bucket, Integer maxRetries, Integer sleepTime) {
        // 删除缓存并添加重试机制
        // 当前重试次数
        int retries = StarTaskWebCommonConstant.DELETE_BUCKET_INIT_RETRY_COUNT;
        // 缓存删除状态
        boolean cacheDeleted = Boolean.FALSE;
        // 重试
        while (!cacheDeleted && retries < maxRetries) {
            try {
                // 删除缓存
                bucket.delete();
                // 标记缓存已删除
                cacheDeleted = Boolean.TRUE;
            } catch (Exception e) {
                retries++;
                try {
                    // 每次休眠
                    Thread.sleep(sleepTime);
                } catch (InterruptedException ex) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("线程休眠异常");
                }
            }
        }
        if (cacheDeleted) {
            LogUtil.info(log, "StarTaskWebMerchantServiceImpl.deleteBucket >> 缓存删除成功 >> bucket = {}", bucket.get());
        } else {
            LogUtil.info(log, "StarTaskWebMerchantServiceImpl.deleteBucket >> 缓存删除异常 >> bucket = {}", bucket.get());
        }
    }

    /**
     * 构建充值日志
     *
     * @param accountDO      账户信息
     * @param param          入参
     * @param loginBasicInfo 当前登录信息
     * @return 充值日志
     */
    private StarTaskBalanceLogDO buildBalanceLog(StarTaskBalanceAccountDO accountDO, WebMerchantRechargeParam param, StarTaskWebLoginModel loginBasicInfo) {
        BigDecimal rechargeAmount = param.getRechargeAmount();
        BigDecimal withdrawableBalance = accountDO.getAvailableBalance();
        StarTaskBalanceLogDO insertDO = new StarTaskBalanceLogDO();
        insertDO.setUserId(accountDO.getUserId());
        insertDO.setIdentityId(accountDO.getIdentityId());
        insertDO.setChangeType(BalanceLogChangeTypeEnum.ADDED.getValue());
        insertDO.setRemarkType(param.getRemarkType());
        insertDO.setChangeRemark(param.getChangeRemark());
        insertDO.setChangeAmount(rechargeAmount);
        insertDO.setChangeAvailableBalance(withdrawableBalance);
        insertDO.setAfterAvailableBalance(withdrawableBalance.add(rechargeAmount));
        insertDO.setChangeExpenditureTotalIncome(accountDO.getExpenditureTotalIncome());
        insertDO.setAfterExpenditureTotalIncome(accountDO.getExpenditureTotalIncome());
        // 操作人信息
        String operatorId = loginBasicInfo.getOperatorId();
        String contactName = loginBasicInfo.getContactName();
        insertDO.setOperatorId(operatorId);
        insertDO.setOperatorName(contactName);
        return insertDO;
    }
}
