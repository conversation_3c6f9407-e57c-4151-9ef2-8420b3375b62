package com.huike.nova.service.business;

import com.alibaba.fastjson.JSONObject;
import com.annimon.stream.function.Consumer;
import com.huike.nova.service.domain.model.amap.AmapGeoCodeModel;
import com.huike.nova.service.domain.model.amap.ReGeoAddressComponentModel;
import com.huike.nova.service.domain.model.enterprise.GetKsAuthUrlModel;
import com.huike.nova.service.domain.model.enterprise.SaveKsAuthUserModel;
import com.huike.nova.service.domain.model.enterprise.ShortUrlTicketModel;
import com.huike.nova.service.domain.model.releasevideo.GetUserInfoModel;
import com.huike.nova.service.domain.param.enterprise.*;

import javax.annotation.Nullable;
import java.io.File;
import java.util.List;

/**
 * 企业能力接口
 *
 * <AUTHOR> (<EMAIL>)
 * @version EnterpriseService.java, v1.0 06/19/2023 18:55 John Exp$
 */
public interface EnterpriseService {

    /**
     * 获得抖音授权链接地址
     *
     * @param clientKey 网页应用ClientKey
     * @param state     携带参数，在获得Code后会回调原有State
     * @return 抖音授权链接
     */
    String getDouyinAuthUrl(String clientKey, String state);

    /**
     * 根据Ticket获取Url
     *
     * @param ticket 事先请求的Ticket
     * @return ticket对应的Url链接
     */
    String getUrlByTicket(@Nullable String ticket);

    /**
     * 根据Ticket和UserAgent获得Url
     *
     * @param ticket    ticket
     * @param userAgent userAgent
     * @return Url地址
     */
    String getUrlByTicketAndUserAgent(@Nullable String ticket, @Nullable String userAgent);

    /**
     * 保存矩阵用户信息
     *
     * @param clientKey 网页应用clientKey
     * @param code      授权获得的Code信息
     * @param state     授权获得的透传参数的信息
     * @param scopes    授权获得的权限
     */
    GetUserInfoModel registerMatrixStar(String clientKey, String code, String state, String scopes);

    /**
     * 请求UrlTicket
     *
     * @param param 请求参数
     * @return 返回参数
     */
    ShortUrlTicketModel requestUrlTicket(ShortUrlTicketParam param);

    /**
     * 请求抖音矩阵绑定UrlTicket
     *
     * @param param 参数
     * @return 返回值
     */
    ShortUrlTicketModel requestMatrixBindingTicket(DouyinMatrixBindParam param);

    /**
     * 请求抖音矩阵绑定页面的UrlTicket
     *
     * @param param 参数
     * @return 返回值
     */
    ShortUrlTicketModel requestMatrixIndexPageInfo(DouyinMatrixBindParam param);

    /**
     * 根据POI ID 查询抖音的POI信息
     *
     * @param poiId POI ID
     * @return POI信息
     */
    JSONObject queryDouyinPoiById(String poiId);

    /**
     * 根据POI关键字搜索 POI ID
     *
     * @param name       关键字
     * @param coordinate 坐标，建议精确到城市
     * @param region     地区
     * @return POI信息
     */
    JSONObject searchDouyinPoi(String name, String coordinate, String region);

    /**
     * 根据地址查询高德地理编码
     *
     * @param address 地址
     * @param city    查询城市
     * @return 地理位置列表
     */
    List<AmapGeoCodeModel> queryAmapGeoCodeModel(String address, @Nullable String city);

    /**
     * 根据经纬度查询高德地理编码
     *
     * @param location 经纬度坐标
     * @return 地理位置列表
     */
    ReGeoAddressComponentModel queryAmapReGeoCodeModel(String location);

    /**
     * 获取快手达人推授权地址
     *
     * @param param
     * @return
     */
    GetKsAuthUrlModel getKsAuthUrl(GetKsAuthUrlParam param);

    /**
     * 新增快手达人推用户信息
     *
     * @param param
     * @return
     */
    SaveKsAuthUserModel saveKsAuthUser(SaveKsAuthUserParam param);

    /**
     * 手机号码解码
     *
     * @param file 文件
     * @return 解码后的文件（Excel）
     */
    void decryptPhoneNumber(File file, Consumer<File> onFileGenerate);

    /**
     * 获得抖音的ClientToken
     *
     * @param channelCode 通道类型
     * @return ClientToken
     */
    String getMinaClientToken(String channelCode);

    /**
     * 添加短链
     *
     * @param param 短链参数
     */
    void createShortUrl(CreateShortUrlParam param);
}
