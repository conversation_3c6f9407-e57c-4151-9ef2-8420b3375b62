/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.startask.StarTaskConstant;
import com.huike.nova.common.constant.startask.agentweb.StarTaskAgentWebRedisPrefixConstant;
import com.huike.nova.common.constant.startask.web.StarTaskWebRedisPrefixConstant;
import com.huike.nova.common.enums.EmployeeAdminEnum;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.GrantPlatformEnum;
import com.huike.nova.common.enums.startask.web.OperateEmployeeTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.common.util.ExceptionUtil;
import com.huike.nova.common.util.IdWorkerUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.dao.domain.param.oem.FindRolePageDTO;
import com.huike.nova.dao.domain.param.startask.PageEmployeeListParamDTO;
import com.huike.nova.dao.domain.result.startask.PageEmployeeListResultDTO;
import com.huike.nova.dao.entity.AilikeRoleDO;
import com.huike.nova.dao.entity.AilikeRoleIdentityDO;
import com.huike.nova.dao.entity.GrantDO;
import com.huike.nova.dao.entity.RoleGrantDO;
import com.huike.nova.dao.entity.StarTaskAgentUserDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.AilikeRoleDAO;
import com.huike.nova.dao.repository.AilikeRoleIdentityDAO;
import com.huike.nova.dao.repository.GrantDAO;
import com.huike.nova.dao.repository.RoleGrantDAO;
import com.huike.nova.dao.repository.StarTaskAgentUserDAO;
import com.huike.nova.service.business.oem.common.OemCommonService;
import com.huike.nova.service.business.startask.agentweb.AgentWebRoleService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.web.StarTaskWebRoleServiceObjMapper;
import com.huike.nova.service.domain.model.oem.operation.employee.GrantModel;
import com.huike.nova.service.domain.model.oem.operation.employee.PageQueryRoleModel;
import com.huike.nova.service.domain.model.oem.operation.employee.QueryAllGrantModel;
import com.huike.nova.service.domain.model.oem.operation.employee.SelectRoleListModel;
import com.huike.nova.service.domain.model.startask.agentweb.login.StarTaskAgentWebLoginModel;
import com.huike.nova.service.domain.model.startask.agentweb.role.AgentWebEmployeeDetailModel;
import com.huike.nova.service.domain.model.startask.agentweb.role.AgentWebPageEmployeeListModel;
import com.huike.nova.service.domain.param.oem.operation.employee.AddOrUpdateRoleParam;
import com.huike.nova.service.domain.param.startask.agentweb.role.AgentWebEmployeeDetailParam;
import com.huike.nova.service.domain.param.startask.agentweb.role.AgentWebOperateEmployeeParam;
import com.huike.nova.service.domain.param.startask.agentweb.role.AgentWebUpdateEmployeeParam;
import com.huike.nova.service.domain.param.startask.web.role.StarTaskAddEmployeeParam;
import com.huike.nova.service.domain.param.startask.web.role.StarTaskPageEmployeeListParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version AgentWebRoleServiceImpl.java, v 0.1 2024-05-21 4:23 PM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class AgentWebRoleServiceImpl implements AgentWebRoleService {

    private StarTaskAgentUserDAO starTaskAgentUserDAO;

    private AilikeRoleDAO ailikeRoleDAO;

    private AilikeRoleIdentityDAO ailikeRoleIdentityDAO;

    private GrantDAO grantDAO;

    private RoleGrantDAO roleGrantDAO;

    private RedissonClient redissonClient;

    private TransactionTemplate transactionTemplate;

    private StarTaskWebRoleServiceObjMapper starTaskWebRoleServiceObjMapper;

    private OemCommonService oemCommonService;

    /**
     * 新增员工
     *
     * @param param
     */
    @Override
    public void addEmployee(StarTaskAddEmployeeParam param) {
        String roleId = param.getRoleId();
        String phone = param.getPhoneNumber();
        String phoneNumber = FieldEncryptUtil.encode(phone);
        String encodeLikeFieldExt = FieldEncryptUtil.encodeLikeFieldExt(phone);
        // 登录态获取信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        // 加锁
        String lock = StrUtil.format(StarTaskWebRedisPrefixConstant.STAR_TASK_AGENT_WEB_AGENT_OPERATE_CACHE_KEY);
        try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
            redisLockHelper.tryLock();
            // 判断手机号和区域
            StarTaskAgentUserDO checkPhone = starTaskAgentUserDAO.checkPhone(phoneNumber);
            if (Objects.nonNull(checkPhone)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("该手机号已被注册，请更换");
            }
            // 校验角色是否存在
            this.checkRoleDO(roleId);
            String agentUserId = StarTaskConstant.AGENT_USER_PREFIX + IdWorkerUtil.getSingleId();
            // 区代用户信息
            StarTaskAgentUserDO agentUserDO = new StarTaskAgentUserDO();
            agentUserDO.setAgentId(basicInfo.getAgentId());
            agentUserDO.setAgentUserId(agentUserId);
            agentUserDO.setPhoneNumber(phoneNumber);
            agentUserDO.setPhoneNumberEncryptExt(encodeLikeFieldExt);
            agentUserDO.setIsAdmin(EmployeeAdminEnum.NOT_ADMIN.getValue());
            agentUserDO.setContactName(param.getContactName());
            // 新增关联关系表
            AilikeRoleIdentityDO ailikeRoleIdentityDO = new AilikeRoleIdentityDO();
            ailikeRoleIdentityDO.setRoleId(roleId);
            ailikeRoleIdentityDO.setIdentityId(agentUserId);
            // 事物
            transactionTemplate.execute(status -> {
                starTaskAgentUserDAO.saveAgentUser(agentUserDO);
                ailikeRoleIdentityDAO.addIdentityRole(ailikeRoleIdentityDO);
                return Boolean.TRUE;
            });
        } catch (Exception e) {
            LogUtil.warn(log, "AgentWebRoleServiceImpl" + " >>>>> " + "addEmployee" + "  锁释放失败", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }

    /**
     * 修改员工
     *
     * @param param
     */
    @Override
    public void updateEmployee(AgentWebUpdateEmployeeParam param) {
        // 获取参数
        String agentUserId = param.getAgentUserId();
        String roleId = param.getRoleId();
        // 校验角色是否存在
        this.checkRoleDO(roleId);
        // 事物
        transactionTemplate.execute(status -> {
            // 修改员工信息
            starTaskAgentUserDAO.updateContactName(agentUserId, param.getContactName());
            // 修改员工角色信息
            ailikeRoleIdentityDAO.updateByIdentityId(agentUserId, roleId);
            return Boolean.TRUE;
        });
    }

    /**
     * 分页查询员工列表
     *
     * @param param
     * @return
     */
    @Override
    public PageResult<AgentWebPageEmployeeListModel> pageEmployeeList(PageParam<StarTaskPageEmployeeListParam> param) {
        StarTaskPageEmployeeListParam query = param.getQuery();
        // 登录态获取信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        // 构建入参
        PageParam<PageEmployeeListParamDTO> pageParam = new PageParam<>();
        pageParam.setPage(param.getPage());
        pageParam.setPageSize(param.getPageSize());
        PageEmployeeListParamDTO paramDTO = new PageEmployeeListParamDTO();
        paramDTO.setStatus(query.getStatus());
        paramDTO.setRoleId(query.getRoleId());
        paramDTO.setPhoneOrName(query.getPhoneOrName());
        paramDTO.setAgentId(basicInfo.getAgentId());
        paramDTO.setPhoneEncrypt(FieldEncryptUtil.encode(query.getPhoneOrName()));
        pageParam.setQuery(paramDTO);
        Page<PageEmployeeListResultDTO> resultDTOPage = starTaskAgentUserDAO.pageEmployeeList(pageParam);
        List<PageEmployeeListResultDTO> records = resultDTOPage.getRecords();
        List<AgentWebPageEmployeeListModel> list = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(records)) {
            for (PageEmployeeListResultDTO record : records) {
                AgentWebPageEmployeeListModel listModel = starTaskWebRoleServiceObjMapper.toAgentWebPageEmployeeListModel(record);
                // 解密手机号
                listModel.setPhoneNumber(FieldEncryptUtil.decode(listModel.getPhoneNumber()));
                list.add(listModel);
            }
        }
        PageResult<AgentWebPageEmployeeListModel> model = new PageResult<>();
        model.setTotal(resultDTOPage.getTotal());
        model.setCurrent(resultDTOPage.getCurrent());
        model.setSize(resultDTOPage.getSize());
        model.setRecords(list);
        return model;
    }

    /**
     * 员工详情
     *
     * @param param
     * @return
     */
    @Override
    public AgentWebEmployeeDetailModel getEmployeeDetail(AgentWebEmployeeDetailParam param) {
        PageEmployeeListResultDTO resultDTO = starTaskAgentUserDAO.getEmployeeDetail(param.getAgentUserId());
        if (Objects.isNull(resultDTO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("信息为空");
        }
        AgentWebEmployeeDetailModel model = starTaskWebRoleServiceObjMapper.toAgentWebEmployeeDetailModel(resultDTO);
        // 手机号解密
        model.setPhoneNumber(FieldEncryptUtil.decode(model.getPhoneNumber()));
        return model;
    }

    /**
     * 员工操作
     *
     * @param param
     */
    @Override
    public void operateEmployee(AgentWebOperateEmployeeParam param) {
        // 加锁
        String lock = StrUtil.format(StarTaskAgentWebRedisPrefixConstant.STAR_TASK_AGENT_WEB_CACHE_KEY_OPERATE_EMPLOYEE, param.getAgentUserId());
        try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
            redisLockHelper.tryLock();
            OperateEmployeeTypeEnum typeEnum = OperateEmployeeTypeEnum.getByValue(param.getType());
            if (Objects.isNull(typeEnum)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("类型不合法");
            }
            String agentUserId = param.getAgentUserId();
            StarTaskAgentUserDO agentUserDO = starTaskAgentUserDAO.getAgentUserByAgentUserId(agentUserId);
            if (Objects.isNull(agentUserDO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("员工不存在");
            }
            switch (typeEnum) {
                case STOPPED:
                    if (OperateEmployeeTypeEnum.STOPPED.getValue().equals(agentUserDO.getAccountStatus())) {
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("请勿重复操作");
                    }
                    starTaskAgentUserDAO.operateEmployee(agentUserId, OperateEmployeeTypeEnum.STOPPED.getValue());
                    // 停用员工
                    StpUtil.logout(agentUserId);
                    break;
                case ENABLED:
                    if (OperateEmployeeTypeEnum.ENABLED.getValue().equals(agentUserDO.getAccountStatus())) {
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("请勿重复操作");
                    }
                    starTaskAgentUserDAO.operateEmployee(agentUserId, OperateEmployeeTypeEnum.ENABLED.getValue());
                    break;
                default:
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("类型不合法");
            }
        } catch (Exception e) {
            LogUtil.warn(log, "AgentWebRoleServiceImpl" + " >>>>> " + "operateEmployee" + "  锁释放失败", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }

    /**
     * 分页查看角色列表
     *
     * @param param
     * @return
     */
    @Override
    public PageResult<PageQueryRoleModel> pageQueryRole(PageParam param) {
        // 登录态获取信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        // 构建入参
        PageParam<FindRolePageDTO> rolePageDTOPageParam = new PageParam<>();
        rolePageDTOPageParam.setPage(param.getPage());
        rolePageDTOPageParam.setPageSize(param.getPageSize());
        FindRolePageDTO findRolePageDTO = new FindRolePageDTO();
        findRolePageDTO.setPartnerId(basicInfo.getAgentId());
        findRolePageDTO.setBelong(GrantPlatformEnum.STAR_TASK_AGENT_BACKSTAGE.getValue());
        rolePageDTOPageParam.setQuery(findRolePageDTO);
        Page<AilikeRoleDO> page = ailikeRoleDAO.pageQueryRole(rolePageDTOPageParam);
        return starTaskWebRoleServiceObjMapper.toPageQueryRoleModelPage(page);
    }

    /**
     * 角色权限列表
     *
     * @return
     */
    @Override
    public QueryAllGrantModel queryAllGrant() {
        // 登录态获取信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        // 获取权限列表
        List<GrantDO> grantList;
        if (EmployeeAdminEnum.IS_ADMIN.getValue().equals(basicInfo.getIsAdmin())) {
            grantList = grantDAO.queryAllByPlatform(GrantPlatformEnum.STAR_TASK_AGENT_BACKSTAGE.getValue());
        } else {
            grantList = grantDAO.queryGrantByIdentityId(basicInfo.getAgentUserId());
        }
        List<GrantModel> grantResult = starTaskWebRoleServiceObjMapper.toGrantModelList(grantList);
        List<GrantModel> grantResultTree = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(grantList)) {
            grantResult.forEach(t -> t.setChecked(CommonConstant.INTEGER_TWO));
            // 生成树状结构
            oemCommonService.processGrantTree(grantResultTree, grantResult);
        }
        QueryAllGrantModel model = new QueryAllGrantModel();
        model.setGrantList(grantResultTree);
        return model;
    }

    /**
     * 新增或修改角色
     *
     * @param param
     */
    @Override
    public void addOrUpdateRole(AddOrUpdateRoleParam param) {
        String roleId = param.getRoleId();
        // 登录态获取登录信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        // 判断名称是否存在
        AilikeRoleDO queryRoleInfo = new AilikeRoleDO();
        queryRoleInfo.setBelong(GrantPlatformEnum.STAR_TASK_AGENT_BACKSTAGE.getValue());
        queryRoleInfo.setRoleName(param.getRoleName());
        queryRoleInfo.setPartnerId(basicInfo.getAgentId());
        AilikeRoleDO roleInfo = ailikeRoleDAO.queryRoleInfo(queryRoleInfo);
        // 判断是新增还是修改
        if (StringUtils.isBlank(roleId)) {
            if (Objects.nonNull(roleInfo)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("角色名称已存在，修改失败");
            }
            // 保存角色信息
            AilikeRoleDO roleDO = new AilikeRoleDO();
            // 修改
            String key = IdWorkerUtil.getSingleId();
            roleDO.setRoleId(key);
            roleDO.setBelong(GrantPlatformEnum.STAR_TASK_AGENT_BACKSTAGE.getValue());
            roleDO.setPartnerId(basicInfo.getAgentId());
            roleDO.setRoleName(param.getRoleName());
            roleDO.setRemark(param.getRemark());
            // 保存角色权限
            List<RoleGrantDO> roleGrantList = this.getRoleGrantDOS(param, key);
            // 事物
            transactionTemplate.execute(status -> {
                ailikeRoleDAO.addMerchantRole(roleDO);
                roleGrantDAO.batchSaveRoleGrant(roleGrantList);
                return Boolean.TRUE;
            });
        } else {
            if (Objects.nonNull(roleInfo) && !roleInfo.getRoleId().equals(roleId)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("角色名称已存在，修改失败");
            }
            this.checkRoleDO(roleId);
            // 保存角色权限
            List<RoleGrantDO> roleGrantList = this.getRoleGrantDOS(param, roleId);
            // 事物
            transactionTemplate.execute(status -> {
                // 更新角色信息
                ailikeRoleDAO.updateRole(roleId, param.getRoleName(), param.getRemark());
                // 删除角色权限
                roleGrantDAO.deleteRoleGrant(param.getRoleId());
                // 保存角色权限
                roleGrantDAO.batchSaveRoleGrant(roleGrantList);
                return Boolean.TRUE;
            });
        }
    }

    /**
     * 下拉获取角色列表
     *
     * @return
     */
    @Override
    public SelectRoleListModel selectRoleList() {
        // 登录态获取登录信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        List<AilikeRoleDO> list = ailikeRoleDAO.queryListSelectRole(GrantPlatformEnum.STAR_TASK_AGENT_BACKSTAGE.getValue(), basicInfo.getAgentId());
        SelectRoleListModel model = new SelectRoleListModel();
        model.setRoleList(starTaskWebRoleServiceObjMapper.toGetRoleDetailModelList(list));
        return model;
    }

    /**
     * 保存角色权限
     *
     * @param param
     * @param roleId
     * @return
     */
    @NotNull
    private List<RoleGrantDO> getRoleGrantDOS(AddOrUpdateRoleParam param, String roleId) {
        List<RoleGrantDO> roleGrantList = Lists.newArrayList();
        param.getGrantIdList().forEach(item -> {
            RoleGrantDO roleGrantDO = new RoleGrantDO();
            roleGrantDO.setRoleId(roleId);
            roleGrantDO.setGrantId(item);
            roleGrantList.add(roleGrantDO);
        });
        return roleGrantList;
    }

    /**
     * 校验角色是否存在
     *
     * @param roleId
     * @return
     */
    private AilikeRoleDO checkRoleDO(String roleId) {
        AilikeRoleDO roleDO = ailikeRoleDAO.queryByRoleId(roleId);
        if (Objects.isNull(roleDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("角色已被删除");
        }
        return roleDO;
    }
}