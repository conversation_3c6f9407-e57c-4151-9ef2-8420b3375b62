package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.Imagemode.FindActivityImageListModel;
import com.huike.nova.service.domain.model.Imagemode.GetImageModeConfigModel;
import com.huike.nova.service.domain.model.Imagemode.ImagePublishRecordModel;
import com.huike.nova.service.domain.param.Imagemode.AddActivityImageParam;
import com.huike.nova.service.domain.param.Imagemode.DeleteActivityImageParam;
import com.huike.nova.service.domain.param.Imagemode.FindActivityImageListParam;
import com.huike.nova.service.domain.param.Imagemode.GetImageModeConfigParam;
import com.huike.nova.service.domain.param.Imagemode.ImagePublishRecordParam;
import com.huike.nova.service.domain.param.Imagemode.UpdateImageModeConfigParam;

/**
 * <AUTHOR>
 * @date 2023年06月28日 11:08
 */
public interface ImageModeService {


    /**
     * 查询活动图片列表
     *
     * @param pageParam 入参
     * @return 出参
     */
    PageResult<FindActivityImageListModel> findActivityImageList(PageParam<FindActivityImageListParam> pageParam);

    /**
     * 查询活动图片设置
     *
     * @param param 入参
     * @return 出参
     */
    GetImageModeConfigModel getImageModeConfig(GetImageModeConfigParam param);

    /**
     * 更新活动图片设置
     *
     * @param param 入参
     */
    void updateImageModeConfig(UpdateImageModeConfigParam param);

    /**
     * 添加活动图片
     *
     * @param param 入参
     */
    void addActivityImage(AddActivityImageParam param);

    /**
     * 删除活动图片
     *
     * @param param 入参
     */
    void deleteActivityImage(DeleteActivityImageParam param);

    /**
     * 查询活动图片发布记录-已合成
     *
     * @param pageParam 入参
     * @return 出参
     */
    PageResult<ImagePublishRecordModel> imagePublishRecordPageList(PageParam<ImagePublishRecordParam> pageParam);
}
