/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.alibaba.fastjson.JSONObject;
import com.huike.nova.service.domain.model.callback.amap.ExtTicketQueryModel;
import com.huike.nova.service.domain.model.callback.amap.ExtTicketSendDetailModel;
import com.huike.nova.service.domain.model.callback.takeout.TakeoutApplyRefundModel;
import com.huike.nova.service.domain.model.tiktokopen.CloseOrderQueryModel;
import com.huike.nova.service.domain.param.callback.CreateVideoParam;
import com.huike.nova.service.domain.param.callback.amap.ExtTicketQueryParam;
import com.huike.nova.service.domain.param.callback.amap.ExtTicketSendParam;
import com.huike.nova.service.domain.param.callback.amap.TicketExpireCallBackParam;
import com.huike.nova.service.domain.param.callback.takeaway.TakeoutApplyRefundParam;
import com.huike.nova.service.domain.param.tiktokopen.DouyinHermesAkteAfterSaleAuditParam;
import com.huike.nova.service.domain.param.tiktokclose.RepairCloseLoopOrderParam;
import com.huike.nova.service.domain.result.IssueDouyinClosedLoopOrderResult;

import javax.annotation.Nonnull;
import java.util.List;

/**
 * <AUTHOR>
 * @version CallbackService.java, v 0.1 2022-09-08 1:51 PM ruanzy
 */
public interface CallbackService {

    /**
     * 视频分享回调处理
     *
     * @param param
     */
    void createVideo(CreateVideoParam param);

    /**
     * 高德同步发码通知
     *
     * @param param
     * @return
     */
    List<ExtTicketSendDetailModel> sendTicketCallBack(ExtTicketSendParam param);

    /**
     * 高德退款凭证作废
     *
     * @param param
     * @return
     */
    boolean amapTicketCallBack(TicketExpireCallBackParam param);

    /**
     * 高德凭证信息查询回调
     *
     * @param param
     * @return
     */
    ExtTicketQueryModel amapTicketQueryCallBack(ExtTicketQueryParam param);

    /**
     * 用户下单消息
     *
     * @param content
     * @param msgId
     * @param interfaceType
     * @param webhook
     */
    void takeoutOrderPay(String content, String msgId, Integer interfaceType, String webhook);

    /**
     * 平台配送-配送状态变更消息
     *
     * @param content
     * @param msgId
     * @param interfaceType
     * @param webhook
     */
    void changeTakeoutDelivery(String content, String msgId, Integer interfaceType, String webhook);

    /**
     * 订单已接单消息
     *
     * @param content
     * @param msgId
     * @param interfaceType
     * @param webhook
     */
    void merchantReceiveTakeoutOrder(String content, String msgId, Integer interfaceType, String webhook);

    /**
     * 订单已拒单消息
     *
     * @param content
     * @param msgId
     * @param interfaceType
     * @param webhook
     */
    void merchantRefuseTakeoutOrder(String content, String msgId, Integer interfaceType, String webhook);

    /**
     * 订单已取消消息
     *
     * @param content
     * @param msgId
     * @param interfaceType
     * @param webhook
     */
    void cancelTradeRefund(String content, String msgId, Integer interfaceType, String webhook);

    /**
     * 订单已同意/已拒绝退款消息
     *
     * @param content
     * @param msgId
     * @param interfaceType
     * @param webhook
     */
    void auditTradeRefund(String content, String msgId, Integer interfaceType, String webhook);

    /**
     * 闭环订单下单回调
     *
     * @param content
     * @param msgId
     * @param interfaceType
     * @param webhook
     */
    void tradeOrderNotify(String content, String msgId, Integer interfaceType, String webhook);

    /**
     *  闭环券核销回调
     *
     * @param content
     * @param msgId
     */
    void lifeTradeCertificateNotify(String content, String msgId);

    /**
     * 生活合作接口回调
     *
     * @param content 上下文
     * @param clientKey 应用CK
     * @param msgId 消息Id
     */
    void lifeSaasCooperateAuthWithBindNotify(String content, String clientKey, String msgId);

    /**
     * 用户申请退款消息
     *
     * @param param
     * @return
     */
    TakeoutApplyRefundModel applyRefundByUser(TakeoutApplyRefundParam param);

    /**
     * 抖音外卖异常消息钉钉发送
     *
     * @param msgId
     * @param message
     */
    void sendTakeoutMessage(String msgId, String message);

    /**
     * 闭环订单补单（到综）
     *
     * @param outOrderId 平台订单号
     * @param accountId 抖音来客Id
     */
    IssueDouyinClosedLoopOrderResult restoreCloseLoopOrder(String outOrderId, String accountId);

    /**
     * 闭环RPA订单补单
     *
     * @param appId 集团AppId
     * @param outOrderId 订单号
     */
    IssueDouyinClosedLoopOrderResult restoreRpaCloseLoopOrder(String appId, String outOrderId, boolean checkGoodsDetail);

    /**
     * 插入或者更新闭环订单（到综）
     *
     * @param accountId 来客Id
     * @param orderDTO 订单对象
     * @return 结果
     */
    IssueDouyinClosedLoopOrderResult synchronizeCloseLoopOrder(String accountId, @Nonnull CloseOrderQueryModel.OrdersDTO orderDTO);

    /**
     * 闭环订单补单（餐饮：支持一单多品）<br/>
     * 该接口和issueCloseLoopOrder接口效果一样，但是性能比较差，对于目前的业务来说请使用通用接口
     *
     * @param repairParam 补单参数
     * @return 补单结果
     */
    JSONObject manualIssueCloseLoopOrderV2(RepairCloseLoopOrderParam repairParam);

    /**
     * 处理抖音来客核销后退款申请
     * @param param 退款申请参数
     */
    void handleDouyinLifeHermesAkteAfterSaleAudit(DouyinHermesAkteAfterSaleAuditParam param);
}