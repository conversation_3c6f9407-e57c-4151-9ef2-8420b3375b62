package com.huike.nova.service.business;

import cn.hutool.extra.spring.SpringUtil;
import com.huike.nova.service.listener.StarPushAutoPublishListener;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 调试服务
 *
 * <AUTHOR> (<EMAIL>)
 * @version DebugService.java, v1.0 2025-05-23 15:03 John Exp$
 */
@Service
@Slf4j
@AllArgsConstructor
public class DebugService {
    /**
     * 模拟MQ消费者
     *
     * @param key MQKey
     * @param message 消息内容
     */
    public void mockMqConsumer(String key, String message) {
        SpringUtil.getBean(StarPushAutoPublishListener.class).mockConsume(message);
    }
}
