/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb.impl;

import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.SystemConstants;
import com.huike.nova.common.constant.startask.agentweb.StarTaskAgentWebRedisPrefixConstant;
import com.huike.nova.common.enums.EmployeeAdminEnum;
import com.huike.nova.common.enums.EnvEnum;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.GrantPlatformEnum;
import com.huike.nova.common.enums.SmsTypeEnum;
import com.huike.nova.common.enums.oem.AccountStatusEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.XorCipherUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.entity.GrantDO;
import com.huike.nova.dao.entity.StarTaskAgentDO;
import com.huike.nova.dao.entity.StarTaskAgentUserDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.GrantDAO;
import com.huike.nova.dao.repository.StarTaskAgentDAO;
import com.huike.nova.dao.repository.StarTaskAgentUserDAO;
import com.huike.nova.service.business.SmsService;
import com.huike.nova.service.business.startask.agentweb.AgentWebLoginService;
import com.huike.nova.service.domain.model.startask.agentweb.login.StarTaskAgentWebLoginModel;
import com.huike.nova.service.domain.param.sms.SmsMessageCodeParam;
import com.huike.nova.service.domain.param.sms.SmsMessageParam;
import com.huike.nova.service.domain.param.startask.agentweb.login.AgentWebOneClickLoginParam;
import com.huike.nova.service.domain.param.startask.agentweb.login.StarTaskAgentSendCaptchaParam;
import com.huike.nova.service.domain.param.startask.agentweb.login.StarTaskAgentWebLoginParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version AgentWebLoginServiceImpl.java, v 0.1 2024-05-20 10:42 AM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class AgentWebLoginServiceImpl implements AgentWebLoginService {

    private StarTaskAgentUserDAO starTaskAgentUserDAO;

    private StarTaskAgentDAO starTaskAgentDAO;

    private GrantDAO grantDAO;

    private RedissonClient redissonClient;

    private SmsService smsService;

    private SysConfig sysConfig;

    /**
     * 一键登录
     *
     * @param param
     * @return
     */
    @Override
    public StarTaskAgentWebLoginModel oneClickLogin(AgentWebOneClickLoginParam param) {
        // 解密
        String agentUserId = XorCipherUtil.xorDecode(param.getAgentUserId(), CommonConstant.XOR_IV);
        // 根据agentUserId查询
        StarTaskAgentUserDO agentUserDO = starTaskAgentUserDAO.getAgentUserByAgentUserId(agentUserId);
        if (Objects.isNull(agentUserDO)) {
            throw new CommonException(ErrorCodeEnum.LOGIN_ERROR).detailMessage("登录失效");
        }
        // 只有管理员账号可以一键登录
        if (!EmployeeAdminEnum.IS_ADMIN.getValue().equals(agentUserDO.getIsAdmin())) {
            throw new CommonException(ErrorCodeEnum.LOGIN_ERROR).detailMessage("登录失效");
        }
        // 判断账号的使用状态
        StarTaskAgentDO agentInfo = starTaskAgentDAO.getAgentInfo(agentUserDO.getAgentId());
        if (Objects.isNull(agentInfo) || AccountStatusEnum.DISABLED.getValue().equals(agentInfo.getUsedStatus())) {
            throw new CommonException(ErrorCodeEnum.LOGIN_ERROR).detailMessage("登录失效");
        }
        return buildLoginModel(agentUserDO);
    }

    /**
     * 登录
     *
     * @param request
     * @return
     */
    @Override
    public StarTaskAgentWebLoginModel login(StarTaskAgentWebLoginParam request) {
        String account = request.getAccount();
        String code = request.getCode();
        // 校验账号
        StarTaskAgentUserDO agentUser = starTaskAgentUserDAO.getAgentUser(account, AccountStatusEnum.ENABLED.getValue());
        if (Objects.isNull(agentUser)) {
            throw new CommonException(ErrorCodeEnum.LOGIN_ERROR);
        }
        // 判断账号的使用状态
        StarTaskAgentDO agentInfo = starTaskAgentDAO.getAgentInfo(agentUser.getAgentId());
        if (Objects.isNull(agentInfo) || AccountStatusEnum.DISABLED.getValue().equals(agentInfo.getUsedStatus())) {
            throw new CommonException(ErrorCodeEnum.LOGIN_ERROR);
        }
        // 测试环境直接登录
        if (EnvEnum.TEST.getValue().equalsIgnoreCase(sysConfig.getEnv()) && code.equals("9999")) {
            return this.buildLoginModel(agentUser);
        }
        // 校验验证码是否正确
        final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(StarTaskAgentWebRedisPrefixConstant.STAR_TASK_AGENT_WEB_CACHE_KEY_SMS_VERIFY,
                SmsTypeEnum.STAR_TASK_WEB_LOGIN.getValue(), account), StringCodec.INSTANCE);
        String smdCode = bucket.get();
        if (StringUtils.isBlank(smdCode)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("验证码已失效，请重新发送");
        }
        if (Objects.isNull(code) || !smdCode.equals(code)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("验证码错误，请重新输入");
        }
        return this.buildLoginModel(agentUser);
    }

    @NotNull
    private StarTaskAgentWebLoginModel buildLoginModel(StarTaskAgentUserDO agentUser) {
        String agentUserId = agentUser.getAgentUserId();
        String agentId = agentUser.getAgentId();
        // 下线
        StpUtil.logout();
        // sa-token登录
        SaLoginModel loginModel = new SaLoginModel();
        loginModel.setTimeout(60 * 60 * 24 * 7);
        loginModel.setDevice("star.task.agentweb");
        StpUtil.login(agentUserId, loginModel);
        // 获取accessToken
        final String accessToken = StpUtil.getTokenInfo().getTokenValue();
        StarTaskAgentWebLoginModel model = new StarTaskAgentWebLoginModel();
        // 获取角色权限
        List<GrantDO> grantList;
        // 判断是否是管理员
        if (EmployeeAdminEnum.IS_ADMIN.getValue().equals(agentUser.getIsAdmin())) {
            // 主账号获取所有权限
            grantList = grantDAO.queryAllByPlatform(GrantPlatformEnum.STAR_TASK_AGENT_BACKSTAGE.getValue());
        } else {
            // 查询账号下面角色权限
            grantList = grantDAO.queryGrantByIdentityId(agentUserId);
        }
        model.setGrantList(grantList.stream().map(GrantDO::getGrantCode).collect(Collectors.toList()));
        model.setAccessToken(accessToken);
        model.setPhoneNumber(FieldEncryptUtil.decode(agentUser.getPhoneNumber()));
        model.setAgentUserId(agentUserId);
        model.setAgentId(agentId);
        model.setIsAdmin(agentUser.getIsAdmin());
        model.setAppletId(agentUser.getAppletId());
        // sa-token存储登录信息
        StpUtil.getTokenSessionByToken(accessToken).set(accessToken, JSON.toJSONString(model));
        return model;
    }

    /**
     * 退出登录
     */
    @Override
    public void logout() {
        // 下线
        StpUtil.logout();
    }

    /**
     * 发送验证码
     *
     * @param param
     */
    @Override
    public void sendCaptcha(StarTaskAgentSendCaptchaParam param) {
        String phoneNumber = param.getPhoneNumber();
        // 获取6位随机数
        final String smdCode = RandomUtil.randomNumbers(6);
        SmsTypeEnum smsTypeEnum = SmsTypeEnum.STAR_TASK_WEB_LOGIN;
        Integer type = smsTypeEnum.getValue();
        // 发送间隔要超过一分钟
        final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(StarTaskAgentWebRedisPrefixConstant.STAR_TASK_AGENT_WEB_CACHE_KEY_SMS_REQUEST, type, phoneNumber), StringCodec.INSTANCE);
        if (bucket.isExists()) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("手机号码验证码发送频繁，请稍后再试");
        }
        // 30分钟内，只能发送3次
        final RAtomicLong cacheCount = redissonClient.getAtomicLong(
                StrUtil.format(StarTaskAgentWebRedisPrefixConstant.STAR_TASK_AGENT_WEB_CACHE_KEY_SMS_REQUEST_COUNT, type, phoneNumber));
        if (!SmsTypeEnum.STAR_TASK_WEB_MERCHANT_RECHARGE.equals(smsTypeEnum)) {
            if (cacheCount.get() >= SystemConstants.SEND_SMS_MAX_COUNT) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("手机号码验证码发送过于频繁，请30分钟后再试");
            }
        }
        // 发送验证码
        final SmsMessageParam smsMessageParam = new SmsMessageParam();
        smsMessageParam.setParam(new SmsMessageCodeParam(smdCode));
        smsMessageParam.setSign(smsTypeEnum.getSignEnum());
        smsMessageParam.setSmsTemplateCodeEnum(smsTypeEnum.getTemplateCodeEnum());
        smsMessageParam.setPhoneNumber(phoneNumber);
        smsService.sendMessage(smsMessageParam);
        // 增加一次发送次数
        cacheCount.getAndIncrement();
        // 第一次加过期时间
        if (cacheCount.get() == 1) {
            cacheCount.expire(30, TimeUnit.MINUTES);
        }
        // 设置一分钟缓存
        bucket.set(phoneNumber, 1, TimeUnit.MINUTES);
        // 发送验证码
        final RBucket<String> cacheVerify = redissonClient.getBucket(StrUtil.format(StarTaskAgentWebRedisPrefixConstant.STAR_TASK_AGENT_WEB_CACHE_KEY_SMS_VERIFY,
                smsTypeEnum.getValue(), phoneNumber), StringCodec.INSTANCE);
        // 设置5分钟验证码缓存
        cacheVerify.set(smdCode, 5, TimeUnit.MINUTES);
    }
}