/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.common;

import com.huike.nova.service.domain.param.startask.web.task.TaskApplyStatusOperationParam;

/**
 * <AUTHOR>
 * @version StarTaskJobService.java, v 0.1 2023-12-10 11:49 AM ruanzy
 */
public interface StarTaskJobService {

    /**
     * 任务结束时间状态修改
     */
    void updateStarTaskByEndTime();

    /**
     * 结算完成任务状态修改
     *
     * @param taskId
     */
    void finishStarTask(String taskId);

    /**
     * 更新平台审核状态
     */
    void updatePlatformAudit();

    /**
     * 初始化身份关联
     */
    void initIdentityRelationJobHandler();

    /**
     * 解绑重复达人
     */
    void unbindRepeatStar();

    /**
     * 余额数据清洗
     */
    void cleanBalanceJobHandler();

    /**
     * 统计任务报名完成量设置任务状态完成
     *
     * @param taskId
     */
    void statisticsTaskIsFinish(String taskId);

    /**
     * 区代日统计
     *
     * @param param
     */
    void agentDayStatistics(String param);

    /**
     * 报名清单状态操作
     *
     * @param param
     * @param operatorId
     * @param contactName
     */
    void applyStatusOperation(TaskApplyStatusOperationParam param, String operatorId, String contactName);
}