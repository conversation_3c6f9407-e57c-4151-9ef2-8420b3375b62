/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb;

import com.huike.nova.service.domain.model.startask.AgentAreaModel;
import com.huike.nova.service.domain.model.startask.agentweb.home.GetBasicInfoModel;
import com.huike.nova.service.domain.model.startask.agentweb.home.GetManageDataModel;
import com.huike.nova.service.domain.model.startask.agentweb.home.GetProportionConfigModel;
import com.huike.nova.service.domain.model.web.homepage.GetConfigurationModel;
import com.huike.nova.service.domain.param.startask.agentweb.home.GetManageDataParam;
import com.huike.nova.service.domain.param.startask.agentweb.home.UpdateProportionConfigParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version AgentHomePageService.java, v 0.1 2024-05-20 4:38 PM ruanzy
 */
public interface AgentHomePageService {

    /**
     * 基本信息获取
     *
     * @return
     */
    GetBasicInfoModel getBasicInfo();

    /**
     * 首页-更新日志和帮助中心
     *
     * @return
     */
    GetConfigurationModel getConfiguration();

    /**
     * 查询分成比例配置
     *
     * @return
     */
    GetProportionConfigModel getProportionConfig();

    /**
     * 更新分成比例配置
     *
     * @param param
     */
    void updateProportionConfig(UpdateProportionConfigParam param);

    /**
     * 经营数据
     *
     * @param param
     * @return
     */
    GetManageDataModel getManageData(GetManageDataParam param);

    /**
     * 根据类型查询的所有的城市列表
     *
     * @param type
     * @return
     */
    List<AgentAreaModel> findHistoryCityList(Integer type);
}