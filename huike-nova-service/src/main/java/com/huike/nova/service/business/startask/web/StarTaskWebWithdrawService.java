package com.huike.nova.service.business.startask.web;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.dao.domain.param.startask.WithdrawPageListParamDTO;
import com.huike.nova.service.domain.model.startask.web.merchant.WithdrawPageListModel;
import com.huike.nova.service.domain.param.startask.web.merchant.WithdrawApplyStatusOperationParam;
import com.huike.nova.service.domain.param.startask.web.merchant.WithdrawPageListParam;
import io.reactivex.Observable;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2023年12月11日 09:54
 */
public interface StarTaskWebWithdrawService {

    /**
     * 提现申请分页列表
     *
     * @param param 入参
     * @return 出参
     */
    PageResult<WithdrawPageListModel> pageList(PageParam<WithdrawPageListParam> param);

    /**
     * 提现申请分页列表导出
     *
     * @param param 入参
     */
    void requestExport(WithdrawPageListParam param);

    /**
     * 提现申请状态操作
     *
     * @param param 入参
     */
    void applyStatusOperation(WithdrawApplyStatusOperationParam param);

    /**
     * 提现申请记录导出
     *
     * @param fileName 文件名
     * @param param    导出参数
     * @return RxJava导出成功文件
     */
    Observable<File> export(String fileName, WithdrawPageListParamDTO param);

    /**
     * 提现申请状态操作(区代佣金)
     *
     * @param param
     */
    void operationApplyStatusToAgent(WithdrawApplyStatusOperationParam param);
}
