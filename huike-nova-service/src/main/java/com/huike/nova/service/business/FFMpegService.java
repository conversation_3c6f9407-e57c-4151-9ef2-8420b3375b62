package com.huike.nova.service.business;

import com.huike.nova.sdk.model.MediaInfoModel;

/**
 * FFMpeg服务，用于处理音视频信息
 *
 * <AUTHOR> (<EMAIL>)
 * @version FFMpegService.java, v1.0 12/02/2023 14:39 John Exp$
 */
public interface FFMpegService {

    /**
     * 获得媒体文件信息，包括视频时长、编码方式、音频编码信息、视频编码信息
     *
     * @param filePathOrUrl 文件名或者URL
     * @return 媒体文件信息
     */
    MediaInfoModel getMediaInfo(String filePathOrUrl);
}
