/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version CountDayTakeoutOrderJobHandler.java, v 0.1 2023-02-03 1:39 PM ruanzy
 */
@Component
@Slf4j
@JobHandler("countDayTakeoutOrderJobHandler")
@AllArgsConstructor
public class CountDayTakeoutOrderJobHandler extends IJobHandler {

    private TaskService taskService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("CountDayTakeoutOrderJobHandler.execute >> 日统计外卖订单脚本执行开始：time = {}", DateUtil.now());
        taskService.countDayTakeoutOrder();
        XxlJobLogger.log("CountDayTakeoutOrderJobHandler.execute >> 日统计外卖订单脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}