/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.material.*;
import com.huike.nova.service.domain.model.tiktokopen.TiktokSentenceListModel;
import com.huike.nova.service.domain.param.material.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version ActivitySoundService.java, v 0.1 2022-09-02 15:33 zhangling
 */
public interface ActivitySoundService {
    /**
     * 查询活动口播列表
     *
     * @param pageParam
     * @return
     */
    PageResult<SoundListModel> findActivitySoundPage(PageParam<FindSoundPageParam> pageParam);

    /**
     * 新增活动口播
     *
     * @param param 口播内容
     */
    void saveActivitySound(SaveActivitySoundParam param);

    /**
     * 批量新增活动口播
     *
     * @param param
     * @return
     */
    boolean saveBatchSound(SoundBatchSaveParam param);

    /**
     * 删除活动口播
     *
     * @param materialSoundId 口播id
     * @param activityId      剪辑任务id
     */
    void deleteActivitySound(String activityId, String materialSoundId);

    /**
     * 更新活动口播内容
     *
     * @param param 口播内容
     */
    void updateActivitySound(SoundUpdateParam param);

    /**
     * 获取抖音热词
     *
     * @return
     */
    TiktokSentenceListModel findSentenceList();

    /**
     * 获取预设素材
     *
     * @param param
     * @return
     */
    PageResult<MaterialListModel> findPresetMaterialPage(PageParam<FindPresetMaterialParam> param);

    /**
     * 批量保存预设素材
     *
     * @param param
     * @return
     */
    boolean batchSavePresetMaterial(SavePresetMaterialParam param);

    /**
     * 获取预设口播人声
     *
     * @return
     */
    List<IceVoiceGetModel> findIceVoice();

    /**
     * 添加人声
     *
     * @param param
     * @return
     */
    boolean addIceVoice(IceVoiceAddParam param);

    /**
     * 查询已选择的口播人声
     *
     * @param param
     * @return
     */
    List<SelectedIceVoiceDetailModel> findSelectedIceVoiceList(SelectedIceVoicesParam param);

    /**
     * 查询口播时长
     *
     * @param param
     * @return
     */
    SoundDurationQueryModel soundDurationQuery(SoundDurationQueryParam param);

    /**
     * 批量更新口播url
     *
     * @param param 入参
     */
    void batchUpdateSoundUrl(SoundBatchSaveParam param);


    /**
     * 查询自定义口播详情
     *
     * @param param 入参
     * @return 自定义口播详情
     */
    CustomSoundDetailModel customSoundDetail(CustomSoundDetailParam param);

    /**
     * 修改为空的口播
     *
     * @param param 入参
     */
    List<String> updateNullSound(String param);
}