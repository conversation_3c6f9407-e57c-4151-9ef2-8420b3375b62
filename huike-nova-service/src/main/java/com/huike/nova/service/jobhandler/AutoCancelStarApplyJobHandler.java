/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.huike.nova.service.business.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version AutoCancelStarApplyJobHandler.java, v 0.1 2024-03-28 10:46
 */
@Component
@Slf4j
@JobHandler("AutoCancelStarApplyJobHandler")
@AllArgsConstructor
public class AutoCancelStarApplyJobHandler extends IJobHandler {

    private TaskService taskService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        //{"taskIdList":"","lastDayNum":-190}
        XxlJobLogger.log(" AutoCancelStarApplyJobHandler.execute >> 自动结束达人报名任务脚本执行开始：time = {}", DateUtil.now());
        List<String> taskIdList = CollectionUtil.newArrayList();
        Integer lastDayNum = -30;
        if (StringUtils.isNotBlank(s)) {
            JSONObject jsonObject = JSON.parseObject(s);
            String taskIdListStr = jsonObject.getString("taskIdList");
            if (StringUtils.isNotBlank(taskIdListStr)) {
                taskIdList = Arrays.asList(taskIdListStr.split(","));
            }
            lastDayNum = jsonObject.getInteger("lastDayNum");
        }
        taskService.autoCancelStarApply(taskIdList, lastDayNum);
        XxlJobLogger.log(" AutoCancelStarApplyJobHandler.execute >> 自动结束达人报名任务脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}