package com.huike.nova.service.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.PayStatusEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.service.business.AppreciationOpeningRecordService;
import com.huike.nova.service.domain.model.pay.GetAppreciationOpeningInfoModel;
import com.huike.nova.service.domain.param.pay.GetAppreciationOpeningInfoParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * 增值服务支付主查消息
 *
 * <AUTHOR>
 * @version AppreciationPayOrderQueryListener.java, v 0.1 2023/2/10 4:31 下午 mayucong
 */
@Slf4j(topic = "mq")
@Component
@AllArgsConstructor
public class AppreciationPayOrderQueryListener implements MessageListener {

    private RedissonClient redissonClient;
    private AppreciationOpeningRecordService appreciationOpeningRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Action consume(Message message, ConsumeContext context) {
        String openingRecordSn = new String(message.getBody(), StandardCharsets.UTF_8);
        LogUtil.info(log, "consume >> AppreciationPayOrderQueryListener >> 增值服务支付主查消息处理开始 >> message = {}", message);
        // 加锁
        String lockKey = StrUtil.format(RedisPrefixConstant.ORDER_PAY_CALLBACK_LOCK_ORDER_SN, openingRecordSn);
        RLock redisLock = redissonClient.getLock(lockKey);

        try {
            if (!redisLock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.SECONDS)) {
                throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage("已在其他地方处理");
            }

            // 查询支付状态
            GetAppreciationOpeningInfoParam param = new GetAppreciationOpeningInfoParam();
            param.setOpeningRecordSn(openingRecordSn);
            GetAppreciationOpeningInfoModel appreciationOpeningInfo = appreciationOpeningRecordService.getAppreciationOpeningInfo(param);
            if (ObjectUtil.isNull(appreciationOpeningInfo) || PayStatusEnum.WAITING.getValue().equals(appreciationOpeningInfo.getPayStatus())) {
                return Action.ReconsumeLater;
            }
        } catch (Exception e) {
            LogUtil.error(log, "AppreciationPayOrderQueryListener > 增值服务支付主查消息发生异常, openingRecordSn={}", openingRecordSn, e);
            throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage("增值服务支付主查消息异常");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (redisLock != null && redisLock.isLocked() && redisLock.isHeldByCurrentThread()) {
                    redisLock.unlock();
                }
            } catch (Exception e) {
                LogUtil.error(log, "AppreciationPayOrderQueryListener >>  锁释放异常");
            }
        }
        LogUtil.info(log, "consume >> AppreciationPayOrderQueryListener >> 增值服务支付主查消息处理结束 >> message = {}", openingRecordSn);
        return Action.CommitMessage;
    }

}
