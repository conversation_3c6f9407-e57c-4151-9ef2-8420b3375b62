/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.employeepush.*;
import com.huike.nova.service.domain.model.supactivity.VideoDataListModel;
import com.huike.nova.service.domain.param.employeepush.EmployeeVideoDataCountParam;
import com.huike.nova.service.domain.param.employeepush.FindEmployeeProduceParam;
import com.huike.nova.service.domain.param.employeepush.FindEmployeeVideoDataParam;
import com.huike.nova.service.domain.param.employeepush.MaterialContributionParam;

/**
 * <AUTHOR>
 * @version EmployeePushService.java, v 0.1 2022-09-30 13:37 zhangling
 */
public interface EmployeePushService {

    /**
     * 查询员工发布视频数据
     * @param param
     * @return
     */
    PageResult<VideoDataListModel> pageQueryEmployeeVideoData(PageParam<FindEmployeeVideoDataParam> param);

    /**
     * 获取员工素材贡献记录
     * @param param
     * @return
     */
    PageResult<ContributionMaterialListModel> pageQueryMaterialContribution(PageParam<MaterialContributionParam> param);

    /**
     * 统计员工素材贡献数
     * @param param
     * @return
     */
    ContributionMaterialCountModel countContributionMaterial(MaterialContributionParam param);

    /**
     * 统计员工邀请数据
     * @param param
     * @return
     */
    EmployeeProduceCountModel countEmployeeProduce(FindEmployeeProduceParam param);

    /**
     * 获取员工邀请记录
     * @param param
     * @return
     */
    PageResult<EmployeeInviteRecordListModel> pageQueryInviteRecord(PageParam<FindEmployeeProduceParam> param);

    /**
     * 统计员工发布视频数据
     * @param param
     * @return
     */
    EmployeeVideoDataCountModel countEmployeeVideoData(EmployeeVideoDataCountParam param);

}