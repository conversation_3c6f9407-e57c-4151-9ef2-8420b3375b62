package com.huike.nova.service.listener.gpt;

import com.alibaba.fastjson.JSONObject;
import com.annimon.stream.function.Consumer;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.sdk.baidu.model.BceWenXinAigcResponse;
import com.huike.nova.service.enums.GPTServiceProviderEnum;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Objects;

/**
 * 百度云千帆大模型SSE事件监听器
 *
 * <AUTHOR> (<EMAIL>)
 * @version BceWenXinSseEventSourceListener.java, v1.0 11/29/2023 19:36 John Exp$
 */
@Slf4j
@Getter
public class BceWenXinSseEventSourceListener extends AbsGptSseEventSourceListener {
    public BceWenXinSseEventSourceListener(GPTServiceProviderEnum gptServiceProvider, SseEmitter sseEmitter, String contentId, String accountId, RAtomicLong atomicLong) {
        super(gptServiceProvider, sseEmitter, contentId, accountId, atomicLong);
    }

    @Override
    protected boolean processContent(String data, Consumer<String> processingDataHandler) {
        LogUtil.info(log, "BceWenXinSseEventSourceListener.processContent >> SSE.{} >> contentId:{} data:{}", getGptServiceProvider(), contentId, data);
        // 转换为JSON对象
        val completionResponse = JSONObject.parseObject(data, BceWenXinAigcResponse.class);
        if (Objects.isNull(completionResponse))  {
            throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("三方返回数据为空");
        }
        // 结束标志位
        // 移除回车换行符号
        val content = removeCRLF(completionResponse.getResult());
        if (StringUtils.isNotBlank(content)) {
            // 处理数据
            processingDataHandler.accept(content);
        }
        return completionResponse.isEnd();
    }
}
