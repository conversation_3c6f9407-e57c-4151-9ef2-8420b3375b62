package com.huike.nova.service.listener.gpt;

import com.annimon.stream.function.Consumer;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.TraceIdGenerator;
import com.huike.nova.service.enums.GPTServiceProviderEnum;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RAtomicLong;
import org.slf4j.MDC;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Nullable;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;

/**
 * GPT SSE接口事件监听
 *
 * <AUTHOR> (<EMAIL>)
 * @version AbsGptSseEventSourceListener.java, v1.0 11/29/2023 00:45 John Exp$
 */
@RequiredArgsConstructor
@Data
@Slf4j
public abstract class AbsGptSseEventSourceListener extends EventSourceListener {
    /**
     * 结束标志位
     */
    public static final String DONE = "[DONE]";

    /**
     * 结束标志位2
     */
    public static final String STOP = "stop";

    /**
     * GPT 服务提供商
     */
    protected final GPTServiceProviderEnum gptServiceProvider;

    /**
     * SSe对象
     */
    protected final SseEmitter sseEmitter;

    /**
     * 消息唯一ID
     */
    protected final String contentId;

    /**
     * 登录账号ID
     */
    protected final String accountId;

    /**
     * 调用次数的Atomic
     */
    protected final RAtomicLong atomicLong;

    /**
     * 错误回调
     */
    private BiConsumer<Throwable, Response> onFailCallback;

    /**
     * 内容缓存
     */
    private StringBuilder contentCache = new StringBuilder();

    @Override
    public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
        initTraceId();
        LogUtil.info(log, "OpenAiSseEventSourceListener.onOpen >> SSE.{} >> 建立SSE连接, contentId={}, accountId={}", gptServiceProvider, contentId, accountId);
    }

    /**
     * 创建事件
     *
     * @param data 数据
     * @return 事件builder
     */
    protected SseEmitter.SseEventBuilder createSseBuilder(String data) {
        return SseEmitter.event()
               .id(contentId)
               .data(data)
               .reconnectTime(3000);
    }

    /**
     * 流数据获得
     *
     * @param eventSource 事件源
     * @param id 流Id
     * @param type 类型
     * @param data 获取到的数据
     */
    @SneakyThrows
    @Override
    public void onEvent(@NotNull EventSource eventSource, String id, String type, @NotNull String data) {
        initTraceId();
        try {
            // 处理数据，并且判断是否结束
            val proceedFinish = processContent(data, this::proceeding);
            // 如果已经结束，则发送结束标志位
            if (proceedFinish) {
                LogUtil.info(log, "OpenAiSseEventSourceListener.onEvent >> SSE.{} >> SSE返回数据结束, contentId:{}, 服务器响应缓存:{}", getGptServiceProvider(), contentId, contentCache.toString());
                // 发送结束标志位
                sseEmitter.send(createSseBuilder(DONE));
                // 传输完成后自动关闭SSE
                sseEmitter.complete();
            }
        } catch (Throwable e) {
            LogUtil.info(log, "OpenAiSseEventSourceListener.onEvent >> SSE.{} >> SSE信息推送失败, contentId:{}, 错误原因: {}", gptServiceProvider, contentId, e);
            eventSource.cancel();
        }
    }

    /**
     * 处理流式数据
     *
     * @param data 从流中获得数据
     * @param processingDataHandler 结束回调
     * @return 是否结束 true-是 false-否
     */
    protected abstract boolean processContent(String data, Consumer<String> processingDataHandler);

    @SneakyThrows
    protected void proceeding(String content) {
        if (StringUtils.isBlank(content)) {
            return;
        }
        // 客户端缓存发送
        sseEmitter.send(createSseBuilder(content));
        // 缓存写入
        contentCache.append(content);
    }


    @Override
    public void onClosed(@NotNull EventSource eventSource) {
        initTraceId();
        LogUtil.info(log, "OpenAiSseEventSourceListener.onClosed >> SSE.{} >> SSE关闭连接..", gptServiceProvider);
        //不管是否成功，新增缓存
        if (atomicLong.isExists()) {
            //设置缓存
            atomicLong.getAndIncrement();
        } else {
            // 获取当前时间
            LocalTime now = LocalTime.now();
            // 计算今天的最后一秒钟
            LocalTime endOfDay = LocalTime.MAX;
            // 计算还剩下多少秒时间
            long secondsLeft = now.until(endOfDay, ChronoUnit.SECONDS);
            //设置缓存
            atomicLong.getAndIncrement();
            atomicLong.expire(secondsLeft, TimeUnit.SECONDS);
        }
    }


    @SneakyThrows
    @Override
    public void onFailure(@NotNull EventSource eventSource, Throwable t, Response response) {
        initTraceId();
        if (Objects.nonNull(t)) {
            LogUtil.warn(log, "OpenAiSseEventSourceListener.onFailure >> SSE.{} >> SSE连接异常, 错误:{}", gptServiceProvider, StringUtils.defaultIfBlank(t.getMessage(), t.getClass().getName()));
        }
        if (Objects.nonNull(onFailCallback)) {
            onFailCallback.accept(t, response);
        }
        // 关闭连接
        Consumer.Util.safe(EventSource::cancel).accept(eventSource);
        if (Objects.isNull(response)) {
            return;
        }
        ResponseBody body = response.body();
        if (Objects.nonNull(body)) {
            LogUtil.info(log, "OpenAiSseEventSourceListener.onFailure >> SSE.{} >> sse连接异常data: {}，异常: {}", gptServiceProvider, body.string(), t);
        } else {
            LogUtil.info(log, "OpenAiSseEventSourceListener.onFailure >> SSE.{} >> sse连接异常data: {}，异常: {}", gptServiceProvider, response, t);
        }
    }

    /**
     * 移除换行符
     *
     * @param str 原始字符串
     * @return 处理后的字符串
     */
    protected String removeCRLF(@Nullable String str) {
        if (Objects.isNull(str)) {
            return null;
        }
        return StringUtils.replacePattern(str, CommonConstant.PATTERN_CRLF, StringPool.EMPTY);
    }

    protected void initTraceId() {
        MDC.put(CommonConstant.TRACE_ID, TraceIdGenerator.generate());
    }
}
