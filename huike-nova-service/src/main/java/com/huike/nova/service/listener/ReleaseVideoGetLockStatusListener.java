package com.huike.nova.service.listener;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.huike.nova.common.enums.IceTaskInfoUseStatusEnum;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.dao.entity.AilikeBIceSynthesisRecordInfoDO;
import com.huike.nova.dao.repository.AilikeBIceSynthesisRecordInfoDAO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023年03月31日 16:17
 */
@Slf4j(topic = "mq")
@Component
@AllArgsConstructor
public class ReleaseVideoGetLockStatusListener implements MessageListener {

    private AilikeBIceSynthesisRecordInfoDAO ailikeBIceSynthesisRecordInfoDAO;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        LogUtil.info(log, "ReleaseVideoGetLockStatusListener >> 释放获取视频的锁定状态 >> 消息消费开始... >> message = {}", message);
        String jobId;
        jobId = new String(message.getBody(), StandardCharsets.UTF_8);
        if (StringUtils.isEmpty(jobId)) {
            LogUtil.info(log, "ReleaseVideoGetLockStatusListener:接收到的消息为空！message={}", message);
            return Action.CommitMessage;
        }
        // 根据jobId查询
        AilikeBIceSynthesisRecordInfoDO iceSynthesisRecordInfoDO = ailikeBIceSynthesisRecordInfoDAO.getInfoByJobId(jobId);
        if (null == iceSynthesisRecordInfoDO || !IceTaskInfoUseStatusEnum.IN_USE.getValue().equals(iceSynthesisRecordInfoDO.getIsUse())) {
            LogUtil.info(log, "ReleaseVideoGetLockStatusListener:ice数据不存在或状态已变更！jobId={}", jobId);
            return Action.CommitMessage;
        }
        // 释放状态，改为未使用
        ailikeBIceSynthesisRecordInfoDAO.updateUseStatus(jobId, IceTaskInfoUseStatusEnum.UNUSED.getValue());
        LogUtil.info(log, "ReleaseVideoGetLockStatusListener >> 释放获取视频的锁定状态 >> 消息消费完毕... >> message = {}", message);
        return Action.CommitMessage;
    }
}
