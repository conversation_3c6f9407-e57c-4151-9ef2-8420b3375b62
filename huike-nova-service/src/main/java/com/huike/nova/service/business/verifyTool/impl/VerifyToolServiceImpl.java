package com.huike.nova.service.business.verifyTool.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.enums.ChannelCodeEnum;
import com.huike.nova.common.enums.ChannelTypeEnum;
import com.huike.nova.common.enums.DouyinOrderDeliveryTypeEnum;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.OrderGoodsStatusEnum;
import com.huike.nova.common.enums.OrderGoodsVerifyStatusEnum;
import com.huike.nova.common.enums.OrgProductTypeEnum;
import com.huike.nova.common.enums.ProduceEnum;
import com.huike.nova.common.enums.ServiceProviderCouponStatusEnum;
import com.huike.nova.common.enums.VerifyCouponRecordStatusEnum;
import com.huike.nova.common.enums.VerifyGoodsModeEnum;
import com.huike.nova.common.enums.VoucherStatusEnum;
import com.huike.nova.common.enums.qyk.mina.JumpTypeEnum;
import com.huike.nova.common.enums.qyk.mina.QykServiceCardActiveStatusEnum;
import com.huike.nova.common.enums.takeout.CloseGroupBuyingCertificateStatusEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.BizNoBuildUtil;
import com.huike.nova.common.util.FsDateUtils;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.NumberUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.common.util.RetryerUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.param.BuildQykMinaCardDTO;
import com.huike.nova.dao.domain.result.QykVerifyMerchantStoreInfoDTO;
import com.huike.nova.dao.entity.AilikeBGoodsDetailDO;
import com.huike.nova.dao.entity.AilikeBOrderDO;
import com.huike.nova.dao.entity.AilikeBOrderSnapshotDO;
import com.huike.nova.dao.entity.AilikeMerchantStoreDO;
import com.huike.nova.dao.entity.AilikeServiceProviderCouponVerifyDO;
import com.huike.nova.dao.entity.AilikeTiktokLifeProductDO;
import com.huike.nova.dao.entity.GoshMallcooAddPointsRecordDO;
import com.huike.nova.dao.entity.QykAppCardGroupDO;
import com.huike.nova.dao.entity.QykAppUserMerchantCardDO;
import com.huike.nova.dao.entity.QykAppUserServiceCardDO;
import com.huike.nova.dao.entity.TiktokLifeProductGroupDO;
import com.huike.nova.dao.entity.TiktokLifeProductGroupInfoDO;
import com.huike.nova.dao.entity.TiktokLifeProductPoiDO;
import com.huike.nova.dao.entity.VerifyCouponRecordDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.AilikeBGoodsDetailDAO;
import com.huike.nova.dao.repository.AilikeBOrderDAO;
import com.huike.nova.dao.repository.AilikeBOrderSnapshotDAO;
import com.huike.nova.dao.repository.AilikeMerchantStoreDAO;
import com.huike.nova.dao.repository.AilikeServiceProviderCouponVerifyDAO;
import com.huike.nova.dao.repository.AilikeTiktokLifeProductDAO;
import com.huike.nova.dao.repository.AilikeTiktokLifeServingOrderDAO;
import com.huike.nova.dao.repository.AilikeTiktokServiceProviderDAO;
import com.huike.nova.dao.repository.GoshMallcooAddPointsRecordDAO;
import com.huike.nova.dao.repository.QykAppCardGroupDAO;
import com.huike.nova.dao.repository.QykAppUserMerchantCardDAO;
import com.huike.nova.dao.repository.QykAppUserServiceCardDAO;
import com.huike.nova.dao.repository.TiktokLifeProductGroupDAO;
import com.huike.nova.dao.repository.TiktokLifeProductGroupInfoDAO;
import com.huike.nova.dao.repository.TiktokLifeProductPoiDAO;
import com.huike.nova.dao.repository.VerifyCouponRecordDAO;
import com.huike.nova.service.business.CommonService;
import com.huike.nova.service.business.TiktokOpenService;
import com.huike.nova.service.business.common.DingDingCommonService;
import com.huike.nova.service.business.common.ProductStockCommonService;
import com.huike.nova.service.business.common.QykCommonService;
import com.huike.nova.service.business.open.MallcooOpenService;
import com.huike.nova.service.business.qyk.mina.QykBizHelper;
import com.huike.nova.service.business.verify.VerifyCouponDataService;
import com.huike.nova.service.business.verifyTool.VerifyToolService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.VerifyToolServiceObjMapper;
import com.huike.nova.service.domain.model.mina.greenlandopen.GreenLandMemberQueryModel;
import com.huike.nova.service.domain.model.mina.greenlandopen.GreenLandRegisterMemberModel;
import com.huike.nova.service.domain.model.mina.product.ProductGetModel;
import com.huike.nova.service.domain.model.mina.product.UseDateModel;
import com.huike.nova.service.domain.model.openapi.MallcooBaseModel;
import com.huike.nova.service.domain.model.qyk.mina.card.GetCardDetailModel;
import com.huike.nova.service.domain.model.qyk.mina.login.QykMinaLoginModel;
import com.huike.nova.service.domain.model.tiktokopen.CertificateInfoModel;
import com.huike.nova.service.domain.model.tiktokopen.CloseOrderQueryModel;
import com.huike.nova.service.domain.model.tiktokopen.QueryCertificateInfoModel;
import com.huike.nova.service.domain.model.tiktokopen.VerifyInfoModel;
import com.huike.nova.service.domain.model.verifyTool.GetMemberStatusModel;
import com.huike.nova.service.domain.model.verifyTool.MallcooPoiWhiteListModel;
import com.huike.nova.service.domain.model.verifyTool.MemberRegisterModel;
import com.huike.nova.service.domain.model.verifyTool.QueryVerifyStatusModel;
import com.huike.nova.service.domain.param.mina.product.ProductSearchParam;
import com.huike.nova.service.domain.param.openapi.MallcooMemberParam;
import com.huike.nova.service.domain.param.qyk.mina.card.SaveOrderParam;
import com.huike.nova.service.domain.param.tiktokopen.CloseOrderQueryParam;
import com.huike.nova.service.domain.param.verifyTool.GetMemberStatusParam;
import com.huike.nova.service.domain.param.verifyTool.MallCooAddPointsParam;
import com.huike.nova.service.domain.param.verifyTool.MemberRegisterParam;
import com.huike.nova.service.domain.param.verifyTool.PushServiceDoneParam;
import com.huike.nova.service.domain.param.verifyTool.QueryVerifyStatusParam;
import com.huike.nova.service.domain.param.verifyTool.VerifyNumberPhoneReportParam;
import com.huike.nova.service.domain.result.SaveOrderResult;
import com.huike.nova.service.enums.douyin.DouyinOrderStatusEnum;
import com.huike.nova.service.enums.douyin.DouyinRefundStatusEnum;
import com.huike.nova.service.message.MsgProducer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class VerifyToolServiceImpl implements VerifyToolService {

    private final SysConfig sysConfig;

    private VerifyToolServiceObjMapper verifyToolServiceObjMapper;
    private RedissonClient redissonClient;
    private AilikeTiktokLifeProductDAO ailikeTiktokLifeProductDAO;
    private AilikeBOrderDAO ailikeBOrderDAO;
    private QykAppUserServiceCardDAO qykAppUserServiceCardDAO;
    private QykAppUserMerchantCardDAO qykAppUserMerchantCardDAO;
    private AilikeMerchantStoreDAO ailikeMerchantStoreDAO;
    private AilikeTiktokLifeServingOrderDAO ailikeTiktokLifeServingOrderDAO;
    private QykCommonService qykCommonService;
    private CommonService commonService;
    private TiktokLifeProductPoiDAO tiktokLifeProductPoiDAO;
    private TiktokLifeProductGroupDAO tiktokLifeProductGroupDAO;
    private TransactionTemplate transactionTemplate;
    private TiktokLifeProductGroupInfoDAO tiktokLifeProductGroupInfoDAO;
    private QykAppCardGroupDAO qykAppCardGroupDAO;
    private AilikeBGoodsDetailDAO ailikeBGoodsDetailDAO;
    private TiktokOpenService tiktokOpenService;
    private AilikeServiceProviderCouponVerifyDAO ailikeServiceProviderCouponVerifyDAO;
    private AilikeBOrderSnapshotDAO ailikeBOrderSnapshotDAO;
    private VerifyCouponRecordDAO verifyCouponRecordDAO;
    private GoshMallcooAddPointsRecordDAO goshMallcooAddPointsRecordDAO;
    private ProductStockCommonService productStockCommonService;
    private MsgProducer msgProducer;
    private MallcooOpenService mallcooOpenService;
    private VerifyCouponDataService verifyCouponDataService;
    private AilikeTiktokServiceProviderDAO ailikeTiktokServiceProviderDAO;
    private DingDingCommonService dingDingCommonService;

    @Override
    public SaveOrderResult saveCloseOrder(SaveOrderParam param) {
        LogUtil.info(log, "VerifyToolServiceImpl.saveCloseOrder >> 接口开始 >> param = {}", JSON.toJSONString(param));
        // 保存抖音商圈卡订单信息
        try (RedisLockHelper redisLockHelper = RedisLockHelper.create(redissonClient, RedisPrefixConstant.SAVE_TIKTOK_CLOSED_ORDER_LOCK, param.getOrderId())) {
            redisLockHelper.tryLock();
            SaveOrderResult saveOrderResult = new SaveOrderResult();
            List<QykAppUserServiceCardDO> qykAppUserServiceCardDOList = new ArrayList<>();
            List<QykAppUserMerchantCardDO> qykAppUserMerchantCardDOList = new ArrayList<>();
            List<QykAppCardGroupDO> qykAppCardGroupDOList = new ArrayList<>();
            // 系统订单号
            String orderSn;
            // 有订单信息，无子母券信息
            AilikeBOrderDO orderByOutOrderSn = ailikeBOrderDAO.getOrderByOutOrderSn(param.getOrderId());

            if (ObjectUtil.isNotNull(orderByOutOrderSn)) {
                // 订单信息存在，子母券信息不存在，进行补全
                List<QykAppUserServiceCardDO> serviceCardDOS = qykAppUserServiceCardDAO.findByOutOrderSn(param.getOrderId());
                List<AilikeBGoodsDetailDO> ailikeBGoodsDetailDOList = ailikeBGoodsDetailDAO.findGoodsDetailList(orderByOutOrderSn.getOrderSn());
                if (CollectionUtil.isEmpty(serviceCardDOS)) {
                    if (CollectionUtil.isEmpty(ailikeBGoodsDetailDOList)) {
                        throw new CommonException(ErrorCodeEnum.ORDER_ERROR).detailMessage("订单商品详情信息异常！");
                    }
                    // 判断是否系统中的存在的商圈卡
                    AilikeTiktokLifeProductDO snapshotProduct = ailikeTiktokLifeProductDAO.getSnapshotProduct(ailikeBGoodsDetailDOList.get(0).getProductId(), null);
                    if (ObjectUtil.isNull(snapshotProduct) || !OrgProductTypeEnum.BUSINESS_MOTHER_CARD.getValue().equals(snapshotProduct.getOrgProductType())) {
                        // 非系统商圈卡引导页
                        saveOrderResult.setJumpType(JumpTypeEnum.GUIDE_PAGE.getValue());
                        return saveOrderResult;
                    }
                    BuildQykMinaCardDTO buildQykMinaCardDTO = qykCommonService.buildUserQykCardForVerifyToolMina(orderByOutOrderSn, ailikeBGoodsDetailDOList,null);
                    // 录入子母券信息
                    qykAppUserServiceCardDOList.addAll(buildQykMinaCardDTO.getQykAppUserServiceCardDOList());
                    qykAppUserMerchantCardDOList.addAll(buildQykMinaCardDTO.getMerchantCardList());
                    qykAppCardGroupDOList.addAll(buildQykMinaCardDTO.getCardGroupList());
                    List<AilikeBOrderSnapshotDO> ailikeBOrderSnapshotDOList = this.buildOrderSnapshot(orderByOutOrderSn.getOrderSn(), ailikeBGoodsDetailDOList);
                    transactionTemplate.execute(status -> {
                        qykAppUserServiceCardDAO.saveBatch(qykAppUserServiceCardDOList);
                        qykAppUserMerchantCardDAO.saveBatch(qykAppUserMerchantCardDOList);
                        qykAppCardGroupDAO.saveBatch(qykAppCardGroupDOList);
                        return true;
                    });
                    try {
                        ailikeBOrderSnapshotDAO.saveBatch(ailikeBOrderSnapshotDOList);
                    } catch (Exception e) {
                        LogUtil.info(log, "VerifyToolServiceImpl.saveCloseOrder >> 保存订单快照失败 >> ailikeBOrderSnapshotDOList = {}", JSON.toJSONString(ailikeBOrderSnapshotDOList));
                    }
                }

                // 订单快照补全（针对于不使用核销工具激活闭环订单）
                List<AilikeBOrderSnapshotDO> ailikeBOrderSnapshotDOList = this.buildOrderSnapshot(orderByOutOrderSn.getOrderSn(), ailikeBGoodsDetailDOList);
                if (CollectionUtil.isNotEmpty(ailikeBOrderSnapshotDOList)) {
                    try {
                        ailikeBOrderSnapshotDAO.saveBatch(ailikeBOrderSnapshotDOList);
                    } catch (Exception e) {
                        LogUtil.info(log, "VerifyToolServiceImpl.saveCloseOrder >> 订单快照补全-保存订单快照失败 >> ailikeBOrderSnapshotDOList = {}", JSON.toJSONString(ailikeBOrderSnapshotDOList));
                    }
                }

                orderSn = orderByOutOrderSn.getOrderSn();
                if (StringUtils.isEmpty(orderByOutOrderSn.getPurchasePhone()) || orderByOutOrderSn.getPurchasePhone().contains(CommonConstant.PHONE_DESENSITIZATION)) {
                    saveOrderResult.setJumpType(JumpTypeEnum.LOGIN_PAGE.getValue());
                } else {
                    saveOrderResult.setJumpType(JumpTypeEnum.PRODUCT_INFO_PAGE.getValue());
                }
            } else { // 无订单信息
                List<AilikeBOrderSnapshotDO> orderSnapshotDOList = new ArrayList<>();
                CloseOrderQueryModel closeOrderQueryModel = searchOrder(param);
                List<CloseOrderQueryModel.OrdersDTO> ordersList = closeOrderQueryModel.getOrders();
                if (CollectionUtil.isEmpty(ordersList)) {
                    LogUtil.warn(log, "verifyToolServiceImpl.saveCloseOrder >> 查询抖音闭环商圈卡订单列表为空. closeOrderQueryModel:{}", JSONObject.toJSONString(closeOrderQueryModel));
                    throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("查询抖音闭环商圈卡订单列表为空");
                }
                if (CollectionUtil.isEmpty(ordersList.get(0).getProducts())) {
                    LogUtil.warn(log, "verifyToolServiceImpl.saveCloseOrder >> 查询抖音闭环商圈卡订单商品列表为空. closeOrderQueryModel:{}", JSONObject.toJSONString(closeOrderQueryModel));
                    throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("查询抖音闭环商圈卡订单商品列表为空");
                }
                // 判断是否系统中的存在的商圈卡
                String productId = ordersList.get(0).getProducts().get(0).getProductId();
                AilikeTiktokLifeProductDO snapshotProduct = ailikeTiktokLifeProductDAO.getSnapshotProduct(productId, null);
                if (ObjectUtil.isNull(snapshotProduct) || !OrgProductTypeEnum.BUSINESS_MOTHER_CARD.getValue().equals(snapshotProduct.getOrgProductType())) {
                    // 非系统商圈卡引导页
                    saveOrderResult.setJumpType(JumpTypeEnum.GUIDE_PAGE.getValue());
                    return saveOrderResult;
                }
                List<AilikeBOrderDO> orderDOList = new ArrayList<>();
                List<AilikeBGoodsDetailDO> goodsDetailDOList = new ArrayList<>();
                orderSn = BizNoBuildUtil.build(ordersList.get(0).getPayTime());
                for (CloseOrderQueryModel.OrdersDTO orderDTO : ordersList) {
                    // 构建订单信息
//                    AilikeBOrderDO orderDO = buildOrder(orderDTO, orderSn, param.getAccountId());
                    AilikeBOrderDO orderDO = verifyCouponDataService.buildBOrder(orderDTO, orderSn, param.getAccountId());
                    if (ObjectUtil.isNull(orderDO)) {
                        throw new CommonException(ErrorCodeEnum.ORDER_ERROR).detailMessage("创建订单信息异常");
                    }
                    orderDOList.add(orderDO);
                    // 构建订单商品信息
                    List<AilikeBGoodsDetailDO> goodsDetailDOS = buildGoodDetails(orderDTO, orderDTO.getCertificate(), orderSn);
                    if (CollectionUtil.isEmpty(goodsDetailDOS)) {
                        throw new CommonException(ErrorCodeEnum.ORDER_ERROR).detailMessage("创建订单商品详情异常");
                    }
                    goodsDetailDOList.addAll(goodsDetailDOS);
                    // 构建快照信息
                    List<AilikeBOrderSnapshotDO> orderSnapshotDOs = this.buildOrderSnapshot(orderSn, goodsDetailDOS);
                    orderSnapshotDOList.addAll(orderSnapshotDOs);
                    // 构建商圈卡子母券信息
                    BuildQykMinaCardDTO buildQykMinaCardDTO = qykCommonService.buildUserQykCardForVerifyToolMina(orderDO, goodsDetailDOS,null);
                    qykAppUserServiceCardDOList.addAll(buildQykMinaCardDTO.getQykAppUserServiceCardDOList());
                    qykAppUserMerchantCardDOList.addAll(buildQykMinaCardDTO.getMerchantCardList());
                    qykAppCardGroupDOList.addAll(buildQykMinaCardDTO.getCardGroupList());
                }
                transactionTemplate.execute(status -> {
                    ailikeBOrderDAO.saveBatch(orderDOList);
                    ailikeBGoodsDetailDAO.batchSave(goodsDetailDOList);
                    qykAppUserServiceCardDAO.saveBatch(qykAppUserServiceCardDOList);
                    qykAppUserMerchantCardDAO.saveBatch(qykAppUserMerchantCardDOList);
                    qykAppCardGroupDAO.saveBatch(qykAppCardGroupDOList);
                    return true;
                });
                try {
                    ailikeBOrderSnapshotDAO.saveBatch(orderSnapshotDOList);
                } catch (Exception e) {
                    LogUtil.info(log, "VerifyToolServiceImpl.saveCloseOrder >> 保存订单快照失败 >> orderSnapshotDOList = {}", JSON.toJSONString(orderSnapshotDOList));
                }

                saveOrderResult.setJumpType(JumpTypeEnum.LOGIN_PAGE.getValue());
            }

            // 缓存中获取上次访问的商圈卡id
            final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.ORDER_LAST_TIME_SERVICE_CARD_ID, orderSn));
            if (bucket.isExists()) {
                saveOrderResult.setOrderLastTimeServiceCardId(bucket.get());
            } else {
                // 不存在缓存则默认取收个 用ServiceCardId正序排序
                List<String> serviceCardIdList = qykAppUserServiceCardDAO.findServiceCardIdByOrderSn(orderSn);
                saveOrderResult.setOrderLastTimeServiceCardId(serviceCardIdList.stream().sorted(Comparator.naturalOrder()).collect(Collectors.toList()).get(0));
            }
            saveOrderResult.setOrderSn(orderSn);
            return saveOrderResult;
        } catch (CommonException ex) {
            throw ex;
        } catch (Exception ex) {
            LogUtil.warn(log, "verifyToolServiceImpl.saveCloseOrder >> 保存商圈卡闭环订单失败，", ex);
            throw new CommonException(ErrorCodeEnum.TIKTOK_CLOSE_ORDER_SAVE_ERROR).extra("保存抖音商圈卡闭环订单失败，{}", ex.getMessage());
        }
    }

    /**
     * 核销状态查询（轮询接口）
     *
     * @param param 请求参数
     */
    @Override
    public QueryVerifyStatusModel queryVerifyStatus(QueryVerifyStatusParam param) {
        LogUtil.info(log, "VerifyToolServiceImpl.queryVerifyStatus >> 接口开始 >> param = {}", JSON.toJSONString(param));
        return new QueryVerifyStatusModel(saveVerifyRecord(param.getCertificateId(), param.getCouponCode(), param.getSource()));
    }

    /**
     * 推送服务完成
     *
     * @param param 请求参数
     */
    @Override
    public void pushServiceDone(PushServiceDoneParam param) {
        LogUtil.info(log, "VerifyToolServiceImpl.pushServiceDone >> 接口开始 >> param = {}", param);
        if (null == param || StringUtils.isBlank(param.getCertificateId()) || StringUtils.isBlank(param.getVerifyId())) {
            LogUtil.info(log, "VerifyToolServiceImpl.pushServiceDone >> 参数异常 >> param = {}", param);
            return;
        }
        String certificateId = param.getCertificateId();
        // 加锁
        RLock lock = redissonClient.getLock(StrUtil.format(RedisPrefixConstant.LOCK_PUSH_SERVICE_DONE_KEY, certificateId));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("推送服务完成操作等待超时");
            }
            QueryCertificateInfoModel certificateInfoModel;
            try {
                certificateInfoModel = RetryerUtil.<QueryCertificateInfoModel>createRetryer().call(() ->
                        tiktokOpenService.queryCertificateInfo(Collections.singletonList(certificateId), ChannelCodeEnum.VERIFY_TOOL.getValue(), null)
                );
            } catch (Exception ex) {
                LogUtil.info(log, "VerifyToolServiceImpl.pushServiceDone >> 推送服务完成-原生券信息查询-多次请求错误 >> certificateId = {}", certificateId);
                dingDingCommonService.sendVerifyToolDistributionMessage("服务完成推送异常", "推送服务完成-原生券信息查询-多次请求错误", JSONObject.toJSONString(param));
                throw RetryerUtil.toCommonExceptionToCommon(ex).extra("推送服务完成-原生券信息查询-多次请求错误, 参数:{}",
                        JSONObject.toJSONString(param));
            }
            if (null == certificateInfoModel || CollectionUtil.isEmpty(certificateInfoModel.getCertificateInfoList())) {
                LogUtil.info(log, "VerifyToolServiceImpl.pushServiceDone >> 接口结束 >> 原生券信息查询为空 certificateId = {}", certificateId);
                dingDingCommonService.sendVerifyToolDistributionMessage("服务完成推送异常", "原生券信息查询为空", JSONObject.toJSONString(param));
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("原生券信息查询返回为空");
            }
            // 抖音券信息
            CertificateInfoModel tiktokCertificateInfo = certificateInfoModel.getCertificateInfoList().get(0);
            if (!CommonConstant.INTEGER_THREE.equals(tiktokCertificateInfo.getStatus())) {
                if (CommonConstant.INTEGER_ONE.equals(tiktokCertificateInfo.getStatus())) {
                    LogUtil.info(log, "VerifyToolServiceImpl.pushServiceDone >> 接口结束 >> 券状态变更中,mq 重试 certificateInfoModel = {}", JSON.toJSONString(certificateInfoModel));
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("券状态变更中");
                }
                LogUtil.info(log, "VerifyToolServiceImpl.pushServiceDone >> 接口结束 >> 券状态非核销完成，不推送服务完成 certificateInfoModel = {}", JSON.toJSONString(certificateInfoModel));
                dingDingCommonService.sendVerifyToolDistributionMessage("服务完成推送异常", StrUtil.format("券状态非核销完成，不推送服务完成, status:{}", tiktokCertificateInfo.getStatus()), JSONObject.toJSONString(param));
                return;
            }
            List<VerifyInfoModel> verifyInfoList = tiktokCertificateInfo.getVerifyInfoList();
            if (CollectionUtil.isEmpty(verifyInfoList)) {
                LogUtil.info(log, "VerifyToolServiceImpl.pushServiceDone >> 接口结束 >> 核销数据异常 certificateInfoModel = {}", JSON.toJSONString(certificateInfoModel));
                dingDingCommonService.sendVerifyToolDistributionMessage("服务完成推送异常", "核销数据异常", JSONObject.toJSONString(param));
                return;
            }
            VerifyInfoModel verifyInfoModel = verifyInfoList.get(0);
            String tiktokVerifyId = verifyInfoModel.getVerifyId();
            if (!tiktokVerifyId.equals(param.getVerifyId())) {
                LogUtil.info(log, "VerifyToolServiceImpl.pushServiceDone >> 接口结束 >> 核销id不一致 certificateInfoModel = {}", JSON.toJSONString(certificateInfoModel));
                dingDingCommonService.sendVerifyToolDistributionMessage("服务完成推送异常", "核销id不一致", JSONObject.toJSONString(param));
                return;
            }
            tiktokOpenService.pushServiceDone(tiktokCertificateInfo.getOrderId(), ChannelCodeEnum.VERIFY_TOOL.getValue(), Collections.singletonList(tiktokVerifyId));
            LogUtil.info(log, "VerifyToolServiceImpl.pushServiceDone >> 服务推送成功 >> param = {}", param);
            String serviceCardId = param.getServiceCardId();
            if (StringUtils.isNotBlank(serviceCardId)) {
                QykAppUserServiceCardDO serviceCard = qykAppUserServiceCardDAO.getServiceCard(serviceCardId);
                if (null != serviceCard) {
                    serviceCard.setPushStatus(CommonConstant.ONE);
                    serviceCard.setPushTime((int) (System.currentTimeMillis() / 1000));
                    qykAppUserServiceCardDAO.updateById(serviceCard);
                }
            }

        } catch (CommonException commonException) {
            LogUtil.info(log, "VerifyToolServiceImpl.pushServiceDone >> 推送服务完成-CommonException", commonException);
            throw commonException;
        } catch (Exception e) {
            LogUtil.info(log, "VerifyToolServiceImpl.pushServiceDone >> 推送服务完成全局异常", e);
            dingDingCommonService.sendVerifyToolDistributionMessage("服务完成推送异常", "推送服务完成全局异常", JSONObject.toJSONString(param));
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.info(log, "VerifyToolServiceImpl.pushServiceDone >> 锁释放失败 ", e);
            }
        }
    }
    /**
     * 获取会员状态
     *
     * @param param 请求参数
     */
    @Override
    public GetMemberStatusModel getMemberStatus(GetMemberStatusParam param) {
        LogUtil.info(log, "VerifyToolServiceImpl.getMemberStatus >> 接口开始 >> param = {}", JSON.toJSONString(param));
        String serviceCardId = param.getServiceCardId();
        QykAppUserServiceCardDO serviceCard = qykAppUserServiceCardDAO.getServiceCard(serviceCardId);
        GetMemberStatusModel model = GetMemberStatusModel.init();
        if (null == serviceCard){
            LogUtil.info(log, "VerifyToolServiceImpl.getMemberStatus >> 商圈卡为空 >> param = {}", param);
            return model;
        }
        AilikeTiktokLifeProductDO productDO = ailikeTiktokLifeProductDAO.getSnapshotProductByProductId(serviceCard.getProductId());
        if (null == productDO){
            LogUtil.info(log, "VerifyToolServiceImpl.getMemberStatus >> 商品信息异常 >> param = {}", param);
            return model;
        }
        List<TiktokLifeProductPoiDO> productPoiDOList = tiktokLifeProductPoiDAO.queryByBusinessProductId(productDO.getBusinessProductId());
        if (CollectionUtil.isEmpty(productPoiDOList)){
            LogUtil.info(log, "VerifyToolServiceImpl.getMemberStatus >> 适用广场异常 >> param = {}", param);
            return model;
        }
        List<MallcooPoiWhiteListModel> mallcooPoiWhiteList = JSONObject.parseArray(sysConfig.getVerifyToolMallcooPoiWhiteList(), MallcooPoiWhiteListModel.class);
        Map<String, MallcooPoiWhiteListModel> mallcooPoiWhiteMap = mallcooPoiWhiteList.stream().collect(Collectors.toMap(MallcooPoiWhiteListModel::getPoiId, Function.identity()));
        for (TiktokLifeProductPoiDO item : productPoiDOList) {
            MallcooPoiWhiteListModel mallcooPoiWhiteListModel = mallcooPoiWhiteMap.get(item.getPoiId());
            if (null == mallcooPoiWhiteListModel){
                continue;
            }
            model.setNeedMember(CommonConstant.INTEGER_ONE);
            model.setPoiId(mallcooPoiWhiteListModel.getPoiId());
            model.setRegisterMemberBanner(mallcooPoiWhiteListModel.getRegisterMemberBanner());
            MallcooMemberParam mallcooParam = new MallcooMemberParam();
            mallcooParam.setAppId(mallcooPoiWhiteListModel.getAppId());
            mallcooParam.setHost(CommonConstant.GREEN_LAND_MEMBER_MEMBER_QUERY_HOST);
            mallcooParam.setPublicKey(mallcooPoiWhiteListModel.getPublicKey());
            mallcooParam.setPrivateKey(mallcooPoiWhiteListModel.getPrivateKey());
            mallcooParam.setContent(StrUtil.format("{\"Mobile\":\"{}\"}", param.getMobile()));
            MallcooBaseModel baseInfoModel = mallcooOpenService.call(mallcooParam, MallcooBaseModel.class);
            if (null != baseInfoModel && CommonConstant.INTEGER_ONE.equals(baseInfoModel.getCode()) && StringUtils.isNotBlank(baseInfoModel.getData())){
                GreenLandMemberQueryModel memberQueryModel = JSON.parseObject(baseInfoModel.getData(), GreenLandMemberQueryModel.class);
                model.setIsMember(null == memberQueryModel? CommonConstant.INTEGER_TWO:CommonConstant.INTEGER_ONE);
            }
            return model;
        }
        return model;
    }

    /**
     * 会员注册
     *
     * @param param 请求参数
     */
    @Override
    public MemberRegisterModel memberRegister(MemberRegisterParam param) {
        LogUtil.info(log, "VerifyToolServiceImpl.memberRegister >> 接口开始 >> param = {}", JSON.toJSONString(param));
        List<MallcooPoiWhiteListModel> mallcooPoiWhiteList = JSONObject.parseArray(sysConfig.getVerifyToolMallcooPoiWhiteList(), MallcooPoiWhiteListModel.class);
        Map<String, MallcooPoiWhiteListModel> mallcooPoiWhiteMap = mallcooPoiWhiteList.stream().collect(Collectors.toMap(MallcooPoiWhiteListModel::getPoiId, Function.identity()));
        MallcooPoiWhiteListModel mallcooPoiWhiteListModel = mallcooPoiWhiteMap.get(param.getPoiId());
        if (null == mallcooPoiWhiteListModel){
            LogUtil.info(log, "VerifyToolServiceImpl.memberRegister >> 广场权限异常 >> param = {}", param);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("广场权限异常");
        }
        MallcooMemberParam mallcooParam = new MallcooMemberParam();
        mallcooParam.setAppId(mallcooPoiWhiteListModel.getAppId());
        mallcooParam.setHost(CommonConstant.GREEN_LAND_MEMBER_MEMBER_REGISTER_HOST);
        mallcooParam.setPublicKey(mallcooPoiWhiteListModel.getPublicKey());
        mallcooParam.setPrivateKey(mallcooPoiWhiteListModel.getPrivateKey());
        mallcooParam.setContent(StrUtil.format("{\"Mobile\":\"{}\"}", param.getMobile()));
        MallcooBaseModel baseInfoModel = mallcooOpenService.call(mallcooParam, MallcooBaseModel.class);
        if (null ==baseInfoModel){
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("会员注册异常");
        }
        if (!CommonConstant.INTEGER_ONE.equals(baseInfoModel.getCode()) || StringUtils.isBlank(baseInfoModel.getData()) ){
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage(baseInfoModel.getMessage());
        }
        return new MemberRegisterModel(JSON.parseObject(baseInfoModel.getData(),GreenLandRegisterMemberModel.class).getOpenUserID());
    }

    /**
     * 猫酷会员加积分
     *
     * @param mallCooAddPointsParam
     */
    public void mallCooAddPoints(MallCooAddPointsParam mallCooAddPointsParam) {
        String transId = UUID.randomUUID().toString().replace("-", StrUtil.EMPTY).substring(10);
        GoshMallcooAddPointsRecordDO goshMallcooAddPointsRecordDO = new GoshMallcooAddPointsRecordDO();
        goshMallcooAddPointsRecordDO.setScore(mallCooAddPointsParam.getScore());
        goshMallcooAddPointsRecordDO.setOutOrderId(mallCooAddPointsParam.getOutOrderId());
        goshMallcooAddPointsRecordDO.setItemOrderId(mallCooAddPointsParam.getItemOrderId());
        goshMallcooAddPointsRecordDO.setPhoneNumber(mallCooAddPointsParam.getMobile());
        goshMallcooAddPointsRecordDO.setTransId(transId);
        goshMallcooAddPointsRecordDO.setPoiId(mallCooAddPointsParam.getPoiId());
        if (CommonConstant.INTEGER_ONE.equals(mallCooAddPointsParam.getOperateStatus())) {
            // 加积分
            goshMallcooAddPointsRecordDO.setOperateStatus(CommonConstant.INTEGER_ONE);
        } else {
            // 扣除积分
            goshMallcooAddPointsRecordDO.setOperateStatus(CommonConstant.INTEGER_TWO);
        }
        try {
            LogUtil.info(log, "VerifyToolServiceImpl.mallCooAddPoints >> 猫酷调用加积分接口 >> param = {}", JSON.toJSONString(mallCooAddPointsParam));
            GetMemberStatusParam getMemberStatusParam = new GetMemberStatusParam();
            getMemberStatusParam.setServiceCardId(mallCooAddPointsParam.getServiceCardId());
            getMemberStatusParam.setMobile(mallCooAddPointsParam.getMobile());
            // 查询是否会员
            GetMemberStatusModel memberStatus = getMemberStatus(getMemberStatusParam);
            if (CommonConstant.INTEGER_TWO.equals(memberStatus.getIsMember())) {
                LogUtil.warn(log, "VerifyToolServiceImpl.mallCooAddPoints >> 不属于猫酷会员用户 >> param = {}", mallCooAddPointsParam);
                return;
            }
            List<MallcooPoiWhiteListModel> mallcooPoiWhiteList = JSONObject.parseArray(sysConfig.getVerifyToolMallcooPointsWhiteList(), MallcooPoiWhiteListModel.class);
            Map<String, MallcooPoiWhiteListModel> mallcooPoiWhiteMap = mallcooPoiWhiteList.stream().collect(Collectors.toMap(MallcooPoiWhiteListModel::getPoiId, Function.identity()));
            MallcooPoiWhiteListModel mallcooPoiWhiteListModel = mallcooPoiWhiteMap.get(mallCooAddPointsParam.getPoiId());
            if (null == mallcooPoiWhiteListModel){
                LogUtil.info(log, "VerifyToolServiceImpl.mallCooAddPoints >> 该广场未开放积分功能 >> param = {}", mallCooAddPointsParam);
                return;
            }
            MallcooMemberParam mallcooParam = new MallcooMemberParam();
            mallcooParam.setAppId(mallcooPoiWhiteListModel.getAppId());
            if (CommonConstant.INTEGER_ONE.equals(mallCooAddPointsParam.getOperateStatus())) {
                // 加积分
                mallcooParam.setHost(CommonConstant.MALLCOO_ADD_POINTS_HOST);
            } else {
                // 扣除积分
                mallcooParam.setHost(CommonConstant.MALLCOO_REDUCE_POINTS_HOST);
            }
            mallcooParam.setPublicKey(mallcooPoiWhiteListModel.getPublicKey());
            mallcooParam.setPrivateKey(mallcooPoiWhiteListModel.getPrivateKey());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("Mobile", mallCooAddPointsParam.getMobile());
            jsonObject.put("ScoreEvent", 19);
            jsonObject.put("Score", mallCooAddPointsParam.getScore());
            jsonObject.put("TransID",transId);
            mallcooParam.setContent(jsonObject.toJSONString());
            MallcooBaseModel baseInfoModel = mallcooOpenService.call(mallcooParam, MallcooBaseModel.class);
            if (null == baseInfoModel){
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("猫酷调用加积分接口调用异常");
            }
            if (!CommonConstant.INTEGER_ONE.equals(baseInfoModel.getCode())){
                goshMallcooAddPointsRecordDO.setIsSuccess(CommonConstant.INTEGER_TWO);
            } else {
                goshMallcooAddPointsRecordDO.setIsSuccess(CommonConstant.INTEGER_ONE);
            }
            goshMallcooAddPointsRecordDAO.save(goshMallcooAddPointsRecordDO);
        } catch (Exception e) {
            LogUtil.warn(log, "VerifyToolServiceImpl.addPoints >> 猫酷加积分异常", e);
            goshMallcooAddPointsRecordDO.setIsSuccess(CommonConstant.INTEGER_TWO);
            goshMallcooAddPointsRecordDAO.save(goshMallcooAddPointsRecordDO);
        }
    }

    /**
     * 核销手机号上报
     *
     * @param param 请求参数
     */
    @Override
    public void verifyPhoneNumberReport(VerifyNumberPhoneReportParam param) {
        LogUtil.info(log, "VerifyToolServiceImpl.verifyPhoneNumberReport >> 接口开始 >> param = {}", JSON.toJSONString(param));
        String certificateId = param.getCertificateId();
        final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.VERIFY_TOOL_SAVE_VERIFY_RECORD_PHONE_KEY, certificateId));
        // 登录态获取登录信息
        QykMinaLoginModel loginBasicInfo = LoginUtil.getQykMinaLoginBasicInfo();
        // 当前登录用户手机号
        String phoneNumber = loginBasicInfo.getPhoneNumber();
        // 设置缓存
        bucket.set(phoneNumber, CommonConstant.INTEGER_TWO, TimeUnit.HOURS);
    }

    /**
     * 设置小程序跳转path
     *
     * @param accountId 抖音来客 id
     */
    @Override
    public void updateMerchantPath(String accountId) {
        LogUtil.info(log, "VerifyToolServiceImpl.updateMerchantPath >> 接口开始 >> accountId = {}", accountId);
        tiktokOpenService.updateMerchantPath(accountId);
        // 修改服务商配置表核销工具开关
        ailikeTiktokServiceProviderDAO.updateVerifyToolStatusByTiktokLifeId(accountId);
        LogUtil.info(log, "VerifyToolServiceImpl.updateMerchantPath >> 接口结束 >> accountId = {}", accountId);

    }

    /**
     * 保存核销记录
     *
     * @param certificateId 抖音券id
     * @param couponCode    券码
     * @param source        来源 1主查 2核销通知
     */
    public Integer saveVerifyRecord(String certificateId, String couponCode, Integer source) {
        LogUtil.info(log, "VerifyToolServiceImpl.saveVerifyRecord >> 接口开始 >> certificateId={}, couponCode={}, source={}", certificateId, couponCode, source);

        // 加锁
        RLock lock = redissonClient.getLock(StrUtil.format(RedisPrefixConstant.LOCK_VERIFY_TOOL_SAVE_VERIFY_RECORD_KEY, certificateId));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("保存核销记录操作等待超时");
            }

            final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.VERIFY_TOOL_SAVE_VERIFY_RECORD_PHONE_KEY, certificateId));
            if (CommonConstant.INTEGER_ONE.equals(source)) {
                if (!bucket.isExists()) {
                    // 登录态获取登录信息
                    QykMinaLoginModel loginBasicInfo = LoginUtil.getQykMinaLoginBasicInfo();
                    // 当前登录用户手机号
                    String phoneNumber = loginBasicInfo.getPhoneNumber();
                    // 设置缓存
                    bucket.set(phoneNumber, CommonConstant.INTEGER_TWO, TimeUnit.HOURS);
                }
            }
            if (!bucket.isExists()) {
                LogUtil.info(log, "VerifyToolServiceImpl.saveVerifyRecord >> 手机号处理异常 >>certificateId = {},couponCode={},source={}", certificateId, couponCode, source);
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("手机号处理异常");
            }
            String phoneNumber = bucket.get();
            // 查询订单信息
            AilikeBGoodsDetailDO orderGoodsDetailResultDTO = ailikeBGoodsDetailDAO.getByCertificateId(certificateId);
            if (null == certificateId) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("商圈卡不存在");
            }
            AilikeBOrderDO ailikeOrderDO = ailikeBOrderDAO.getOrderByOrderSn(orderGoodsDetailResultDTO.getOrderSn());
            if (null == ailikeOrderDO) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("订单信息异常");
            }
            String itemOrderId = orderGoodsDetailResultDTO.getItemOrderId();
            // 根据抖音券码查询商圈卡母卡
            QykAppUserServiceCardDO serviceCardDO = qykAppUserServiceCardDAO.getByItemOrderId(itemOrderId);
            if (null == serviceCardDO) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("商圈卡不存在");
            }
            QueryCertificateInfoModel certificateInfoModel;
            try {
                certificateInfoModel = RetryerUtil.<QueryCertificateInfoModel>createRetryer().call(() ->
                        tiktokOpenService.queryCertificateInfo(Collections.singletonList(certificateId), ChannelCodeEnum.VERIFY_TOOL.getValue(), null)
                );
            } catch (Exception ex) {
                LogUtil.info(log, "VerifyToolServiceImpl.saveVerifyRecord >> 推送服务完成-原生券信息查询-多次请求错误 >> certificateId = {}", certificateId);
                throw RetryerUtil.toCommonExceptionToCommon(ex).extra("推送服务完成-原生券信息查询-多次请求错误, 参数:{}",
                        certificateId);
            }
            if (null == certificateInfoModel || CollectionUtil.isEmpty(certificateInfoModel.getCertificateInfoList())) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("原生券信息查询返回为空");
            }
            // 抖音券信息
            CertificateInfoModel tiktokCertificateInfo = certificateInfoModel.getCertificateInfoList().get(0);
            // 券状态
            Integer certificateInfoStatus = tiktokCertificateInfo.getStatus();
            // 核销信息
            List<VerifyInfoModel> verifyInfoList = tiktokCertificateInfo.getVerifyInfoList();
            // 抖音订单号
            String outOrderSn = tiktokCertificateInfo.getOrderId();
            AilikeBOrderDO orderDO = ailikeBOrderDAO.getOrderByOutOrderSn(outOrderSn);
            if (null == orderDO) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("订单不存在");
            }
            if (!VoucherStatusEnum.FULFILLED.getValue().equals(certificateInfoStatus) || CollectionUtil.isEmpty(verifyInfoList)) {
                LogUtil.info(log, "VerifyToolServiceImpl.saveVerifyRecord >> 抖音查询核销信息异常 >> certificateId = {}", certificateId);
                return certificateInfoStatus;
            }
            VerifyInfoModel verifyInfoModel = verifyInfoList.stream()
                    .sorted(Comparator.comparing(VerifyInfoModel::getVerifyTime).reversed())
                    .collect(Collectors.toList()).get(0);
            String verifyId = verifyInfoModel.getVerifyId();
            AilikeServiceProviderCouponVerifyDO couponVerifyDO = ailikeServiceProviderCouponVerifyDAO.queryByVerifyId(verifyId);
            if (null != couponVerifyDO) {
                LogUtil.info(log, "VerifyToolServiceImpl.saveVerifyRecord >> 当前核销记录已存在 >> VerifyId = {}", verifyId);
                return certificateInfoStatus;
            }
            AilikeTiktokLifeProductDO productDO = ailikeTiktokLifeProductDAO.getSnapshotProductByBusinessProductId(serviceCardDO.getBusinessProductId());
            // 获取核销记录
            VerifyCouponRecordDO verifyCouponRecordDO = getVerifyCouponRecordDO(tiktokCertificateInfo,couponCode,verifyInfoModel, itemOrderId, ailikeOrderDO, productDO);
            // 获取服务商核销记录
            AilikeServiceProviderCouponVerifyDO verifyDO = getAilikeServiceProviderCouponVerifyDO(couponCode, itemOrderId, tiktokCertificateInfo, verifyInfoModel, outOrderSn, productDO, orderDO, orderGoodsDetailResultDTO, phoneNumber);
            // 权益卡更新逻辑
            String serviceCardId = serviceCardDO.getServiceCardId();

            // 事务处理
            transactionTemplate.execute(status -> {
                // 修改商品详情表核销状态,包括券码
                ailikeBGoodsDetailDAO.updateVerifyStatus(OrderGoodsVerifyStatusEnum.FINISH_VERIFY.getValue(), couponCode, itemOrderId);
                // 保存核销信息
                verifyCouponRecordDAO.saveVerifyCouponRecord(verifyCouponRecordDO);
                // 服务商核销记录表
                ailikeServiceProviderCouponVerifyDAO.save(verifyDO);
                // 母卡修改item_order_id字段和激活状态，子卡状态全部修改
                qykAppUserServiceCardDAO.updateItemOrderIdAndStatus(itemOrderId, serviceCardId, couponCode, FieldEncryptUtil.encode(phoneNumber));
                // 更新子卡
                //qykAppUserMerchantCardDAO.updateStatusByServiceCardId(serviceCardId, QykMerchantCardStatusEnum.NOT_USED.getValue(), null);
                qykAppUserMerchantCardDAO.activateMerchantCardsStateByServiceCardId(serviceCardId, null);
                // 更新订单中的购买人手机号
                ailikeBOrderDAO.updatePhoneByOutOrderSn(serviceCardDO.getOutOrderSn(), phoneNumber);
                return true;
            });
            PushServiceDoneParam mqParam = new PushServiceDoneParam();
            mqParam.setCertificateId(certificateId);
            mqParam.setVerifyId(verifyInfoModel.getVerifyId());
            mqParam.setServiceCardId(serviceCardId);
            // 推送服务完成
            msgProducer.sendMessage(ProduceEnum.PUSH_SERVICE_DONE, JSON.toJSONString(mqParam));
            // pushServiceDone(mqParam);
            // 猫酷加积分
            MallCooAddPointsParam mallcooAddPointsParam = new MallCooAddPointsParam();
            AilikeMerchantStoreDO storeInfo = ailikeMerchantStoreDAO.getStoreByMerchantAgentIdPoiId(verifyDO.getVerificationStoreId(), serviceCardDO.getMerchantAgentId());
            if (ObjectUtil.isNull(storeInfo)) {
                LogUtil.warn(log, "VerifyToolServiceImpl.saveVerifyRecord >> 加积分查询门店信息异常 >> verifyDO = {}",verifyDO );
                return certificateInfoStatus;
            }
            mallcooAddPointsParam.setPoiId(storeInfo.getPoiId());
            mallcooAddPointsParam.setMobile(phoneNumber);
            mallcooAddPointsParam.setScore(verifyDO.getCouponSaleAmount().intValue());
            mallcooAddPointsParam.setServiceCardId(serviceCardId);
            mallcooAddPointsParam.setItemOrderId(verifyDO.getItemOrderId());
            mallcooAddPointsParam.setOutOrderId(verifyDO.getOrderId());
            mallcooAddPointsParam.setOperateStatus(CommonConstant.INTEGER_ONE);
            this.mallCooAddPoints(mallcooAddPointsParam);
            return certificateInfoStatus;
        } catch (Exception e) {
            LogUtil.info(log, "VerifyToolServiceImpl.saveVerifyRecord >> 保存核销记录全局异常", e);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("保存核销记录全局异常");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.info(log, "VerifyToolServiceImpl.saveVerifyRecord >> 锁释放失败 ", e);
            }
        }

    }

    private static @NotNull VerifyCouponRecordDO getVerifyCouponRecordDO(CertificateInfoModel tiktokCertificateInfo,String couponCode,VerifyInfoModel verifyInfoModel, String itemOrderId, AilikeBOrderDO ailikeOrderDO, AilikeTiktokLifeProductDO productDO) {
        VerifyCouponRecordDO verifyCouponRecordDO = new VerifyCouponRecordDO();
        verifyCouponRecordDO.setVerifyTime(String.valueOf(verifyInfoModel.getVerifyTime() * CommonConstant.LONG_THOUSAND));
        verifyCouponRecordDO.setOutOrderSn(ailikeOrderDO.getOutOrderSn());
        verifyCouponRecordDO.setItemOrderId(itemOrderId);
        verifyCouponRecordDO.setCertificateCode(couponCode);
        verifyCouponRecordDO.setCertificateId(tiktokCertificateInfo.getCertificateId());
        verifyCouponRecordDO.setVerifyToken(verifyInfoModel.getVerifyId());
        verifyCouponRecordDO.setVerifyId(verifyInfoModel.getVerifyId());
        // verifyCouponRecordDO.setResultCode(verifyResult.getResultCode().toString());
        // verifyCouponRecordDO.setResultMsg(verifyResult.getResultMsg());
        verifyCouponRecordDO.setVerifyStatus(VerifyCouponRecordStatusEnum.SUCCESS.getValue());
        verifyCouponRecordDO.setGoodsMode(VerifyGoodsModeEnum.CLOSE.getValue());
        // verifyCouponRecordDO.setStoreId(storeId);
        verifyCouponRecordDO.setMerchantId(ailikeOrderDO.getMerchantId());
        verifyCouponRecordDO.setOrderSn(ailikeOrderDO.getOrderSn());
        // 查询门店信息
        // AilikeMerchantStoreDO storeInfo = ailikeMerchantStoreDAO.getStoreInfo(storeId);
        // if (Objects.nonNull(storeInfo)) {
        //     verifyCouponRecordDO.setStoreName(storeInfo.getMerchantStoreName());
        // }
        verifyCouponRecordDO.setOrgProductType(productDO.getOrgProductType());
        return verifyCouponRecordDO;
    }

    private AilikeServiceProviderCouponVerifyDO getAilikeServiceProviderCouponVerifyDO(String couponCode, String itemOrderId, CertificateInfoModel tiktokCertificateInfo, VerifyInfoModel verifyInfoModel, String outOrderSn, AilikeTiktokLifeProductDO productDO, AilikeBOrderDO orderDO, AilikeBGoodsDetailDO orderGoodsDetailResultDTO, String phoneNumber) {
        AilikeServiceProviderCouponVerifyDO verifyDO = new AilikeServiceProviderCouponVerifyDO();
        verifyDO.setBusinessOrderSn(BizNoBuildUtil.build());
        verifyDO.setItemOrderId(itemOrderId);
        //  券id拿不到-当前处理方案   能拿到取券code   拿不到置空
        verifyDO.setCouponCode(couponCode);
        verifyDO.setVerifyId(verifyInfoModel.getVerifyId());
        verifyDO.setCertificateId(tiktokCertificateInfo.getCertificateId());
        verifyDO.setVerificationTime(FsDateUtils.longTimeToDate(verifyInfoModel.getVerifyTime() * CommonConstant.LONG_THOUSAND));
        // 查询商品可使用门店信息
        List<QykVerifyMerchantStoreInfoDTO> storeIdList = ailikeMerchantStoreDAO.findQykStoreIdList(productDO.getBusinessProductId());
        if (CollectionUtil.isEmpty(storeIdList)) {
            throw new CommonException(ErrorCodeEnum.ORDER_ERROR).detailMessage("适用广场不存在");
        }
        QykVerifyMerchantStoreInfoDTO storeInfo = CollectionUtil.getFirst(storeIdList);
        verifyDO.setVerificationStoreId(storeInfo.getPoiId());
        verifyDO.setOrderId(outOrderSn);

        verifyDO.setCouponStatus(ServiceProviderCouponStatusEnum.USED.getValue());
        verifyDO.setPayTime(FsDateUtils.longTimeToDate(Long.valueOf(orderDO.getPayTime())));
        verifyDO.setTiktokProductId(orderGoodsDetailResultDTO.getProductId());
        verifyDO.setTiktokProductName(orderGoodsDetailResultDTO.getProductName());
        verifyDO.setCouponOriginPrice(orderGoodsDetailResultDTO.getOriginPrice());
        verifyDO.setCouponSaleAmount(orderGoodsDetailResultDTO.getPrice());
        verifyDO.setCouponCode(couponCode);
        verifyDO.setPhoneNumber(phoneNumber);

        // 额外数据更新
        verifyCouponDataService.updateExtraServiceProviderCouponVerifyRecordInfo(verifyDO, productDO);
        return verifyDO;
    }


    /**
     * 生成用户商圈卡信息
     *
     * @param orderInfo  订单信息
     * @param detailList 商品单列表
     * @return 生成的商圈卡的列表
     * @deprecated 已过时，使用{@link QykCommonService#buildUserQykCardForVerifyToolMina(AilikeBOrderDO, List, String)}
     */
    @Deprecated
    private BuildQykMinaCardDTO buildUserQykCard(AilikeBOrderDO orderInfo, List<AilikeBGoodsDetailDO> detailList) {
        BuildQykMinaCardDTO buildQykMinaCardDTO = new BuildQykMinaCardDTO();
        String orderSn = orderInfo.getOrderSn();
        if (CollectionUtil.isEmpty(detailList)) {
            throw new CommonException(ErrorCodeEnum.ORDER_ERROR).detailMessage("商品详情查询异常");
        }
        String productId = detailList.get(0).getProductId();
        // 根据抖音商品ID查询商品信息, 开环品都是5
        AilikeTiktokLifeProductDO productDetail = ailikeTiktokLifeProductDAO.getSnapshotProduct(productId, null);
        if (Objects.isNull(productDetail)) {
            throw new CommonException(ErrorCodeEnum.ORDER_ERROR).detailMessage("商品信息查询异常");
        }
        // 只有商圈卡才需要保存权益卡信息
        if (!OrgProductTypeEnum.isBusinessMasterCard(productDetail.getOrgProductType())) {
            return null;
        }
        // 查询商圈卡是否已经生成，防止手动补单重复调用
        Set<String> existedItemOrderId = qykAppUserServiceCardDAO.findServiceCardDOListByOrderSn(orderInfo.getOrderSn())
                .stream().map(QykAppUserServiceCardDO::getItemOrderId).collect(Collectors.toSet());
        // 待创建的商圈卡列表
        List<AilikeBGoodsDetailDO> goodsDetailListToBeCreated = Lists.newArrayList();
        for (AilikeBGoodsDetailDO goodsDetailDO : detailList) {
            // 如果当前不存在记录，则添加到待创建列表中
            if (!existedItemOrderId.contains(goodsDetailDO.getItemOrderId())) {
                goodsDetailListToBeCreated.add(goodsDetailDO);
            }
        }
        if (CollectionUtil.isEmpty(goodsDetailListToBeCreated)) {
            List<String> itemOrderIdList = detailList.stream().map(AilikeBGoodsDetailDO::getItemOrderId).collect(Collectors.toList());
            LogUtil.warn(log, "当前商圈卡已生成, 订单Id:{}, 商品单号:{}", orderInfo.getOrderSn(), JSONObject.toJSONString(itemOrderIdList));
            // 根据商品单，查找未激活的商圈卡
            return null;
        }
        String businessProductId = productDetail.getBusinessProductId();
        try {
            // 循环多张子券
            for (AilikeBGoodsDetailDO goodsDetail : goodsDetailListToBeCreated) {
                String serviceCardId = "QYK-S" + commonService.buildIncr();
                // 用户商圈卡
                QykAppUserServiceCardDO serviceCardDO = new QykAppUserServiceCardDO();
                serviceCardDO.setServiceCardId(serviceCardId);
                serviceCardDO.setBusinessProductId(businessProductId);
                serviceCardDO.setProductId(productDetail.getProductId());
                serviceCardDO.setOrderSn(orderInfo.getOrderSn());
                serviceCardDO.setOutOrderSn(orderInfo.getOutOrderSn());
                serviceCardDO.setMerchantAgentId(productDetail.getMerchantAgentId());
                serviceCardDO.setProductName(productDetail.getProductName());
                serviceCardDO.setActiveStatus(QykServiceCardActiveStatusEnum.NOT_ACTIVATED.getValue());
                // 商品信息
                serviceCardDO.setGoodsInfo(StringPool.EMPTY);
                // 如果商品的商品单号码不为空
                if (StringUtils.isNotBlank(goodsDetail.getItemOrderId())) {
                    serviceCardDO.setItemOrderId(goodsDetail.getItemOrderId());
                }
                // 如果商品的券码不为空
                if (StringUtils.isNotBlank(goodsDetail.getCouponCode())) {
                    serviceCardDO.setCouponCode(goodsDetail.getCouponCode());
                }
                // 门店信息
                List<TiktokLifeProductPoiDO> poiDOS = tiktokLifeProductPoiDAO.queryByBusinessProductId(businessProductId);
                // 通道类型
                serviceCardDO.setChannelType(orderInfo.getChannelType());
                serviceCardDO.setStoreInfo(JSONObject.toJSONString(poiDOS));
                String productAttrs = productDetail.getProductAttrs();
                // 对商品进行设置，取头图和使用起讫时间
                JSONObject jsonObject = QykBizHelper.parseProductAttrByChannelType(productDetail.getChannelType(), productAttrs);
                if (Objects.nonNull(jsonObject)) {
                    List<GetCardDetailModel> imageList = JSONArray.parseArray(jsonObject.getString("qyk_card_face"), GetCardDetailModel.class);
                    if (!CollectionUtil.isEmpty(imageList)) {
                        serviceCardDO.setQykCardFace(CollectionUtil.getFirst(imageList).getUrl());
                    }
                    UseDateModel date = JSONObject.parseObject(jsonObject.getString("use_date"), UseDateModel.class);
                    // 开始时间和结束时间
                    if (Objects.nonNull(date)) {
                        serviceCardDO.setStartTime(FsDateUtils.convertTimeToStart(date.getUseStartDate()));
                        serviceCardDO.setEndTime(FsDateUtils.convertTimeToEnd(date.getUseEndDate()));
                    }
                }
                // 获取商圈卡组信息列表
                List<QykAppCardGroupDO> list = Lists.newArrayList();
                // 查询商品组列表
                List<TiktokLifeProductGroupDO> groupDOList = tiktokLifeProductGroupDAO.findList(businessProductId);
                Map<String, String> map = Maps.newHashMap();
                for (TiktokLifeProductGroupDO tiktokLifeProductGroupDO : groupDOList) {
                    String cardGroupId = "QYK-G" + commonService.buildIncr();
                    QykAppCardGroupDO cardGroupDO = new QykAppCardGroupDO();
                    cardGroupDO.setCardGroupId(cardGroupId);
                    cardGroupDO.setCardGroupName(tiktokLifeProductGroupDO.getGroupName());
                    cardGroupDO.setOptionCount(tiktokLifeProductGroupDO.getOptionCount());
                    cardGroupDO.setOptionName(tiktokLifeProductGroupDO.getOptionName());
                    cardGroupDO.setServiceCardId(serviceCardId);
                    map.put(tiktokLifeProductGroupDO.getBusinessGroupId(), cardGroupId);
                    list.add(cardGroupDO);
                }
                // 查看商家卡信息
                List<QykAppUserMerchantCardDO> merchantCardList = Lists.newArrayList();
                this.getCardStoreList(businessProductId, serviceCardDO, merchantCardList, map);
                if (CollectionUtil.isEmpty(merchantCardList)) {
                    LogUtil.warn(log, "VerifyToolServiceImpl.buildUserQykCard >> 没有可用的商圈卡子卡信息, 订单号:{}, 渠道订单号:{}", orderSn, orderInfo.getOutOrderSn());
                    throw new CommonException(ErrorCodeEnum.ORDER_ERROR).detailMessage("没有可用的商圈卡子卡信息");
                }
                // 构建权益卡信息
                buildQykMinaCardDTO.getQykAppUserServiceCardDOList().add(serviceCardDO);
                buildQykMinaCardDTO.getMerchantCardList().addAll(merchantCardList);
                buildQykMinaCardDTO.getCardGroupList().addAll(list);
            }
            return buildQykMinaCardDTO;
        } catch (CommonException ex) {
            LogUtil.warn(log, "VerifyToolServiceImpl.buildUserQykCard >> 权益卡生成失败, 订单号:{}, 外部订单号:{}, 通道:{}, 错误:", orderSn, orderInfo.getOutOrderSn(), orderInfo.getChannelType(), ex);
            throw ex.extra("[权益卡新增] 权益卡新增异常, orderSn: {}", orderSn);
        } catch (Exception e) {
            throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage(e.getMessage()).extra("[权益卡新增] 权益卡新增异常, orderSn: {}", orderSn);
        }
    }

    /**
     * 获取商家券信息
     *
     * @param businessProductId 业务商品Id
     * @param serviceCardDO     商圈卡记录
     * @param merchantCardList  商家券记录
     * @param map               商品组-快照商品组映射
     */

    private void getCardStoreList(String businessProductId, QykAppUserServiceCardDO serviceCardDO, List<QykAppUserMerchantCardDO> merchantCardList, Map<String, String> map) {
        // 根据父商品Id查询子商品
        List<TiktokLifeProductGroupInfoDO> list = tiktokLifeProductGroupInfoDAO.findList(businessProductId);
        for (TiktokLifeProductGroupInfoDO qykProductGroupContentDO : list) {
            String subsetProductId = qykProductGroupContentDO.getSubsetProductId();
            // 判断数量
            for (int i = 0; i < qykProductGroupContentDO.getCount(); i++) {
                String cardGroupId = map.get(qykProductGroupContentDO.getBusinessGroupId());
                QykAppUserMerchantCardDO merchantCardDO = qykCommonService.createNewUserMerchantCard(subsetProductId, serviceCardDO, cardGroupId);
                merchantCardList.add(merchantCardDO);
            }
        }
    }


    /**
     * 查询订单详情
     *
     * @param param
     * @return
     */
    private CloseOrderQueryModel searchOrder(SaveOrderParam param) {
        CloseOrderQueryModel closeOrderQueryModel;
        // 先去缓存中获取订单详情，若无则调用抖音接口查询
        RBucket<CloseOrderQueryModel> orderBucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.TIKTOK_CLOSE_ORDER_SEARCH_KEY, param.getOrderId()));
        boolean exists = orderBucket.isExists();
        if (exists) {
            closeOrderQueryModel = orderBucket.get();
        } else {
            // 抖音接口查询订单信息
            CloseOrderQueryParam queryParam = new CloseOrderQueryParam()
                    .setPageSize(1)
                    .setPageNum(100)
                    .setOrderId(param.getOrderId())
                    .setAccountId(param.getAccountId());
            closeOrderQueryModel = SpringUtil.getBean(TiktokOpenService.class).closeOrderQuery(queryParam);
        }
        return closeOrderQueryModel;
    }

    /**
     * 构建订单信息
     *
     * @param ordersDTO
     * @param orderSn
     * @param accountId
     * @return
     */
    private AilikeBOrderDO buildOrder(CloseOrderQueryModel.OrdersDTO ordersDTO, String orderSn, String accountId) {
        AilikeBOrderDO ailikeBOrderDO = new AilikeBOrderDO();
        ailikeBOrderDO.setOrderSn(orderSn);
        ailikeBOrderDO.setOutOrderSn(ordersDTO.getOrderId());
        ailikeBOrderDO.setChannelType(ChannelTypeEnum.TIKTOK.getValue());
        // 折扣相关
        ailikeBOrderDO.setTotalAmount(NumberUtil.centsToYuan(ordersDTO.getOriginalAmount()));
        ailikeBOrderDO.setDiscountAmount(NumberUtil.centsToYuan(ordersDTO.getDiscountAmount()));
        ailikeBOrderDO.setPayAmount(NumberUtil.centsToYuan(ordersDTO.getPayAmount()));
        ailikeBOrderDO.setAppId(sysConfig.getServiceClientKey());
        // 子订单优惠信息
        // 商家优惠：商家实际到账会扣减商家优惠
        BigDecimal merchantDiscount = BigDecimal.ZERO;
        // 平台优惠：商家实际到账不会进行扣减
        BigDecimal platformDiscount = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(ordersDTO.getSubOrderAmountInfos())) {
            for (val subOrderAmountInfo : ordersDTO.getSubOrderAmountInfos()) {
                if (CollectionUtil.isNotEmpty(subOrderAmountInfo.getDiscounts())) {
                    for (val discount : subOrderAmountInfo.getDiscounts()) {
                        merchantDiscount = merchantDiscount.add(NumberUtil.centsToYuan(discount.getMerchantDiscountAmount()));
                        platformDiscount = platformDiscount.add(NumberUtil.centsToYuan(discount.getPlatformDiscountAmount()));
                    }
                }
            }
        }
        // 订单总商家折扣
        ailikeBOrderDO.setOrderMerchantDiscount(merchantDiscount);
        // 订单总平台折扣
        ailikeBOrderDO.setOrderPlatformDiscount(platformDiscount);
        // 获取订单的POI ID或者意向POI Id
        String poiId = StringUtils.defaultIfBlank(ordersDTO.getPoiId(), ordersDTO.getIntentionPoiId());
        // 获得订单的POI ID
        // 如果通过直播间下单，可能PoiId为空
        if (StringUtils.isNotBlank(poiId)) {
            val merchantStoreInfo = ailikeMerchantStoreDAO.getStoreInfoByPoiId(poiId);
            if (ObjectUtil.isNotNull(merchantStoreInfo)) {
                ailikeBOrderDO.setMerchantId(merchantStoreInfo.getMerchantId());
                ailikeBOrderDO.setStoreId(merchantStoreInfo.getMerchantStoreId());
            }

            // 如果不存在PoiId，则只需要填入merchantId
        } else {
            // 根据服务订单查询该商家是否在服务中
            val servingOrderDO = ailikeTiktokLifeServingOrderDAO.getServingOrderByMerchantId(accountId);
            if (Objects.isNull(servingOrderDO)) {
                LogUtil.warn(log, "verifyToolServiceImpl.buildBOrder >> 授权中但不在服务中的商家,这笔订单将不会落库。accountId = {}", accountId);
                return null;
            }
        }
        ailikeBOrderDO.setDeliveryType(DouyinOrderDeliveryTypeEnum.CLOSE.getValue());
        val orderStatusEnum = DouyinOrderStatusEnum.getByValue(ordersDTO.getOrderStatus());
        // 支付状态
        if (Objects.nonNull(orderStatusEnum) && Objects.nonNull(orderStatusEnum.toOrderPayStatusEnum())) {
            ailikeBOrderDO.setPayStatus(orderStatusEnum.toOrderPayStatusEnum().getValue());
        }
        val certificates = ordersDTO.getCertificate();
        // 根据券码信息，获得退款状态
        if (CollectionUtil.isNotEmpty(certificates)) {
            // 退款状态 1-未退款 2-退款中（部分退款） 3-退款成功
            int refundCount = CommonConstant.ZERO;
            for (val certificate : certificates) {
                val status = CloseGroupBuyingCertificateStatusEnum.getByValue(certificate.getItemStatus());
                // 退款中或者已退款的状态
                if (CloseGroupBuyingCertificateStatusEnum.REFUNDED.equals(status) || CloseGroupBuyingCertificateStatusEnum.REFUNDING.equals(status)) {
                    refundCount += CommonConstant.ONE;
                }
            }
            // 未退款
            if (CommonConstant.ZERO.equals(refundCount)) {
                ailikeBOrderDO.setRefundStatus(DouyinRefundStatusEnum.NOT_REFUND.getValue());
                // 全部退款
            } else if (certificates.size() == refundCount) {
                ailikeBOrderDO.setRefundStatus(DouyinRefundStatusEnum.REFUND_SUCCESS.getValue());
                // 部分退款
            } else {
                ailikeBOrderDO.setRefundStatus(DouyinRefundStatusEnum.REFUNDING.getValue());
            }
        }
        ailikeBOrderDO.setPayTime(String.valueOf(Instant.ofEpochSecond(ordersDTO.getPayTime()).toEpochMilli()));
        ailikeBOrderDO.setGoodsCount(ordersDTO.getCount());
        ailikeBOrderDO.setMarketingDetailInfo(StringPool.EMPTY);

        // 获得商品信息
        if (CollectionUtil.isNotEmpty(ordersDTO.getProducts())) {
            CloseOrderQueryModel.OrdersDTO.ProductDTO product = CollectionUtil.getFirst(ordersDTO.getProducts());
            if (product != null) {
                // 查询商品是否存在
                AilikeTiktokLifeProductDO productDO = ailikeTiktokLifeProductDAO.getSnapshotProductByProductId(product.getProductId());
                if (productDO != null) {
                    ailikeBOrderDO.setChannelType(productDO.getChannelType());
                    ailikeBOrderDO.setChannelCode(productDO.getChannelCode());
                }
            }
        }

        return ailikeBOrderDO;
    }


    private List<AilikeBGoodsDetailDO> buildGoodDetails(CloseOrderQueryModel.OrdersDTO ordersDTO, List<CloseOrderQueryModel.OrdersDTO.CertificateDTO> certificate, String orderSn) {
        AilikeTiktokLifeProductDO productDO = ailikeTiktokLifeProductDAO.getProductDetail(ordersDTO.getSkuId(), null);
        // 子单信息
        List<CloseOrderQueryModel.OrdersDTO.SubOrderAmountInfosDTO> subOrderAmountInfos = ordersDTO.getSubOrderAmountInfos();
        // 商品单列表
        List<AilikeBGoodsDetailDO> goodsDetailDOList = new ArrayList<>(ordersDTO.getCertificate().size());
        Map<String, CloseOrderQueryModel.OrdersDTO.ProductDTO> productDTOMaps = ordersDTO.getProducts().stream().collect(Collectors.toMap(
                CloseOrderQueryModel.OrdersDTO.ProductDTO::getProductId, Function.identity(), (l, r) -> r
        ));

        for (int i = 0; i != certificate.size(); ++i) {
            CloseOrderQueryModel.OrdersDTO.CertificateDTO certificateDTO = certificate.get(i);

            AilikeBGoodsDetailDO ailikeBGoodsDetailDO = new AilikeBGoodsDetailDO();
            ailikeBGoodsDetailDO.setOrderSn(orderSn);
            // 查询到了productDetail
            if (ObjectUtil.isNotNull(productDO)) {
                ailikeBGoodsDetailDO.setProductId(StringUtils.defaultIfBlank(productDO.getProductId(), ordersDTO.getSkuId()));
                ailikeBGoodsDetailDO.setProductName(productDO.getProductName());

                // 未找到，按照订单表里的数据类
            } else if (productDTOMaps.containsKey(ordersDTO.getSkuId())) {
                ailikeBGoodsDetailDO.setProductId(ordersDTO.getSkuId());
                ailikeBGoodsDetailDO.setProductName(productDTOMaps.get(ordersDTO.getSkuId()).getProductName());
            }
            ailikeBGoodsDetailDO.setCertificateId(certificateDTO.getCertificateId());
            ailikeBGoodsDetailDO.setItemOrderId(certificateDTO.getOrderItemId());
            // 设置订单状态
            val itemStatus = CloseGroupBuyingCertificateStatusEnum.getByValue(certificateDTO.getItemStatus());
            ailikeBGoodsDetailDO.setGoodsStatus((CloseGroupBuyingCertificateStatusEnum.REFUNDING.equals(itemStatus) || CloseGroupBuyingCertificateStatusEnum.REFUNDED.equals(itemStatus))
                    ? OrderGoodsStatusEnum.CREATE_REFUND.getValue() : OrderGoodsStatusEnum.CREATE_ORDER.getValue());
            // 核销状态
            ailikeBGoodsDetailDO.setVerifyStatus(CloseGroupBuyingCertificateStatusEnum.PERFORMED.equals(itemStatus)
                    ? OrderGoodsVerifyStatusEnum.FINISH_VERIFY.getValue() : OrderGoodsVerifyStatusEnum.WAIT_VERIFY.getValue());
            val time = new Date(ordersDTO.getPayTime() * 1000L);
            ailikeBGoodsDetailDO.setCreateTime(time);
            ailikeBGoodsDetailDO.setUpdateTime(time);

            val subOrderAmountInfosDTO = CollectionUtil.findOne(subOrderAmountInfos, p ->
                    StringUtils.equals(p.getSubOrderId(), certificateDTO.getOrderItemId())
            );
            if (ObjectUtil.isNull(subOrderAmountInfosDTO)) {
                LogUtil.warn(log, "VerifyToolServiceImpl.buildGoodDetails >> 子单信息不存在 certificateDTO:{}, subOrderAmountInfos:{}", certificateDTO, subOrderAmountInfos);
            } else {
                val receiptAmount = NumberUtil.centsToYuan(subOrderAmountInfosDTO.getReceiptAmount());
                val originAmount = NumberUtil.centsToYuan(subOrderAmountInfosDTO.getOriginAmount());
                ailikeBGoodsDetailDO.setPrice(receiptAmount);
                ailikeBGoodsDetailDO.setOriginPrice(originAmount);
                if (CollectionUtil.isNotEmpty(subOrderAmountInfosDTO.getDiscounts())) {
                    BigDecimal merchantDiscount = BigDecimal.ZERO;
                    BigDecimal platformDiscount = BigDecimal.ZERO;
                    // 设置商品单的优惠金额
                    for (val p : subOrderAmountInfosDTO.getDiscounts()) {
                        merchantDiscount = merchantDiscount.add(NumberUtil.centsToYuan(p.getMerchantDiscountAmount()));
                        platformDiscount = platformDiscount.add(NumberUtil.centsToYuan(p.getPlatformDiscountAmount()));
                    }
                    ailikeBGoodsDetailDO.setMerchantDiscount(merchantDiscount);
                    ailikeBGoodsDetailDO.setPlatformDiscount(platformDiscount);
                }
            }
            goodsDetailDOList.add(ailikeBGoodsDetailDO);
        }
        return goodsDetailDOList;
    }

    /**
     * 构架订单快照
     *
     * @return
     */
    private List<AilikeBOrderSnapshotDO> buildOrderSnapshot(String orderSn, List<AilikeBGoodsDetailDO> goodsDetails) {
        List<AilikeBOrderSnapshotDO> ailikeBOrderSnapshotDOList = new ArrayList<>();
        AilikeBOrderSnapshotDO orderSnapshot = ailikeBOrderSnapshotDAO.getOrderSnapshot(orderSn);
        if (ObjectUtil.isNotNull(orderSnapshot) || CollectionUtil.isEmpty(goodsDetails)) {
            return ailikeBOrderSnapshotDOList;
        }
        // 增加快照
        for (AilikeBGoodsDetailDO goodsDetail : goodsDetails) {
            // 根据订单号查询商品信息
            AilikeTiktokLifeProductDO snapshotProduct = ailikeTiktokLifeProductDAO.getSnapshotProduct(goodsDetail.getProductId(), null);
            // 小程序商品解析
            ProductSearchParam productSearchParam = new ProductSearchParam();
            productSearchParam.setIsCompress(Boolean.FALSE);
            ProductGetModel productInfo = productStockCommonService.getProductInfo(snapshotProduct, productSearchParam);
            // 构建快照对象
            AilikeBOrderSnapshotDO ailikeBOrderSnapshotDO = new AilikeBOrderSnapshotDO();
            ailikeBOrderSnapshotDO.setOrderSn(orderSn);
            ailikeBOrderSnapshotDO.setGoodsInfo(JSON.toJSONString(productInfo));
            ailikeBOrderSnapshotDO.setStoreInfo(StrUtil.EMPTY);
            ailikeBOrderSnapshotDOList.add(ailikeBOrderSnapshotDO);
        }
        return ailikeBOrderSnapshotDOList;
    }
}
