/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.dao.domain.result.CouponOrderInfoModel;
import com.huike.nova.dao.domain.result.DeveloperRefundSummaryModel;
import com.huike.nova.service.domain.model.mina.order.FindOrderListModel;
import com.huike.nova.service.domain.model.mina.order.FindPoiAreaProvinceModel;
import com.huike.nova.service.domain.model.mina.order.GetMerchantContactModel;
import com.huike.nova.service.domain.model.mina.order.GetPowerLongMemberModel;
import com.huike.nova.service.domain.model.mina.order.MinaOrderGetModel;
import com.huike.nova.service.domain.model.mina.order.MinaRegisterMemberModel;
import com.huike.nova.service.domain.model.mina.order.OrderSnapshotModel;
import com.huike.nova.service.domain.model.mina.order.ProductPoiAreaModel;
import com.huike.nova.service.domain.model.mina.order.TiktokOrderGetModel;
import com.huike.nova.service.domain.param.mina.order.FindOrderListParam;
import com.huike.nova.service.domain.param.mina.order.GetMerchantContactParam;
import com.huike.nova.service.domain.param.mina.order.GetPowerLongMemberParam;
import com.huike.nova.service.domain.param.mina.order.GetStorePoiByAreaParam;
import com.huike.nova.service.domain.param.mina.order.MinaOrderGetParam;
import com.huike.nova.service.domain.param.mina.order.OrderSnapshotParam;
import com.huike.nova.service.domain.param.mina.order.ProductPoiAreaParam;
import com.huike.nova.service.domain.param.mina.order.RegisterMemberParam;
import com.huike.nova.service.domain.param.mina.order.SendQykCaptchaParam;
import com.huike.nova.service.domain.param.mina.order.SendRcvAddressParam;
import com.huike.nova.service.domain.param.mina.order.TiktokOrderGetParam;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version MinaOrderService.java, v 0.1 2022-10-28 4:12 PM ruanzy
 */
public interface MinaOrderService {

    /**
     * 查看订单详情
     *
     * @param param
     * @return
     */
    MinaOrderGetModel getOrderInfo(MinaOrderGetParam param);

    /**
     * 查看抖音订单信息
     *
     * @param param
     * @return
     */
    TiktokOrderGetModel getTiktokOrderInfo(TiktokOrderGetParam param);

    /**
     * 获取订单快照
     *
     * @param param
     * @return OrderSnapshotModel
     */
    OrderSnapshotModel getOrderSnapshot(OrderSnapshotParam param);

    /**
     * 宝龙会员信息查询接口
     *
     * @param param
     * @return
     */
    GetPowerLongMemberModel getPowerLongMember(GetPowerLongMemberParam param);

    /**
     * 预约代金券显示联系商家
     *
     * @param param
     * @return param
     */
    GetMerchantContactModel getMerchantContact(GetMerchantContactParam param);

    /**
     * 注册会员
     *
     * @param param
     * @return
     */
    MinaRegisterMemberModel registerMember(RegisterMemberParam param);

    /**
     * 发送收货信息短信
     *
     * @param param
     */
    void sendRcvAddress(SendRcvAddressParam param);

    /**
     * 宝龙广场地区查询
     *
     * @return
     */
    List<FindPoiAreaProvinceModel> findPoiArea(GetStorePoiByAreaParam param);

    /**
     * 查询商品适用广场的省市
     *
     * @return
     */
    List<ProductPoiAreaModel> findProductPoiArea(ProductPoiAreaParam param);

    /**
     * 权益卡-发送验证码
     *
     * @param param
     */
    void sendQykCaptcha(SendQykCaptchaParam param);

    /**
     * 根据抖音订单号查询
     *
     * @param orderIdList 抖音订单号
     * @return 抖音订单号 <-> 开发者退款参数映射
     */
    Map<String, List<DeveloperRefundSummaryModel>> queryDouyinDeveloperRefundParameters(List<String> orderIdList);

    /**
     * 分页查询订单列表
     *
     * @param param 分页查询参数
     * @return 订单列表分页结果
     */
    PageResult<FindOrderListModel> findOrderList(PageParam<FindOrderListParam> param);

    /**
     * 查询券码信息
     *
     * @param outOrderSn 外部订单信息
     * @return 券码信息
     */
    CouponOrderInfoModel queryCouponOrderInfo(String outOrderSn);
}