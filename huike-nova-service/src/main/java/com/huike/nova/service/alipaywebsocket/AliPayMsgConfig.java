package com.huike.nova.service.alipaywebsocket;

import com.alipay.api.msg.AlipayMsgClient;
import com.alipay.api.msg.MsgHandler;
import com.huike.nova.common.constant.AlipayConstant;
import com.huike.nova.common.enums.AlipayMsgApiEnum;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.TraceIdGenerator;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.entity.AilikeTiktokServiceProviderDO;
import com.huike.nova.dao.repository.AilikeTiktokServiceProviderDAO;
import com.huike.nova.sdk.foundation.ext.UtilsExtKt;
import com.huike.nova.service.business.AliPayOrderService;
import com.huike.nova.service.domain.param.alipay.AliPayMsgParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Configuration
@Slf4j
public class AliPayMsgConfig {

    @Resource
    private AliPayOrderService aliPayOrderService;

    @Resource
    private SysConfig sysConfig;

    @Resource
    private AilikeTiktokServiceProviderDAO ailikeTiktokServiceProviderDAO;

    @Bean
    public AlipayMsgClient alipayMsgClient() throws Exception {
        LogUtil.info(log, "AliPayMsgConfig.alipayMsgClient >> 开始 >> ");
        AilikeTiktokServiceProviderDO tiktokServiceProviderDO = ailikeTiktokServiceProviderDAO.getByAppId(sysConfig.getAlipayGroupPurchaseWanDaAppId());
        if (null == tiktokServiceProviderDO) {
            LogUtil.info(log, "AliPayMsgConfig.alipayMsgClient >> 连接失败 >>");
            return null;
        }
        AlipayMsgClient alipayMsgClient = AlipayMsgClient.getInstance(tiktokServiceProviderDO.getAlipayMinaAppId());
        alipayMsgClient.setConnector(AlipayConstant.ALIPAY_CONNECTOR);
        alipayMsgClient.setSecurityConfig(AlipayConstant.SIGN_TYPE, tiktokServiceProviderDO.getAlipayMinaPrivateKey(), tiktokServiceProviderDO.getAlipayPublicKey());
        alipayMsgClient.setMessageHandler(new MsgHandler() {
            /**
             * 客户端接收到消息后回调此方法
             *  @param  msgApi 接收到的消息的消息api名
             *  @param  msgId 接收到的消息的消息id
             *  @param  bizContent 接收到的消息的内容，json格式
             */
            @Override
            public void onMessage(String msgApi, String msgId, String bizContent) {
                // 设置tradeId
                UtilsExtKt.newTraceId();
                LogUtil.info(log, "AliPayMsgConfig.onMessage >> 接受到消息 >> msgApi：{}，msgId：{}，bizContent：{}", msgApi, msgId, bizContent);
                if (StringUtils.isBlank(msgApi)) {
                    return;
                }
                AlipayMsgApiEnum msgApiEnum = AlipayMsgApiEnum.getByValue(msgApi);
                if (null == msgApiEnum) {
                    return;
                }
                AliPayMsgParam msgParam = new AliPayMsgParam(msgApi, msgId, bizContent, tiktokServiceProviderDO.getAlipayMinaAppId());
                switch (msgApiEnum) {
                    case ORDER_RESULT_NOTIFICATION:
                        aliPayOrderService.orderResultNotification(msgParam);
                        break;
                    case CERTIFICATE_MSG_NOTIFICATION:
                        aliPayOrderService.certificateMsgNotification(msgParam);
                        break;
                    default:
                        LogUtil.info(log, "AliPayMsgConfig.onMessage >> 无监测通知 >> msgApi = {}", msgApi);
                }
            }
        });
        alipayMsgClient.connect();
        return alipayMsgClient;
    }

    @Bean
    public AlipayMsgClient haiGuangGoAlipayMsgClient() throws Exception {
        LogUtil.info(log, "AliPayMsgConfig.haiGuangGoAlipayMsgClient >> 开始 >> ");
        AilikeTiktokServiceProviderDO tiktokServiceProviderDO = ailikeTiktokServiceProviderDAO.getByAppId(sysConfig.getAlipayGroupPurchaseHaiGuangGoAppid());
        if (null == tiktokServiceProviderDO) {
            LogUtil.info(log, "AliPayMsgConfig.haiGuangGoAlipayMsgClient >> 连接失败 >>");
            return null;
        }
        AlipayMsgClient alipayMsgClient = AlipayMsgClient.getInstance(tiktokServiceProviderDO.getAlipayMinaAppId());
        alipayMsgClient.setConnector(AlipayConstant.ALIPAY_CONNECTOR);
        alipayMsgClient.setSecurityConfig(AlipayConstant.SIGN_TYPE, tiktokServiceProviderDO.getAlipayMinaPrivateKey(), tiktokServiceProviderDO.getAlipayPublicKey());
        alipayMsgClient.setMessageHandler(new MsgHandler() {
            /**
             * 客户端接收到消息后回调此方法
             *  @param  msgApi 接收到的消息的消息api名
             *  @param  msgId 接收到的消息的消息id
             *  @param  bizContent 接收到的消息的内容，json格式
             */
            @Override
            public void onMessage(String msgApi, String msgId, String bizContent) {
                // 设置tradeId
                UtilsExtKt.newTraceId();
                LogUtil.info(log, "AliPayMsgConfig.onMessage >> 接受到消息 >> msgApi：{}，msgId：{}，bizContent：{}", msgApi, msgId, bizContent);
                if (StringUtils.isBlank(msgApi)) {
                    return;
                }
                AlipayMsgApiEnum msgApiEnum = AlipayMsgApiEnum.getByValue(msgApi);
                if (null == msgApiEnum) {
                    return;
                }
                AliPayMsgParam msgParam = new AliPayMsgParam(msgApi, msgId, bizContent, tiktokServiceProviderDO.getAlipayMinaAppId());
                switch (msgApiEnum) {
                    case ORDER_RESULT_NOTIFICATION:
                        aliPayOrderService.orderResultNotification(msgParam);
                        break;
                    case CERTIFICATE_MSG_NOTIFICATION:
                        aliPayOrderService.certificateMsgNotification(msgParam);
                        break;
                    default:
                        LogUtil.info(log, "AliPayMsgConfig.onMessage >> 无监测通知 >> msgApi = {}", msgApi);
                }
            }
        });
        alipayMsgClient.connect();
        return alipayMsgClient;
    }

//    @Bean
    public AlipayMsgClient chengDuHuaQiaoAlipayMsgClient() throws Exception {
        LogUtil.info(log, "AliPayMsgConfig.chengDuHuaQiaoAlipayMsgClient >> 开始 >> ");
        AilikeTiktokServiceProviderDO tiktokServiceProviderDO = ailikeTiktokServiceProviderDAO.getByAppId(sysConfig.getAlipayGroupPurchaseChengDuHuaQiaoAppid());
        if (null == tiktokServiceProviderDO) {
            LogUtil.info(log, "AliPayMsgConfig.chengDuHuaQiaoAlipayMsgClient >> 连接失败 >>");
            return null;
        }
        AlipayMsgClient alipayMsgClient = AlipayMsgClient.getInstance(tiktokServiceProviderDO.getAlipayMinaAppId());
        alipayMsgClient.setConnector(AlipayConstant.ALIPAY_CONNECTOR);
        alipayMsgClient.setSecurityConfig(AlipayConstant.SIGN_TYPE, tiktokServiceProviderDO.getAlipayMinaPrivateKey(), tiktokServiceProviderDO.getAlipayPublicKey());
        alipayMsgClient.setMessageHandler(new MsgHandler() {
            /**
             * 客户端接收到消息后回调此方法
             *  @param  msgApi 接收到的消息的消息api名
             *  @param  msgId 接收到的消息的消息id
             *  @param  bizContent 接收到的消息的内容，json格式
             */
            @Override
            public void onMessage(String msgApi, String msgId, String bizContent) {
                // 设置tradeId
                String traceId = TraceIdGenerator.generate();
                MDC.put("TRACE_ID", traceId);
                LogUtil.info(log, "AliPayMsgConfig.chengDuHuaQiaoAlipayMsgClient >> 接受到消息 >> msgApi：{}，msgId：{}，bizContent：{}", msgApi, msgId, bizContent);
                if (StringUtils.isBlank(msgApi)) {
                    return;
                }
                AlipayMsgApiEnum msgApiEnum = AlipayMsgApiEnum.getByValue(msgApi);
                if (null == msgApiEnum) {
                    return;
                }
                AliPayMsgParam msgParam = new AliPayMsgParam(msgApi, msgId, bizContent, tiktokServiceProviderDO.getAlipayMinaAppId());
                switch (msgApiEnum) {
                    case ORDER_RESULT_NOTIFICATION:
                        aliPayOrderService.orderResultNotification(msgParam);
                        break;
                    case CERTIFICATE_MSG_NOTIFICATION:
                        aliPayOrderService.certificateMsgNotification(msgParam);
                        break;
                    default:
                        LogUtil.info(log, "AliPayMsgConfig.chengDuHuaQiaoAlipayMsgClient >> 无监测通知 >> msgApi = {}", msgApi);
                }
            }
        });
        alipayMsgClient.connect();
        return alipayMsgClient;
    }

//    @Bean
    public AlipayMsgClient jiNanHuaShanAlipayMsgClient() throws Exception {
        LogUtil.info(log, "AliPayMsgConfig.jiNanHuaShanAlipayMsgClient >> 开始 >> ");
        AilikeTiktokServiceProviderDO tiktokServiceProviderDO = ailikeTiktokServiceProviderDAO.getByAppId(sysConfig.getAlipayGroupPurchaseJiNanHuaShanAppid());
        if (null == tiktokServiceProviderDO) {
            LogUtil.info(log, "AliPayMsgConfig.jiNanHuaShanAlipayMsgClient >> 连接失败 >>");
            return null;
        }
        AlipayMsgClient alipayMsgClient = AlipayMsgClient.getInstance(tiktokServiceProviderDO.getAlipayMinaAppId());
        alipayMsgClient.setConnector(AlipayConstant.ALIPAY_CONNECTOR);
        alipayMsgClient.setSecurityConfig(AlipayConstant.SIGN_TYPE, tiktokServiceProviderDO.getAlipayMinaPrivateKey(), tiktokServiceProviderDO.getAlipayPublicKey());
        alipayMsgClient.setMessageHandler(new MsgHandler() {
            /**
             * 客户端接收到消息后回调此方法
             *  @param  msgApi 接收到的消息的消息api名
             *  @param  msgId 接收到的消息的消息id
             *  @param  bizContent 接收到的消息的内容，json格式
             */
            @Override
            public void onMessage(String msgApi, String msgId, String bizContent) {
                // 设置tradeId
                String traceId = TraceIdGenerator.generate();
                MDC.put("TRACE_ID", traceId);
                LogUtil.info(log, "AliPayMsgConfig.jiNanHuaShanAlipayMsgClient >> 接受到消息 >> msgApi：{}，msgId：{}，bizContent：{}", msgApi, msgId, bizContent);
                if (StringUtils.isBlank(msgApi)) {
                    return;
                }
                AlipayMsgApiEnum msgApiEnum = AlipayMsgApiEnum.getByValue(msgApi);
                if (null == msgApiEnum) {
                    return;
                }
                AliPayMsgParam msgParam = new AliPayMsgParam(msgApi, msgId, bizContent, tiktokServiceProviderDO.getAlipayMinaAppId());
                switch (msgApiEnum) {
                    case ORDER_RESULT_NOTIFICATION:
                        aliPayOrderService.orderResultNotification(msgParam);
                        break;
                    case CERTIFICATE_MSG_NOTIFICATION:
                        aliPayOrderService.certificateMsgNotification(msgParam);
                        break;
                    default:
                        LogUtil.info(log, "AliPayMsgConfig.jiNanHuaShanAlipayMsgClient >> 无监测通知 >> msgApi = {}", msgApi);
                }
            }
        });
        alipayMsgClient.connect();
        return alipayMsgClient;
    }


}
