package com.huike.nova.service.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.huike.nova.common.util.EmojiUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.dao.entity.PrinterLogDO;
import com.huike.nova.dao.repository.PrinterLogDAO;
import com.huike.nova.service.business.BsjOpenService;
import com.huike.nova.service.domain.model.printer.BsjCustomPrintResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 打印通知消息消费
 *
 * <AUTHOR>
 * @version BsjPrinteringListener.java, v 0.1 2019-06-18 11:27 ymm
 */
@Slf4j
@Component
public class BsjPrinteringListener implements MessageListener {

    public static final String REDIS_KEY_PREFIX = "bsj_print_cosumer_";

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PrinterLogDAO printerLogDAO;

    @Autowired
    private BsjOpenService bsjOpenService;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        LogUtil.info(log, "博实结打印处理 >> 消息消费开始···");
        String jsonMessage = null;
        jsonMessage = new String(message.getBody(), StandardCharsets.UTF_8);
        if (StringUtils.isEmpty(jsonMessage)) {
            LogUtil.info(log, "博实结打印处理:接收到的消息为空！message={}", message);
            return Action.CommitMessage;
        }
        LogUtil.info(log, "bsjPrinteringListener >>> 博实结打印处理:接收到的消息 >> jsonMessage={}", jsonMessage);
        Map<String, Object> map = new HashMap<>();
        try {
            map = JSONObject.parseObject(jsonMessage);
            LogUtil.info(log, "bsjPrinteringListener >>> 博实结打印处理 >> map={}", JSONObject.toJSONString(map));
            if (map == null || map.isEmpty()) {
                return Action.CommitMessage;
            }
        } catch (Exception e) {
            // 转换异常 直接消费成功
            return Action.CommitMessage;
        }
        String key1 = message.getMsgID();
        String key2 = (String) map.get("deviceSnCode");
        //利用订单号+设备号做消息队列幂等
        Boolean ifAbsent = redissonClient.getBucket(REDIS_KEY_PREFIX + key1 + key2).isExists();
        LogUtil.info(log, "bsjPrinteringListener >>> 支付后推送:是否重复打印！orderSn={} ifAbsent={}", REDIS_KEY_PREFIX + key1 + key2, ifAbsent);
        redissonClient.getBucket(REDIS_KEY_PREFIX + key1 + key2).set("1", 1, TimeUnit.MINUTES);
        if (ifAbsent) {
            LogUtil.info(log, "bsjPrinteringListener >>> 支付后推送:q订单重复消费，不进行推送！orderSn={}", REDIS_KEY_PREFIX + key1 + key2);
            return Action.CommitMessage;
        }
        try {
            // 设备sn码
            String deviceSnCode = (String) map.get("deviceSnCode");
            // 点餐订单号
            String dishOrder = (String) map.get("orderSn");
            // 打印内容
            String content = (String) map.get("content");
            // 门店id tp_lifecircle_store表的store_id
            String storeId = (String) map.get("storeId");
            // 打印联数
            Integer cn = (Integer) map.get("cn");
            LogUtil.info(log, "BsjPrinteringListener >> 博实结打印小票入参 orderSn = {}, deviceSnCode = {}, content = {}", dishOrder, deviceSnCode, content);
            //去除无打印无播报
            if (StrUtil.isBlank(content)) {
                return Action.CommitMessage;
            }

            // 去除 emoji 表情
            String replaceEmoji = EmojiUtil.replaceEmoji(content, "!");

            BsjCustomPrintResult customPrint = bsjOpenService.customPrint(deviceSnCode, cn, replaceEmoji, "", true);

            //添加日志
            PrinterLogDO logDO = new PrinterLogDO();
            logDO.setEquipmentSn(deviceSnCode);
            logDO.setOrderSn(dishOrder);
            logDO.setResultStatus(1);
            logDO.setStoreId(storeId);
            if (Objects.isNull(customPrint)) {
                logDO.setResultStatus(2);
                LogUtil.error(log, "BsjPrinteringListener >> 博实结打印小票失败 orderSn = {}, deviceSnCode = {}, storeId = {}", dishOrder, deviceSnCode, storeId);
            }
            if (!customPrint.getStatus().equals(Integer.valueOf(1))) {
                logDO.setResultStatus(2);
                LogUtil.error(log, "BsjPrinteringListener >> 博实结打印小票失败 orderSn = {}, deviceSnCode = {}, printResult = {}, storeId = {}", dishOrder, deviceSnCode, JSONObject.toJSONString(customPrint), storeId);
            }
            printerLogDAO.save(logDO);
        } catch (Exception e) {
            LogUtil.error(log, "BsjPrinteringListener >> 博实结打印小票异常", e);
        }

        // 删除缓存的值
        redissonClient.getBucket(REDIS_KEY_PREFIX + key1 + key2).delete();
        //推送失败不会进行重发推送！
        return Action.CommitMessage;
    }
}