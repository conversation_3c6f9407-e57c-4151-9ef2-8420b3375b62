/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.common.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.github.rholder.retry.Retryer;
import com.huike.nova.common.constant.AsyncThreadConstant;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.enums.ChannelTypeEnum;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.ProductCouponTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.UrlEncodeUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.entity.AilikeBGoodsDetailDO;
import com.huike.nova.dao.entity.AilikeBOrderDO;
import com.huike.nova.dao.entity.AilikeTiktokLifeProductDO;
import com.huike.nova.dao.entity.AilikeTiktokLifeProductStoreDO;
import com.huike.nova.dao.entity.PoiAreaDO;
import com.huike.nova.dao.entity.PowerLongLinkDO;
import com.huike.nova.dao.entity.TiktokLifeProductPoiDO;
import com.huike.nova.dao.repository.AilikeBGoodsDetailDAO;
import com.huike.nova.dao.repository.AilikeTiktokLifeProductDAO;
import com.huike.nova.dao.repository.AilikeTiktokLifeProductStoreDAO;
import com.huike.nova.dao.repository.PoiAreaDAO;
import com.huike.nova.dao.repository.PowerLongLinkDAO;
import com.huike.nova.dao.repository.TiktokLifeProductPoiDAO;
import com.huike.nova.service.business.common.PowerLongSendSmsService;
import com.huike.nova.service.business.open.PowerLongOpenService;
import com.huike.nova.service.enums.open.SendRcvAddressTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version PowerLongSendSmsServiceImpl.java, v 0.1 2023-09-01 10:48 AM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class PowerLongSendSmsServiceImpl implements PowerLongSendSmsService {

    private RedissonClient redissonClient;

    private PowerLongOpenService powerLongOpenService;

    private AilikeBGoodsDetailDAO ailikeBGoodsDetailDAO;

    private AilikeTiktokLifeProductDAO ailikeTiktokLifeProductDAO;

    private AilikeTiktokLifeProductStoreDAO ailikeTiktokLifeProductStoreDAO;

    private TiktokLifeProductPoiDAO tiktokLifeProductPoiDAO;

    private PowerLongLinkDAO powerLongLinkDAO;

    private SysConfig sysConfig;

    private PoiAreaDAO poiAreaDAO;

    @Qualifier("exceptionRetryer")
    private Retryer<Object> exceptionRetryer;

    /**
     * 宝龙发送发货短信
     *
     * @param orderInfo
     */
    @Async(AsyncThreadConstant.SYNC_PLAY_COUNT_EXECUTOR)
    @Override
    public void sendPowerLongSms(AilikeBOrderDO orderInfo) {
        RLock lock = redissonClient.getLock(StrUtil.format(RedisPrefixConstant.POWER_LONG_LOCK_SERVING_SEND_SMS_KEY, orderInfo.getOutOrderSn()));
        try {
            if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                LogUtil.error(log, "PowerLongSendSmsServiceImpl" + "." + "sendPowerLongSms" + " >>>>> " + "锁获取失败");
                throw new CommonException(ErrorCodeEnum.GET_LOCK_ERROR).detailMessage("PowerLongSendSmsServiceImpl" + " >>>>> " + "sendPowerLongSms" + "  锁获取失败");
            }
            // 查询商品信息，不是预约代金券，不需要发送短信
            List<AilikeBGoodsDetailDO> goodsDetailList = ailikeBGoodsDetailDAO.findGoodsDetailList(orderInfo.getOrderSn());
            if (CollectionUtil.isEmpty(goodsDetailList)) {
                throw new CommonException(ErrorCodeEnum.ORDER_ERROR).detailMessage("商品详情查询异常");
            }
            String productId = goodsDetailList.get(0).getProductId();
            //根据抖音商品ID查询商品信息
            AilikeTiktokLifeProductDO productDetail = ailikeTiktokLifeProductDAO.getSnapshotProduct(productId, ChannelTypeEnum.TIKTOK.getProductBizLine());
            if (Objects.isNull(productDetail)) {
                throw new CommonException(ErrorCodeEnum.ORDER_ERROR).detailMessage("商品信息查询异常");
            }
            if (!ProductCouponTypeEnum.APPOINTMENT.getValue().equals(productDetail.getCouponType())) {
                return;
            }
            String businessProductId = productDetail.getBusinessProductId();
            // 查询预约代金券的默认广场
//            List<AilikeTiktokLifeProductStoreDO> storeList = ailikeTiktokLifeProductStoreDAO.getStoreList(businessProductId, null);
            List<AilikeTiktokLifeProductStoreDO> storeList = ailikeTiktokLifeProductStoreDAO.getStoreListSkipPoi(businessProductId, sysConfig.getMinaProductPoiSkipList());
            if (CollectionUtil.isEmpty(storeList)) {
                throw new CommonException(ErrorCodeEnum.ORDER_ERROR).detailMessage("预约代金券的默认广场异常");
            }
            // 生成短链，数据库存储
            String purchasePhone = orderInfo.getPurchasePhone();
            String outOrderSn = orderInfo.getOutOrderSn();
            String url = StrUtil.format(sysConfig.getPowerLongSmsLongLink(), purchasePhone, outOrderSn, UrlEncodeUtil.urlEncode(storeList.get(0).getStoreName()), productId);
            // 长链变短链逻辑处理
            String shortUrl = this.savePowerLongLink(url, outOrderSn);
            // 获取项目号
            List<TiktokLifeProductPoiDO> poiDOS = tiktokLifeProductPoiDAO.queryByBusinessProductId(businessProductId);
            if (CollectionUtil.isEmpty(poiDOS)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("广场信息获取失败");
            }
            PoiAreaDO poiAreaInfo = poiAreaDAO.getPoiAreaInfo(poiDOS.get(0).getPoiId());
            if (Objects.isNull(poiAreaInfo)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("广场信息获取失败");
            }
            // 宝龙悠悠发送短信
            powerLongOpenService.sendRcvAddress(purchasePhone, sysConfig.getPowerLongSmsShortLink() + shortUrl, SendRcvAddressTypeEnum.PAYMENT_SUCCESS.getValue(), poiAreaInfo.getProjectNo());
        } catch (CommonException commonException) {
            throw commonException;
        } catch (Exception e) {
            LogUtil.error(log, "PowerLongSendSmsServiceImpl" + "." + "sendPowerLongSms" + " >>>>> " + "执行加锁方法失败", e);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("PowerLongSendSmsServiceImpl" + " >>>>> " + "sendPowerLongSms" + "  执行加锁方法失败");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                LogUtil.error(log, "PowerLongSendSmsServiceImpl" + " >>>>> " + "sendPowerLongSms" + "  锁释放失败", e);
            }
        }
    }

    /**
     * @param url
     * @param outOrderSn
     * @return
     */
    private String savePowerLongLink(String url, String outOrderSn) {
        // 查看数据库是否存在
        String shortLink;
        try {
            shortLink = (String) exceptionRetryer.call(() -> getShortUrl(url, outOrderSn));
        } catch (Exception e) {
            LogUtil.warn(log, "PowerLongSendSmsServiceImpl.savePowerLongLink >>> 短链重试生成异常,url={},outOrderSn={}", e, url, outOrderSn);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("短链重试生成异常");
        }
        PowerLongLinkDO powerLongLinkDO = new PowerLongLinkDO();
        powerLongLinkDO.setOutOrderSn(outOrderSn);
        powerLongLinkDO.setLongUrl(url);
        powerLongLinkDO.setShortUrl(shortLink);
        powerLongLinkDAO.savePowerLongLink(powerLongLinkDO);
        return shortLink;
    }

    /**
     * 校验短链是否重复
     *
     * @param url
     * @param outOrderSn
     * @return
     */
    private String getShortUrl(String url, String outOrderSn) {
        String shortLink = this.generateShortLink(url, outOrderSn);
        if (StringUtils.isBlank(shortLink)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("短链为空");
        }
        // 查询数据库是否存在数据
        PowerLongLinkDO shortUrl = powerLongLinkDAO.getLinkInfoByShortUrl(shortLink);
        if (Objects.nonNull(shortUrl)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("短链已经存在");
        }
        return shortLink;
    }

    /**
     * 生成短链接
     *
     * @param url
     * @param outOrderSn
     * @return
     */
    private String generateShortLink(String url, String outOrderSn) {
        String input = url + outOrderSn;
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = md.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.substring(0, 12);
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }
}