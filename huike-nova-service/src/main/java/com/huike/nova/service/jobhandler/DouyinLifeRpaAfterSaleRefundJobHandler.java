package com.huike.nova.service.jobhandler;

import com.huike.nova.service.business.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 抖音售后单退款同步
 *
 * <AUTHOR> (<EMAIL>)
 * @version DouyinLifeRpaAfterSaleRefundJobHandler.java, v1.0 2025-01-21 10:32 John Exp$
 */
@Component
@Slf4j
@JobHandler("douyinLifeRpaAfterSaleRefundJobHandler")
@AllArgsConstructor
public class DouyinLifeRpaAfterSaleRefundJobHandler extends AbsJobHandler {

    private TaskService taskService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        taskService.handleDouyinLifeAfterSaleRefundApply();
        return ReturnT.SUCCESS;
    }
}
