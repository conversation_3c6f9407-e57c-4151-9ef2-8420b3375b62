/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.listener.gpt;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.annimon.stream.function.Consumer;
import com.huike.nova.service.enums.GPTServiceProviderEnum;
import com.unfbx.chatgpt.entity.chat.ChatChoice;
import com.unfbx.chatgpt.entity.chat.ChatCompletionResponse;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * OpenAi SSE流式请求事件监听
 *
 * <AUTHOR>
 * @version OpenAiSseEventSourceListener.java, v 0.1 2023-10-18 3:51 PM ruanzy
 */
@Slf4j
@Getter
public class OpenAiSseEventSourceListener2 extends AbsGptSseEventSourceListener {
    public OpenAiSseEventSourceListener2(GPTServiceProviderEnum gptServiceProvider, SseEmitter sseEmitter, String contentId, String accountId, RAtomicLong atomicLong) {
        super(gptServiceProvider, sseEmitter, contentId, accountId, atomicLong);
    }

    @Override
    @SneakyThrows
    protected boolean processContent(String data, Consumer<String> processingDataHandler) {
        if (StringUtils.equalsIgnoreCase(DONE, data)) {
            return false;
        }
        // 读取Json
        val completionResponse = JSONObject.parseObject(data, ChatCompletionResponse.class);
        ChatChoice chatChoice = CollectionUtil.getFirst(completionResponse.getChoices());
        // 判断是否结束：是否包含结束标志
        if (StringUtils.equalsIgnoreCase(DONE, data)) {
            return true;
        }
        val isFinished = StringUtils.equalsIgnoreCase(STOP, chatChoice.getFinishReason());
        if (!isFinished) {
            val content = removeCRLF(chatChoice.getDelta().getContent());
            processingDataHandler.accept(content);
        }
        return isFinished;
    }
}