/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.mina;

import com.huike.nova.service.domain.model.login.FindProtocolListModel;
import com.huike.nova.service.domain.model.startask.mina.login.GetLoginInfoModel;
import com.huike.nova.service.domain.model.startask.mina.login.StarTaskMinaLoginModel;
import com.huike.nova.service.domain.param.startask.mina.login.ConfirmIdentityParam;
import com.huike.nova.service.domain.param.startask.mina.login.GetLoginInfoParam;
import com.huike.nova.service.domain.param.startask.mina.login.RegisterInvitedUserParam;
import com.huike.nova.service.domain.param.startask.mina.login.StarTaskMinaLoginParam;
import com.huike.nova.service.domain.param.startask.mina.login.StarTaskSendCaptchaParam;

/**
 * <AUTHOR>
 * @version StarTaskMinaLoginService.java, v 0.1 2023-11-24 11:54 AM ruanzy
 */
public interface StarTaskMinaLoginService {

    /**
     * 登录-验证码登录/快捷登录
     *
     * @param param
     * @return
     */
    StarTaskMinaLoginModel login(StarTaskMinaLoginParam param);

    /**
     * 发送验证码
     *
     * @param param
     */
    void sendCaptcha(StarTaskSendCaptchaParam param);

    /**
     * 确认身份选择并登录
     *
     * @param param
     * @return
     */
    StarTaskMinaLoginModel confirmIdentity(ConfirmIdentityParam param);

    /**
     * 获取协议列表
     *
     * @return
     */
    FindProtocolListModel findProtocolList();

    /**
     * 退出登录
     */
    void logout();

    /**
     * 登录页获取邀请人信息
     *
     * @param param
     * @return
     */
    GetLoginInfoModel getLoginInfo(GetLoginInfoParam param);

    /**
     * 注册邀请用户
     *
     * @param param
     * @return
     */
    StarTaskMinaLoginModel registerInvitedUser(RegisterInvitedUserParam param);
}