/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.dao.entity.AilikeBCustomerDO;
import com.huike.nova.service.domain.model.mina.customer.MinaLoginModel;
import com.huike.nova.service.domain.model.mina.customer.PhoneDecryptModel;
import com.huike.nova.service.domain.model.qyk.mina.login.QykMinaLoginModel;
import com.huike.nova.service.domain.param.mina.customer.MinaLoginParam;
import com.huike.nova.service.domain.param.mina.customer.PhoneDecryptParam;

/**
 * <AUTHOR>
 * @version MinaCustomerService.java, v 0.1 2022-10-22 5:57 PM ruanzy
 */
public interface MinaCustomerService {

    /**
     * 小程序登录
     *
     * @param param
     * @return
     */
    MinaLoginModel login(MinaLoginParam param);

    /**
     * 手机号解密
     *
     * @param param
     * @return
     */
    PhoneDecryptModel decryptPhone(PhoneDecryptParam param);

    /**
     * 根据CustomerId获得顾客Id
     *
     * @param customerId 顾客Id
     * @return
     */
    String getOpenIdByCustomerId(String customerId);

    /**
     * 获得当前登录态的顾客信息
     *
     * @param loginModel 登录态信息
     * @return 顾客信息
     */
    AilikeBCustomerDO getCurrentLoginCustomer(QykMinaLoginModel loginModel);
}