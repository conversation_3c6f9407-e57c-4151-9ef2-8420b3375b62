package com.huike.nova.service.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.common.ResultCallback;
import com.alibaba.fastjson.JSONObject;
import com.huike.nova.common.util.LogUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import javax.annotation.Nonnull;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 阿里云灵积模型GPT SSE适配器
 *
 * <AUTHOR> (<EMAIL>)
 * @version AliyunDashScopeEventSourceCompatibleListener.java, v1.0 11/28/2023 15:11 John Exp$
 */
@Slf4j
@Data
public class AliyunDashScopeSseEventSourceCompatibleListener extends ResultCallback<GenerationResult> {
    @Override
    public void onEvent(GenerationResult message) {

    }

    @Override
    public void onComplete() {

    }

    @Override
    public void onError(Exception e) {

    }

//    public AliyunDashScopeSseEventSourceCompatibleListener(@Nonnull OpenAiSseEventSourceListener listener) {
//        this.listener = listener;
//    }
//
//    /**
//     * OpenAiSseEventSourceListener对象
//     */
//    private final OpenAiSseEventSourceListener listener;
//
//    private StringBuilder fullContent = new StringBuilder();
//
//    private AtomicInteger inputTokens = new AtomicInteger(0);
//
//    private AtomicInteger outputTokens = new AtomicInteger(0);
//
//    @Override
//    public void onEvent(GenerationResult message) {
////        LogUtil.info(log, "onEvent >> SSE");
//        val requestId = message.getRequestId();
//        val usage  = message.getUsage();
//        if (Objects.nonNull(usage)) {
//            inputTokens.getAndAdd(usage.getInputTokens());
//            outputTokens.getAndAdd(usage.getOutputTokens());
//        }
//        val output = message.getOutput();
//        if (Objects.nonNull(output)) {
//            if (CollectionUtil.isNotEmpty(output.getChoices())) {
//                val choice = CollectionUtil.getFirst(output.getChoices());
//                val str = choice.getMessage().getContent();
//                fullContent.append(str);
//
//            }
//        }
//        LogUtil.info(log, "onEvent >> SSE >> {}", JSONObject.toJSONString(output));
//    }
//
//    @Override
//    public void onComplete() {
//        LogUtil.info(log, "onComplete");
//    }
//
//    @Override
//    public void onError(Exception e) {
//        LogUtil.info(log, "onError");
//        listener.onFailure(listener.getEventSource(), e, null);
//    }
}
