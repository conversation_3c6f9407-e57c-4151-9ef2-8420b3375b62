/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.service.domain.model.openapi.SaasTokenInfoModel;

/**
 * <AUTHOR>
 * @version CommonService.java, v 0.1 2022-11-21 19:26 zhangling
 */
public interface CommonService {
    /**
     * 上传excel文件
     *
     * @param fileName
     * @return
     */
    String uploadExcelFile(String fileName);

    /**
     * 获取自增主键
     *
     * @return 自增主键
     */
    String buildIncr();

    /**
     * http转https
     *
     * @param ossUrl
     * @return
     */
    String httpToHttps(String ossUrl);

    /**
     * 解析saas的token信息
     *
     * @return {@link SaasTokenInfoModel}
     * <AUTHOR>
     */
    SaasTokenInfoModel getSaasTokenInfo();
}