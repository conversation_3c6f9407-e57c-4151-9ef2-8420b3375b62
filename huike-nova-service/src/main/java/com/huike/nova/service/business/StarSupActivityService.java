package com.huike.nova.service.business;

import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.dao.entity.AilikeBSupActivityDO;
import com.huike.nova.dao.entity.RpaVideoRecordDO;
import com.huike.nova.service.domain.model.starsupactivity.PageQueryStarSupActivityModel;
import com.huike.nova.service.domain.model.starsupactivity.StarPushRecordListModel;
import com.huike.nova.service.domain.model.starsupactivity.StarSupActivityDetailModel;
import com.huike.nova.service.domain.model.starsupactivity.StarSupActivityVideoDetailModel;
import com.huike.nova.service.domain.model.starsupactivity.SupActivityStarModel;
import com.huike.nova.service.domain.model.supactivity.StarSupActivityConfigModel;
import com.huike.nova.service.domain.model.supactivity.StarSupPublishEstimateModel;
import com.huike.nova.service.domain.param.starsupactivity.AddMaterialAutoPublishParam;
import com.huike.nova.service.domain.param.starsupactivity.AddStarSupActivityParam;
import com.huike.nova.service.domain.param.starsupactivity.PageQueryStarSupActivityParam;
import com.huike.nova.service.domain.param.starsupactivity.StarPushRecordListParam;
import com.huike.nova.service.domain.param.starsupactivity.StarSupActivityConfigParam;
import com.huike.nova.service.domain.param.starsupactivity.StarSupActivityVideoDetailParam;
import com.huike.nova.service.domain.param.starsupactivity.StarSupPublishEstimateParam;
import com.huike.nova.service.domain.param.starsupactivity.SupActivityStarListParam;
import com.huike.nova.service.domain.param.starsupactivity.UpdateStarSupActivityParam;

import javax.annotation.Nonnull;
import java.util.List;

/**
 * 达人推
 *
 * <AUTHOR>
 * @date 2023/2/15 14:54
 * @copyright 2022 barm Inc. All rights reserved
 */
public interface StarSupActivityService {
    /**
     * 新增达人推
     *
     * @param param
     */
    Boolean addStarSupActivity(AddStarSupActivityParam param);

    /**
     * 更新达人推
     *
     * @param param
     */
    void updateStarSupActivity(UpdateStarSupActivityParam param);

    /**
     * 重新发送失败的RPA记录
     *
     * @param merchantId    商户Id
     * @param supActivityDO 达人推活动
     * @param param         RPA重发记录
     */
    void retrySendRpaVideoLists(String merchantId, AilikeBSupActivityDO supActivityDO, List<RpaVideoRecordDO> rpaVideoRecordList);

    /**
     * 素材不足的达人推活动补充素材尝试发布
     *
     * @param addMaterialAutoPublishParam
     */
    void addMaterialAutoPublish(AddMaterialAutoPublishParam addMaterialAutoPublishParam);

    /**
     * 素材不足的达人推活动补充素材尝试发布（异步处理）
     *
     * @param param
     */
    void syncSupplyMaterial(AddMaterialAutoPublishParam param);

    /**
     * 前置检查素材数量
     *
     * @param param
     */
    void checkMaterialNum(AddStarSupActivityParam param);

    /**
     * 达人推分页列表
     *
     * @param pageParam
     * @return
     */
    PageResult<PageQueryStarSupActivityModel> pageQueryStarSupActivity(PageParam<PageQueryStarSupActivityParam> pageParam);

    /**
     * 获取达人推详情
     *
     * @param supActivityId
     * @param platformType
     * @return
     */
    StarSupActivityDetailModel getSupActivityBySupActivityId(String supActivityId, Integer platformType);

    /**
     * 获取达人发布账号列表
     *
     * @param param
     * @return
     */
    SupActivityStarModel getSupActivityStarListModelList(SupActivityStarListParam param);

    /**
     * 分页查询达人推活动达人参与明细
     *
     * @param param
     * @return
     */
    PageResult<StarSupActivityVideoDetailModel> pageStarSupActivityVideoDetail(PageParam<StarSupActivityVideoDetailParam> param);

    /**
     * 达人推-发布记录列表
     *
     * @param param
     * @return
     */
    PageResult<StarPushRecordListModel> getStarPushRecordList(PageParam<StarPushRecordListParam> param);

    /**
     * 达人推-再次发布
     *
     * @param supActivityId
     * @return
     */
    void republish(String supActivityId);

    /**
     * 商户视频剩余配额检查
     *
     * @param merchantId 商户Id
     * @param count      扣减数量
     * @throws CommonException 配额不足则抛出异常，并进行钉钉通知
     */
    void checkVideoPublishLimitationQuota(@Nonnull String merchantId, int count) throws CommonException;

    /**
     * 获取达人推配置
     *
     * @param param 参数
     * @return 配置返回
     */
    StarSupActivityConfigModel getStarSupActivityConfig(StarSupActivityConfigParam param);

    /**
     * 获得达人推预计
     *
     * @param param 参数
     * @return 配置返回
     */
    StarSupPublishEstimateModel getStarSupPublishEstimate(StarSupPublishEstimateParam param);
}
