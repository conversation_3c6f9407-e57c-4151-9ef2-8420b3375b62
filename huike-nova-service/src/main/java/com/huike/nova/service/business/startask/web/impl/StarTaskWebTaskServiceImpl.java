package com.huike.nova.service.business.startask.web.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.constant.startask.StarTaskConstant;
import com.huike.nova.common.constant.startask.mina.StarTaskMinaRedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.PlatformTypeEnum;
import com.huike.nova.common.enums.oem.BooleanEnum;
import com.huike.nova.common.enums.startask.mina.ApplyListStatusEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskTypeEnum;
import com.huike.nova.common.enums.startask.mina.SubscribeMessageTypeEnum;
import com.huike.nova.common.enums.startask.mina.TaskStatusEnum;
import com.huike.nova.common.enums.startask.web.AuditStatuseEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.common.util.ExcelWriterHelper;
import com.huike.nova.common.util.FsDateUtils;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.WebUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.param.startask.ApplyAuditRecordsPageListDTO;
import com.huike.nova.dao.domain.param.startask.WebApplyListParamDTO;
import com.huike.nova.dao.domain.param.startask.WebTaskListParamDTO;
import com.huike.nova.dao.domain.result.startask.ApplyAuditRecordsPageListResultDTO;
import com.huike.nova.dao.domain.result.startask.FindRecordListResultDTO;
import com.huike.nova.dao.domain.result.startask.WebApplyListResultDTO;
import com.huike.nova.dao.domain.result.startask.WebTaskListResultDTO;
import com.huike.nova.dao.entity.StarTaskApplyListDO;
import com.huike.nova.dao.entity.StarTaskCommissionPlanDO;
import com.huike.nova.dao.entity.StarTaskDO;
import com.huike.nova.dao.entity.StarTaskOperatingRecordDO;
import com.huike.nova.dao.entity.StarTaskOperatorDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.StarTaskApplyListAuditRecordDAO;
import com.huike.nova.dao.repository.StarTaskApplyListDAO;
import com.huike.nova.dao.repository.StarTaskCommissionPlanDAO;
import com.huike.nova.dao.repository.StarTaskDAO;
import com.huike.nova.dao.repository.StarTaskOperatingRecordDAO;
import com.huike.nova.dao.repository.StarTaskOperatorDAO;
import com.huike.nova.service.business.CommonService;
import com.huike.nova.service.business.startask.common.StarTaskJobService;
import com.huike.nova.service.business.startask.mina.StarTaskCommonMinaService;
import com.huike.nova.service.business.startask.mina.SubscribeMessageService;
import com.huike.nova.service.business.startask.web.StarTaskWebExportService;
import com.huike.nova.service.business.startask.web.StarTaskWebTaskService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.web.StarTaskWebTaskServiceObjMapper;
import com.huike.nova.service.domain.model.startask.web.login.StarTaskWebLoginModel;
import com.huike.nova.service.domain.model.startask.web.task.ApplyAuditRecordsPageListModel;
import com.huike.nova.service.domain.model.startask.web.task.FindOperationRecordListModel;
import com.huike.nova.service.domain.model.startask.web.task.GetTaskDetailModel;
import com.huike.nova.service.domain.model.startask.web.task.WebApplyListModel;
import com.huike.nova.service.domain.model.startask.web.task.WebCommissionPlanModel;
import com.huike.nova.service.domain.model.startask.web.task.WebTaskListModel;
import com.huike.nova.service.domain.param.startask.web.task.AddOrUpdateOperationRecordParam;
import com.huike.nova.service.domain.param.startask.web.task.ApplyAuditRecordsExcelParam;
import com.huike.nova.service.domain.param.startask.web.task.ApplyAuditRecordsPageListParam;
import com.huike.nova.service.domain.param.startask.web.task.FindOperationRecordListParam;
import com.huike.nova.service.domain.param.startask.web.task.GetTaskDetailParam;
import com.huike.nova.service.domain.param.startask.web.task.ModifyTaskAssigneeParam;
import com.huike.nova.service.domain.param.startask.web.task.OperateTaskParam;
import com.huike.nova.service.domain.param.startask.web.task.TaskApplyStatusOperationParam;
import com.huike.nova.service.domain.param.startask.web.task.WebApplyListParam;
import com.huike.nova.service.domain.param.startask.web.task.WebTaskListParam;
import com.huike.nova.service.domain.result.startask.web.ApplyAuditRecordsPageListResult;
import com.huike.nova.service.domain.result.startask.web.StarTaskApplyListResult;
import com.huike.nova.service.domain.result.startask.web.StarTaskListResult;
import io.reactivex.Observable;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年12月08日 15:22
 */
@Slf4j
@Service
@AllArgsConstructor
public class StarTaskWebTaskServiceImpl implements StarTaskWebTaskService {

    private StarTaskWebTaskServiceObjMapper starTaskWebTaskServiceObjMapper;

    private StarTaskDAO starTaskDAO;

    private StarTaskCommissionPlanDAO starTaskCommissionPlanDAO;

    private StarTaskApplyListDAO starTaskApplyListDAO;

    private StarTaskApplyListAuditRecordDAO starTaskApplyListAuditRecordDAO;

    private StarTaskOperatingRecordDAO starTaskOperatingRecordDAO;

    private StarTaskOperatorDAO starTaskOperatorDao;

    private CommonService commonService;

    private RedissonClient redissonClient;

    private StarTaskWebExportService starTaskWebExportService;

    private StarTaskCommonMinaService starTaskCommonMinaService;

    private SubscribeMessageService subscribeMessageService;

    private StarTaskJobService starTaskJobService;


    private SysConfig sysConfig;
    private static final Long NE = 57600000L;

    /**
     * 任务管理列表
     *
     * @param param 入参
     * @return 出参
     */
    @Override
    public PageResult<WebTaskListModel> list(PageParam<WebTaskListParam> param) {
        // 获取登录信息
        StarTaskWebLoginModel loginBasicInfo = LoginUtil.getWebStarTaskLoginBasicInfo();
        PageParam<WebTaskListParamDTO> pageParam = starTaskWebTaskServiceObjMapper.toWebTaskListParamDTO(param);
        WebTaskListParamDTO query = pageParam.getQuery();
        String phoneNumber = query.getPhoneNumber();
        query.setAppletId(loginBasicInfo.getAppletId());
        if (StringUtils.isNotEmpty(phoneNumber)) {
            query.setPhoneNumber(FieldEncryptUtil.encode(phoneNumber));
        }
        String createEndDate = query.getCreateEndDate();
        String createStartDate = query.getCreateStartDate();
        if (StringUtils.isNotBlank(createEndDate) && StringUtils.isNotBlank(createStartDate)) {
            query.setCreateStartDate(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(createStartDate, DatePattern.NORM_DATE_PATTERN))));
            query.setCreateEndDate(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(createEndDate, DatePattern.NORM_DATE_PATTERN))));
        }
        String auditEndDate = query.getAuditEndDate();
        String auditStartDate = query.getAuditStartDate();
        if (StringUtils.isNotBlank(auditEndDate) && StringUtils.isNotBlank(auditStartDate)) {
            query.setAuditStartDate(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(auditStartDate, DatePattern.NORM_DATE_PATTERN))));
            query.setAuditEndDate(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(auditEndDate, DatePattern.NORM_DATE_PATTERN))));
        }

        Page<WebTaskListResultDTO> activityPage = starTaskDAO.pageList(pageParam);
        List<WebTaskListResultDTO> resultDTOList = activityPage.getRecords();
        if (CollectionUtils.isEmpty(resultDTOList)) {
            return starTaskWebTaskServiceObjMapper.toWebTaskListModel(activityPage);
        }

        List<String> taskIdList = resultDTOList.stream().map(WebTaskListResultDTO::getTaskId).collect(Collectors.toList());
        List<StarTaskApplyListDO> applyList = starTaskApplyListDAO.findApplyListByTaskIdList(taskIdList);
        List<StarTaskCommissionPlanDO> commissionPlanByTaskIds = starTaskCommissionPlanDAO.findCommissionPlanByTaskIdList(taskIdList);
        // 根据taskId查询操作记录数量
        List<StarTaskOperatingRecordDO> starTaskOperatingRecordDOS = starTaskOperatingRecordDAO.findListByTaskId(taskIdList);
        Map<String, List<StarTaskOperatingRecordDO>> recordMap = starTaskOperatingRecordDOS.stream().collect(Collectors.groupingBy(StarTaskOperatingRecordDO::getTaskId));
        // 根据operatorId查询操作人姓名
        List<String> followUpPersonIdList = resultDTOList.stream().map(WebTaskListResultDTO::getFollowUpPersonId).collect(Collectors.toList());
        List<StarTaskOperatorDO> starTaskOperatorDOList = starTaskOperatorDao.findListByOperatorIdList(followUpPersonIdList);
        Map<String, StarTaskOperatorDO> operatorDOMap = starTaskOperatorDOList.stream().collect(Collectors.toMap(StarTaskOperatorDO::getOperatorId, Function.identity(), (v1, v2) -> v2));
        //使用stream根据taskId分组
        Map<String, List<StarTaskApplyListDO>> applyListMap = applyList.stream().collect(Collectors.groupingBy(StarTaskApplyListDO::getTaskId));
        Map<String, List<StarTaskCommissionPlanDO>> commissionPlanMap = commissionPlanByTaskIds.stream().collect(Collectors.groupingBy(StarTaskCommissionPlanDO::getTaskId));
        for (WebTaskListResultDTO record : resultDTOList) {
            String taskId = record.getTaskId();
            // 构建任务的进度数据
            this.buildListTaskProgressQuantity(record, applyListMap.getOrDefault(taskId, Collections.emptyList()), commissionPlanMap.getOrDefault(taskId, Collections.emptyList()));
            record.setStarAreaName(starTaskCommonMinaService.findStarAreaNameList(taskId));
            record.setPhoneNumber(FieldEncryptUtil.decode(record.getPhoneNumber()));
            record.setApplyStartTime(DateUtil.format(record.getApplyStartDate(), FsDateUtils.SIMPLE_DATE_FORMAT));
            record.setApplyEndTime(DateUtil.format(record.getApplyEndDate(), FsDateUtils.SIMPLE_DATE_FORMAT));
            record.setCancelTimeStr(DateUtil.format(record.getCancelTime(), FsDateUtils.SIMPLE_DATETIME_FORMAT));
            // 判断是否提前结束报名任务
            if (BooleanEnum.YES.getValue().equals(record.getAheadEndApplyFlag())) {
                record.setAheadEndApplyTimeStr(DateUtil.format(record.getAheadEndApplyTime(), FsDateUtils.SIMPLE_DATETIME_FORMAT));
            }
            if (record.getCancelTimeStr().contains(CommonConstant.DEFAULT_TIME)) {
                record.setCancelTimeStr(StringUtils.EMPTY);
            }
            String auditTime = record.getAuditTime();
            if (CommonConstant.INITIAL_TIME_STR.equals(auditTime)) {
                record.setAuditTime(StringPool.EMPTY);
            }
            // 记录数量
            List<StarTaskOperatingRecordDO> list = recordMap.getOrDefault(taskId, Lists.newArrayList());
            record.setRecordNumber(list.size());
            // 联系人姓名
            StarTaskOperatorDO operatorDO = operatorDOMap.getOrDefault(record.getFollowUpPersonId(), new StarTaskOperatorDO());
            record.setFollowUpPersonName(operatorDO.getContactName());
        }
        return starTaskWebTaskServiceObjMapper.toWebTaskListModel(activityPage);
    }

    /**
     * 构建任务进度数量
     *
     * @param record         任务信息
     * @param applyList      报名清单信息
     * @param commissionPlan 佣金计划
     */
    private void buildListTaskProgressQuantity(WebTaskListResultDTO record, List<StarTaskApplyListDO> applyList, List<StarTaskCommissionPlanDO> commissionPlan) {
        // 总需求人数
        int totalNeededCount = CommonConstant.ZERO;
        // 报名人数
        int applyNumber = CommonConstant.ZERO;
        // 提交链接数量
        int numberOfSubmittedLinks = CommonConstant.ZERO;
        // 已完成人数
        int finishedNumber = CommonConstant.ZERO;

        for (StarTaskCommissionPlanDO item : commissionPlan) {
            totalNeededCount += item.getNeededCount();
        }
        for (StarTaskApplyListDO item : applyList) {
            Integer applyStatus = item.getApplyStatus();
            if (!ApplyListStatusEnum.getNotApplyCount.contains(applyStatus)) {
                applyNumber += 1;
            }
            if (!ApplyListStatusEnum.CANCELLED.getValue().equals(applyStatus) && !ApplyListStatusEnum.WAITING_SUBMIT.getValue().equals(applyStatus)
                    && !ApplyListStatusEnum.MERCHANT_PRE_AUDIT.getValue().equals(applyStatus) && !ApplyListStatusEnum.MERCHANT_PRE_REFUND.getValue().equals(applyStatus)) {
                numberOfSubmittedLinks += 1;
            }
            if (ApplyListStatusEnum.COMPLETED.getValue().equals(applyStatus) || ApplyListStatusEnum.PLATFORM_COMPLETED.getValue().equals(applyStatus)) {
                finishedNumber += 1;
            }
        }
        record.setTotalApplyNumber(applyList.size());
        record.setTotalNeededCount(totalNeededCount);
        record.setApplyNumber(applyNumber);
        record.setNumberOfSubmittedLinks(numberOfSubmittedLinks);
        record.setFinishedNumber(finishedNumber);
    }

    /**
     * 任务详情
     *
     * @param param 入参
     * @return 出参
     */
    @Override
    public GetTaskDetailModel getTaskDetail(GetTaskDetailParam param) {
        String taskId = param.getTaskId();
        StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(taskId);
        if (null == starTaskDO) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务不存在或错误");
        }
        GetTaskDetailModel model = starTaskWebTaskServiceObjMapper.toGetTaskDetailModel(starTaskDO);
        List<StarTaskCommissionPlanDO> commissionPlanDOList = starTaskCommissionPlanDAO.findCommissionPlanByTaskId(starTaskDO.getTaskId());
        List<WebCommissionPlanModel> commissionPlanModelList = starTaskWebTaskServiceObjMapper.toWebCommissionPlanModelList(commissionPlanDOList);
        model.setCommissionPlanList(commissionPlanModelList);
        if (StringUtils.isNotBlank(model.getStorePhone())) {
            model.setStorePhone(FieldEncryptUtil.decode(model.getStorePhone()));
        }
        model.setStoreAreaName(starTaskDO.getAreaName());
        // 达人区域列表
        model.setStarAreaName(starTaskCommonMinaService.findStarAreaNameList(taskId));
        if (StringUtils.isNotBlank(starTaskDO.getSampleUrl())) {
            model.setSampleUrlList(JSON.parseArray(starTaskDO.getSampleUrl(), String.class));
        }
        return model;
    }

    /**
     * 任务操作
     *
     * @param param 入参
     */
    @Override
    public void operateTask(OperateTaskParam param) {
        // 加锁
        RLock redisLock = redissonClient.getLock(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_STAR_TASK, param.getTaskId()));
        try {
            if (!redisLock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage("操作频繁，请稍后再试");
            }
            Integer operateType = param.getOperateType();
            String taskId = param.getTaskId();
            Integer taskStatus;
            if (CommonConstant.INTEGER_ONE.equals(operateType)) {
                taskStatus = TaskStatusEnum.PLATFORM_REJECTED.getValue();
            } else if (CommonConstant.INTEGER_TWO.equals(operateType)) {
                taskStatus = TaskStatusEnum.IN_PROGRESS.getValue();
            } else {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("操作类型错误");
            }

            StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(taskId);
            if (null == starTaskDO) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务不存在或错误");
            }
            if (!TaskStatusEnum.PLATFORM_UNDER_REVIEW.getValue().equals(starTaskDO.getTaskStatus())) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("状态已变更，请刷新后再试");
            }
            // 获取登录信息
            StarTaskWebLoginModel loginBasicInfo = LoginUtil.getWebStarTaskLoginBasicInfo();
            Date date = new Date();
            starTaskDAO.operateTask(taskId, taskStatus, param.getRejectReason(), loginBasicInfo.getOperatorId(), loginBasicInfo.getContactName(), date);
            List<String> templateDataList = new ArrayList<>();
            templateDataList.add(starTaskDO.getTaskTitle().length() > CommonConstant.INTEGER_TWENTY ? starTaskDO.getTaskTitle().substring(0, CommonConstant.INTEGER_SEVENTEEN) + StringPool.ELLIPSIS : starTaskDO.getTaskTitle());
            templateDataList.add(taskStatus.equals(TaskStatusEnum.PLATFORM_REJECTED.getValue()) ? "驳回" : "通过");
            templateDataList.add(DateUtil.format(date, FsDateUtils.SIMPLE_DATETIME_FORMAT));
            templateDataList.add(taskStatus.equals(TaskStatusEnum.PLATFORM_REJECTED.getValue()) ? (param.getRejectReason().length() > CommonConstant.INTEGER_TWENTY ? param.getRejectReason().substring(0, CommonConstant.INTEGER_SEVENTEEN) + StringPool.ELLIPSIS : param.getRejectReason()) : "您发布的任务已过审，请关注达人报名情况");
            subscribeMessageService.sendSubscribeMessage(starTaskDO.getTaskId(), starTaskDO.getAppletId(), sysConfig.getWxSubscribeMessageTaskAuditResult(), "merchant/taskManagement/index?enterScene=1", templateDataList, SubscribeMessageTypeEnum.SUBMIT_TASK.getValue());
        } catch (Exception e) {
            LogUtil.info(log, "StarTaskWebTaskServiceImpl.operateTask >> 全局异常 >> param = {}", param);
            throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage("操作异常");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (redisLock != null && redisLock.isLocked() && redisLock.isHeldByCurrentThread()) {
                    redisLock.unlock();
                }
            } catch (Exception e) {
                LogUtil.info(log, "StarTaskWebTaskServiceImpl.operateTask >> 锁释放异常 >> param = {}", param);
            }
        }
    }

    /**
     * 报名清单列表
     *
     * @param param 入参
     * @return 出参
     */
    @Override
    public PageResult<WebApplyListModel> applyList(PageParam<WebApplyListParam> param) {
        PageParam<WebApplyListParamDTO> pageParam = starTaskWebTaskServiceObjMapper.toWebApplyListParamDTO(param);
        WebApplyListParamDTO query = pageParam.getQuery();
        String phoneNumber = query.getPhoneNumber();
        if (StringUtils.isNotBlank(phoneNumber)) {
            query.setPhoneNumber(FieldEncryptUtil.encode(phoneNumber));
        }
        Page<WebApplyListResultDTO> activityPage = starTaskApplyListDAO.applyList(pageParam);
        for (WebApplyListResultDTO record : activityPage.getRecords()) {
            record.setPhoneNumber(FieldEncryptUtil.decode(record.getPhoneNumber()));
            String submitTime = record.getSubmitTime();
            if (CommonConstant.INITIAL_TIME_STR.equals(submitTime)) {
                record.setSubmitTime(StringPool.EMPTY);
            }
        }
        return starTaskWebTaskServiceObjMapper.toWebApplyListModel(activityPage);
    }

    /**
     * 报名清单审核记录分页列表
     *
     * @param param 入参
     * @return 出参
     */
    @Override
    public PageResult<ApplyAuditRecordsPageListModel> applyAuditRecordsPageList(PageParam<ApplyAuditRecordsPageListParam> param) {
        ApplyAuditRecordsPageListParam paramQuery = param.getQuery();
        String createStartDate = paramQuery.getAuditStartDate();
        String createEndDate = paramQuery.getAuditEndDate();

        PageParam<ApplyAuditRecordsPageListDTO> pageParam = starTaskWebTaskServiceObjMapper.toPageApplyAuditRecordsPageListDTO(param);
        ApplyAuditRecordsPageListDTO query = pageParam.getQuery();
        if (StringUtils.isNotBlank(createStartDate) && StringUtils.isNotBlank(createEndDate)) {
            query.setCreateStartTime(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(createStartDate, DatePattern.NORM_DATE_PATTERN))));
            query.setCreateEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(createEndDate, DatePattern.NORM_DATE_PATTERN))));
        }
        Page<ApplyAuditRecordsPageListResultDTO> activityPage = starTaskApplyListAuditRecordDAO.applyAuditRecordsPageList(pageParam);
        for (ApplyAuditRecordsPageListResultDTO record : activityPage.getRecords()) {
            record.setPhoneNumber(FieldEncryptUtil.decode(record.getPhoneNumber()));
        }
        return starTaskWebTaskServiceObjMapper.toApplyAuditRecordsPageListModel(activityPage);
    }

    /**
     * 报名清单状态操作
     *
     * @param param 入参
     */
    @Override
    public void applyStatusOperation(TaskApplyStatusOperationParam param) {
        // 加锁
        RLock redisLock = redissonClient.getLock(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_APPLY_LIST, param.getApplyId()));
        try {
            if (!redisLock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage("操作频繁，请稍后再试");
            }
            // 获取登录信息
            StarTaskWebLoginModel loginBasicInfo = LoginUtil.getWebStarTaskLoginBasicInfo();
            starTaskJobService.applyStatusOperation(param, loginBasicInfo.getOperatorId(), loginBasicInfo.getContactName());
        } catch (CommonException e) {
            LogUtil.info(log, "StarTaskWebTaskServiceImpl.applyStatusOperation >> 报名清单状态操作通用异常 >> param = {}", JSON.toJSONString(param));
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage(e.getMsg());
        } catch (Exception e) {
            LogUtil.error(log, "StarTaskWebTaskServiceImpl.applyStatusOperation >> 报名清单状态操作全局异常 >> param = {}", JSON.toJSONString(param));
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("全局异常");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (redisLock != null && redisLock.isLocked() && redisLock.isHeldByCurrentThread()) {
                    redisLock.unlock();
                }
            } catch (Exception e) {
                LogUtil.info(log, "StarTaskWebTaskServiceImpl.applyStatusOperation >> 锁释放异常 >> param = {}", JSON.toJSONString(param));
            }
        }

    }

    /**
     * 新增导出任务列表
     *
     * @param param 入参
     */
    @Override
    public void requestExport(WebTaskListParam param) {
        // 获取登录信息
        StarTaskWebLoginModel loginBasicInfo = LoginUtil.getWebStarTaskLoginBasicInfo();
        val requestParam = starTaskWebTaskServiceObjMapper.toWebTaskListParamDTO(param);
        requestParam.setAppletId(loginBasicInfo.getAppletId());
        if (StringUtils.isNotEmpty(param.getPhoneNumber())) {
            requestParam.setPhoneNumber(FieldEncryptUtil.encode(param.getPhoneNumber()));
        }
        String createEndDate = param.getCreateEndDate();
        String createStartDate = param.getCreateStartDate();
        if (StringUtils.isNotBlank(createEndDate) && StringUtils.isNotBlank(createStartDate)) {
            requestParam.setCreateStartDate(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(createStartDate, DatePattern.NORM_DATE_PATTERN))));
            requestParam.setCreateEndDate(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(createEndDate, DatePattern.NORM_DATE_PATTERN))));
        }
        String auditEndDate = param.getAuditEndDate();
        String auditStartDate = param.getAuditStartDate();
        if (StringUtils.isNotBlank(auditEndDate) && StringUtils.isNotBlank(auditStartDate)) {
            requestParam.setAuditStartDate(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(auditStartDate, DatePattern.NORM_DATE_PATTERN))));
            requestParam.setAuditEndDate(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(auditEndDate, DatePattern.NORM_DATE_PATTERN))));
        }
        starTaskWebExportService.applyExportWebTaskList(requestParam, loginBasicInfo);
    }

    /**
     * 新增导出报名列表
     *
     * @param param 入参
     */
    @Override
    public void requestExportApplyList(WebApplyListParam param) {
        val requestParam = starTaskWebTaskServiceObjMapper.toWebApplyListParamDTO(param);
        if (StringUtils.isNotEmpty(param.getPhoneNumber())) {
            requestParam.setPhoneNumber(FieldEncryptUtil.encode(param.getPhoneNumber()));
        }
        starTaskWebExportService.applyExportWebTaskApplyList(requestParam, null);
    }

    @Override
    public Observable<File> export(String fileName, WebTaskListParamDTO param) {
        return Observable.create(emitter -> {
            try (val writer = ExcelWriterHelper.create(fileName, StarTaskListResult.class)) {
                starTaskDAO.export(param, resultContext -> {
                    val record = resultContext.getResultObject();
                    if (Objects.isNull(record)) {
                        return;
                    }
                    val taskType = StarTaskTypeEnum.getByType(record.getTaskType());
                    val taskStatus = TaskStatusEnum.getByValue(record.getTaskStatus());
                    if (Objects.isNull(taskType) || Objects.isNull(taskStatus)) {
                        return;
                    }
                    val result = new StarTaskListResult()
                            .setCreateTime(record.getCreateTime())
                            .setTaskType(taskType.getDesc())
                            .setTaskName(record.getTaskTitle())
                            .setTaskStatus(taskStatus.getName())
                            .setPublisher(FieldEncryptUtil.decode(record.getPhoneNumber()));
                    // 报名时间
                    val startDate = record.getApplyStartDate();
                    val endDate = record.getApplyEndDate();
                    // 永久有效
                    if (startDate.getTime() <= NE && endDate.getTime() <= NE) {
                        result.setRegistrationTime(StarTaskConstant.PERMANENT_CN);

                        // 特殊情况1
                    } else if (startDate.getTime() <= NE) {
                        result.setRegistrationTime(StarTaskConstant.UP_TO_CN + DateUtil.format(record.getApplyEndDate(), FsDateUtils.SIMPLE_DATE_FORMAT));

                        // 特殊情况2
                    } else if (endDate.getTime() <= NE) {
                        result.setRegistrationTime(StarTaskConstant.BEGINNING_CN + DateUtil.format(record.getApplyStartDate(), FsDateUtils.SIMPLE_DATE_FORMAT));

                        // 起讫时间
                    } else {
                        val applyStartTime = DateUtil.format(record.getApplyStartDate(), FsDateUtils.SIMPLE_DATE_FORMAT);
                        val applyEndTime = DateUtil.format(record.getApplyEndDate(), FsDateUtils.SIMPLE_DATE_FORMAT);
                        result.setRegistrationTime(StrUtil.format("{} 至 {}", applyStartTime, applyEndTime));
                    }
                    // 提前结束报名时间
                    result.setAheadEndApplyTimeStr("-");
                    if (BooleanEnum.YES.getValue().equals(record.getAheadEndApplyFlag())) {
                        result.setAheadEndApplyTimeStr(DateUtil.format(record.getAheadEndApplyTime(), FsDateUtils.SIMPLE_DATETIME_FORMAT));
                    }
                    String taskId = record.getTaskId();
                    //构建进度数据
                    this.buildListTaskProgressQuantity(record, starTaskApplyListDAO.findApplyListByTaskId(taskId), starTaskCommissionPlanDAO.findCommissionPlanByTaskId(taskId));
                    //达人总报名人次
                    result.setTotalApplyNumber(record.getTotalApplyNumber());
                    result.setStarAreaName(String.join(",", starTaskCommonMinaService.findStarAreaNameList(taskId)));
                    result.setTotalNeededCount(record.getTotalNeededCount());
                    result.setApplyNumber(record.getApplyNumber());
                    result.setNumberOfSubmittedLinks(record.getNumberOfSubmittedLinks());
                    result.setFinishedNumber(record.getFinishedNumber());
                    result.setCancelTimeStr(DateUtil.format(record.getCancelTime(), FsDateUtils.SIMPLE_DATETIME_FORMAT));
                    result.setPlatform(PlatformTypeEnum.TIK_TOK.getValue().equals(record.getPlatform()) ? PlatformTypeEnum.TIK_TOK.getName() : PlatformTypeEnum.KS.getName());
                    result.setTotalAmount(record.getTotalAmount());
                    result.setCompleteAmount(record.getCompleteAmount());
                    if (null == result.getCancelTimeStr() || result.getCancelTimeStr().contains(CommonConstant.DEFAULT_TIME)) {
                        result.setCancelTimeStr(StringUtils.EMPTY);
                    }
                    String auditTime = record.getAuditTime();
                    if (CommonConstant.INITIAL_TIME_STR.equals(auditTime)) {
                        result.setAuditTime(StringPool.EMPTY);
                    } else {
                        result.setAuditTime(auditTime);
                    }
                    result.setOperatorName(record.getOperatorName());
                    // 跟进人名称和运营记录
                    val recordContentListData = new StringBuilder();
                    val recordList = starTaskOperatingRecordDAO.findRecordList(taskId);
                    for (FindRecordListResultDTO findRecordListResultDTO : recordList) {
                        // 时间+操作人+内容
                        String time = DateUtil.format(findRecordListResultDTO.getCreateTime(), FsDateUtils.SIMPLE_DATETIME_FORMAT);
                        recordContentListData.append(time)
                                .append(StrUtil.EMPTY)
                                .append(StrPool.BRACKET_START)
                                .append(findRecordListResultDTO.getOperatorName())
                                .append(StrPool.BRACKET_END)
                                .append(StrUtil.EMPTY)
                                .append(findRecordListResultDTO.getRecordContent())
                                .append(StrPool.LF);
                    }
                    val recordContentList = recordContentListData.toString().trim();
                    result.setRecordContentList(recordContentList);
                    val operatorDO = starTaskOperatorDao.getOperatorByOperatorId(record.getFollowUpPersonId());
                    if (Objects.nonNull(operatorDO)) {
                        result.setFollowUpPersonName(operatorDO.getContactName());
                    }
                    writer.write(result);
                });
                emitter.onNext(writer.finish());
                emitter.onComplete();
            } catch (Exception ex) {
                emitter.onError(ex);
            }
        });
    }

    @Override
    public Observable<File> exportApplyList(String fileName, WebApplyListParamDTO param) {
        return Observable.create(emitter -> {
            try (val writer = ExcelWriterHelper.create(fileName, StarTaskApplyListResult.class)) {
                starTaskApplyListDAO.export(param, resultContext -> {
                    if (Objects.isNull(resultContext.getResultObject())) {
                        return;
                    }
                    val obj = resultContext.getResultObject();
                    val result = starTaskWebTaskServiceObjMapper.toStarTaskApplyListResult(obj);
                    result.setPhoneNumber(FieldEncryptUtil.decode(obj.getPhoneNumber()));
                    result.setStarLevelStr("Lv" + ObjectUtil.defaultIfNull(obj.getStarLevel(), CommonConstant.ZERO));
                    val applyStatusEnum = ApplyListStatusEnum.getByValue(obj.getApplyStatus());
                    if (Objects.nonNull(applyStatusEnum)) {
                        result.setApplyStatusStr(applyStatusEnum.getName());
                    }
                    result.setTaskMoney(result.getTaskMoney().setScale(CommonConstant.INTEGER_TWO, BigDecimal.ROUND_HALF_UP));
                    result.setApplyLink(WebUtil.getUrlFromPhrase(StringUtils.defaultString(obj.getApplyLink())));
                    writer.write(result);
                });
                emitter.onNext(writer.finish());
                emitter.onComplete();
            } catch (Exception ex) {
                emitter.onError(ex);
            }
        });
    }

    /**
     * 报名清单审核记录导出
     *
     * @param param 入参
     */
    @Override
    public void applyAuditRecordsExcel(ApplyAuditRecordsExcelParam param) {
        String createStartDate = param.getCreateStartDate();
        String createEndDate = param.getCreateEndDate();
        ApplyAuditRecordsPageListDTO requestParam = starTaskWebTaskServiceObjMapper.toApplyAuditRecordsPageListDTO(param);
        if (StringUtils.isNotBlank(createStartDate) && StringUtils.isNotBlank(createEndDate)) {
            requestParam.setCreateStartTime(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(createStartDate, DatePattern.NORM_DATE_PATTERN))));
            requestParam.setCreateEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(createEndDate, DatePattern.NORM_DATE_PATTERN))));
        }
        starTaskWebExportService.applyAuditRecordsExcel(requestParam);
    }

    /**
     * 报名审核记录导出（异步任务）
     *
     * @param taskName 任务名称
     * @param param    入参
     * @return rxJava Observable对象
     */
    @Override
    public Observable<File> exportApplyAuditRecord(String taskName, ApplyAuditRecordsPageListDTO param) {
        return Observable.create(emitter -> {
            try (val writer = ExcelWriterHelper.create(taskName, ApplyAuditRecordsPageListResult.class)) {
                // 设置读取参数
                starTaskApplyListAuditRecordDAO.export(param, resultContext -> {
                    if (Objects.isNull(resultContext)) {
                        return;
                    }
                    val data = resultContext.getResultObject();
                    ApplyAuditRecordsPageListResult result = starTaskWebTaskServiceObjMapper.toApplyAuditRecordsPageListResult(data);
                    result.setAuditStatus(AuditStatuseEnum.PASS.getValue().equals(data.getAuditStatus()) ? AuditStatuseEnum.PASS.getName() : AuditStatuseEnum.REJECT.getName());
                    String phoneNumber = result.getPhoneNumber();
                    if (StringUtils.isNotBlank(phoneNumber)) {
                        result.setPhoneNumber(FieldEncryptUtil.decode(phoneNumber));
                    }
                    writer.write(result);
                });
                emitter.onNext(writer.finish());
                emitter.onComplete();
            } catch (Exception ex) {
                emitter.onError(ex);
            }
        });
    }

    /**
     * 修改任务跟进人
     *
     * @param param
     */
    @Override
    public void modifyTaskAssignee(ModifyTaskAssigneeParam param) {
        String taskId = param.getTaskId();
        // 获取任务信息
        StarTaskDO taskDO = starTaskDAO.getStarTaskByTaskId(taskId);
        if (Objects.isNull(taskDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务信息不存在");
        }
        // 修改任务跟进人
        starTaskDAO.updateFollowUpPersonId(param.getFollowUpPersonId(), taskId);
    }

    /**
     * 运营记录列表
     *
     * @param param
     * @return
     */
    @Override
    public List<FindOperationRecordListModel> findOperationRecordList(FindOperationRecordListParam param) {
        String taskId = param.getTaskId();
        // 根据任务id查询运营记录列表
        List<FindRecordListResultDTO> recordList = starTaskOperatingRecordDAO.findRecordList(taskId);
        // 获取任务信息
        StarTaskDO taskDO = starTaskDAO.getStarTaskByTaskId(taskId);
        if (Objects.isNull(taskDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务信息不存在");
        }
        // 最后一条默认增加一条任务开始记录
        FindRecordListResultDTO dto = new FindRecordListResultDTO();
        dto.setTaskId(taskId);
        dto.setTaskTitle(taskDO.getTaskTitle());
        dto.setCreateTime(taskDO.getCreateTime());
        recordList.add(dto);
        // 对象转换
        return starTaskWebTaskServiceObjMapper.toFindOperationRecordListModelList(recordList);
    }

    /**
     * 添加/修改运营记录
     *
     * @param param
     */
    @Override
    public void addOrUpdateOperationRecord(AddOrUpdateOperationRecordParam param) {
        String recordId = param.getRecordId();
        String taskId = param.getTaskId();
        String recordContent = param.getRecordContent();
        // 获取任务信息
        StarTaskDO taskDO = starTaskDAO.getStarTaskByTaskId(taskId);
        if (Objects.isNull(taskDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务信息不存在");
        }
        // 登录态获取信息
        StarTaskWebLoginModel loginBasicInfo = LoginUtil.getWebStarTaskLoginBasicInfo();
        String operatorId = loginBasicInfo.getOperatorId();
        // recordId不为空，修改运营记录/为空，新增运营记录
        if (StringUtils.isNotBlank(recordId)) {
            StarTaskOperatingRecordDO record = starTaskOperatingRecordDAO.getRecordByRecordId(recordId);
            if (Objects.isNull(record)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("运营记录为空");
            }
            record.setRecordContent(recordContent);
            record.setOperatorId(operatorId);
            starTaskOperatingRecordDAO.updateRecord(record);
        } else {
            StarTaskOperatingRecordDO recordDO = new StarTaskOperatingRecordDO();
            recordDO.setRecordId(commonService.buildIncr());
            recordDO.setTaskId(taskId);
            recordDO.setRecordContent(recordContent);
            recordDO.setOperatorId(operatorId);
            starTaskOperatingRecordDAO.addRecord(recordDO);
        }
        // 任务表中has_operating_record字段修改
        if (BooleanEnum.NO.getValue().equals(taskDO.getHasOperatingRecord())) {
            starTaskDAO.updateHasOperatingRecord(taskId, BooleanEnum.YES.getValue());
        }
    }
}
