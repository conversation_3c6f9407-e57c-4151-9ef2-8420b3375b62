/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.mina;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.startask.mina.star.AddStarModel;
import com.huike.nova.service.domain.model.startask.mina.star.CheckStarTaskAreaLimitModel;
import com.huike.nova.service.domain.model.startask.mina.star.ConfirmApplyModel;
import com.huike.nova.service.domain.model.startask.mina.star.FindStarGeolocationModel;
import com.huike.nova.service.domain.model.startask.mina.star.FindStarInfoModel;
import com.huike.nova.service.domain.model.startask.mina.star.FindTaskPlazaListModel;
import com.huike.nova.service.domain.model.startask.mina.star.SelectPublishAccountModel;
import com.huike.nova.service.domain.model.startask.mina.star.SquareStarModel;
import com.huike.nova.service.domain.model.startask.mina.star.StarApplyDetailModel;
import com.huike.nova.service.domain.model.startask.mina.star.StarApplyListModel;
import com.huike.nova.service.domain.model.startask.mina.star.StarCountModel;
import com.huike.nova.service.domain.model.startask.mina.star.StarDetailQueryModel;
import com.huike.nova.service.domain.model.startask.mina.star.StarModel;
import com.huike.nova.service.domain.param.startask.mina.star.AddPartnerCodeParam;
import com.huike.nova.service.domain.param.startask.mina.star.AddStarParam;
import com.huike.nova.service.domain.param.startask.mina.star.CheckStarTaskAreaLimitParam;
import com.huike.nova.service.domain.param.startask.mina.star.ConfirmApplyParam;
import com.huike.nova.service.domain.param.startask.mina.star.FindStarGeolocationParam;
import com.huike.nova.service.domain.param.startask.mina.star.FindStarInfoParam;
import com.huike.nova.service.domain.param.startask.mina.star.FindTaskPlazaListParam;
import com.huike.nova.service.domain.param.startask.mina.star.OperateStarApplyParam;
import com.huike.nova.service.domain.param.startask.mina.star.OperateStarParam;
import com.huike.nova.service.domain.param.startask.mina.star.SaveKsStarParam;
import com.huike.nova.service.domain.param.startask.mina.star.SelectPublishAccountParam;
import com.huike.nova.service.domain.param.startask.mina.star.StarApplyDetailParam;
import com.huike.nova.service.domain.param.startask.mina.star.StarApplyListParam;
import com.huike.nova.service.domain.param.startask.mina.star.StarDetailQueryParam;
import com.huike.nova.service.domain.param.startask.mina.star.UpdateStarInfoParam;

/**
 * 小程序达人角色服务
 *
 * <AUTHOR>
 * @version StarTaskStarMinaService.java, v 0.1 2023-11-29 1:50 PM ruanzy
 */
public interface StarTaskStarMinaService {

    /**
     * 达人任务广场列表
     *
     * @param param
     * @return
     */
    PageResult<FindTaskPlazaListModel> findTaskPlazaList(PageParam<FindTaskPlazaListParam> param);

    /**
     * 达人任务-参与列表
     *
     * @param param
     * @return
     */
    PageResult<StarApplyListModel> list(PageParam<StarApplyListParam> param);

    /**
     * 参与任务操作
     *
     * @param param
     */
    void operate(OperateStarApplyParam param);

    /**
     * 达人任务-参与详情
     *
     * @param param
     * @return
     */
    StarApplyDetailModel detail(StarApplyDetailParam param);

    /**
     * 达人任务-选择发布账号
     *
     * @param param
     * @return
     */
    SelectPublishAccountModel selectPublishAccount(SelectPublishAccountParam param);

    /**
     * 达人任务-确认报名
     *
     * @param param
     * @return
     */
    ConfirmApplyModel confirmApply(ConfirmApplyParam param);

    /**
     * 添加达人
     *
     * @param request
     * @return
     */
    AddStarModel addStar(AddStarParam request);

    /**
     * 保存快手达人信息
     *
     * @param request
     */
    AddStarModel saveKsStar(SaveKsStarParam request);

    /**
     * 达人信息查询
     *
     * @param param
     */
    StarDetailQueryModel starDetailQuery(StarDetailQueryParam param);

    /**
     * 达人列表
     *
     * @param param
     * @return
     */
    PageResult<StarModel> findStarList(PageParam param);

    /**
     * 更新达人数据
     *
     * @param param
     */
    void operateStar(OperateStarParam param);

    /**
     * 添加合作方
     *
     * @param param 合作方参数
     */
    void addPartner(AddPartnerCodeParam param);

    /**
     * 查询达人总数量
     *
     * @return 达人数量
     */
    StarCountModel findStarCount();

    /**
     * 查询广场达人列表
     *
     * @param param 请求参数
     * @return 分页数据
     */
    PageResult<SquareStarModel> querySquareStarList(PageParam<Void> param);

    /**
     * 修改达人信息
     *
     * @param param 请求参数
     */
    void updateStar(UpdateStarInfoParam param);

    /**
     * 查询达人信息
     *
     * @param param 请求
     * @return 返参
     */
    FindStarInfoModel findStarInfo(FindStarInfoParam param);

    /**
     * 获取达人地理位置
     *
     * @param param 请求
     * @return 返参
     */
    FindStarGeolocationModel findStarGeolocation(FindStarGeolocationParam param);

    /**
     * 检查任务区域限制
     *
     * @param param 请求
     * @return 返参
     */
    CheckStarTaskAreaLimitModel checkStarTaskAreaLimit(CheckStarTaskAreaLimitParam param);
}