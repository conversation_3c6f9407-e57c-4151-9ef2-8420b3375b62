package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.param.akstarmatrix.StarRankParam;
import com.huike.nova.service.domain.param.akstarmatrix.StarVideoDataStatisticsParam;
import com.huike.nova.service.domain.result.akstarmatrix.StarRankResult;
import com.huike.nova.service.domain.result.akstarmatrix.StarVideoDataStatisticsResult;

/**
 * 达人数据统计
 *
 * <AUTHOR>
 * @date 2023/2/15 14:54
 * @copyright 2022 barm Inc. All rights reserved
 */
public interface StarVideoDataStatisticsService {

    /**
     * 昨日达人rpa推广数据跑批
     * 总数据-历史数据=昨日数据
     */
    void batchRpaDataStatistics();

    /**
     * 达人推广数据汇总（代理商查询）
     *
     * @param param
     * @return
     */
    StarVideoDataStatisticsResult starVideoDataStatistics(StarVideoDataStatisticsParam param);

    /**
     * 达人排行（代理商查询）
     *
     * @param param
     * @return
     */
    PageResult<StarRankResult> starRank(PageParam<StarRankParam> param);
}
