package com.huike.nova.service.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.service.business.MinaProductService;
import com.huike.nova.service.domain.dto.mina.ProductSalesCountParamDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 销售数据统计
 *
 * <AUTHOR> (<EMAIL>)
 * @version SaleNumberCountListener.java, v1.0 2024-12-12 13:12 John Exp$
 */
@Data
@Slf4j(topic = "mq")
@Component
@AllArgsConstructor
public class ProductSalesCountListener implements MessageListener {

    private MinaProductService minaProductService;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        try {
            String body = StrUtil.str(message.getBody(), StandardCharsets.UTF_8);
            // JSON解析
            ProductSalesCountParamDTO productSalesCountParamDTO = JSONObject.parseObject(body, ProductSalesCountParamDTO.class);
            if (CollectionUtil.isNotEmpty(productSalesCountParamDTO.getProductIds())) {
                minaProductService.handleUpdateProductSalesCount(productSalesCountParamDTO.getProductIds());
            }
        } catch (Exception e) {
            LogUtil.warn(log, "SaleNumberCountListener.consume >> 消费错误: ", e);
        }
        return Action.CommitMessage;
    }
}
