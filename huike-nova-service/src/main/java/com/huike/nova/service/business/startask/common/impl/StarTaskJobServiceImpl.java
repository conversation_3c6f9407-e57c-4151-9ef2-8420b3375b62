/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.common.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.annimon.stream.function.Supplier;
import com.google.common.collect.Lists;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.constant.startask.agentweb.StarTaskAgentWebRedisPrefixConstant;
import com.huike.nova.common.constant.startask.mina.StarTaskMinaRedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.startask.agentweb.AgentDayStatisticalTypeEnum;
import com.huike.nova.common.enums.startask.mina.ApplyListStatusEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskPresetOperatorEnum;
import com.huike.nova.common.enums.startask.mina.SubscribeMessageTypeEnum;
import com.huike.nova.common.enums.startask.mina.TaskStatusEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogRemarkTypeEnum;
import com.huike.nova.common.enums.startask.web.DistributionTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.ExceptionUtil;
import com.huike.nova.common.util.FsDateUtils;
import com.huike.nova.common.util.IdWorkerUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.result.startask.AgentAreaResultDTO;
import com.huike.nova.dao.entity.StarTaskAgentDO;
import com.huike.nova.dao.entity.StarTaskAgentDayStatisticsDO;
import com.huike.nova.dao.entity.StarTaskApplyListAuditRecordDO;
import com.huike.nova.dao.entity.StarTaskApplyListDO;
import com.huike.nova.dao.entity.StarTaskBalanceAccountDO;
import com.huike.nova.dao.entity.StarTaskBalanceLogDO;
import com.huike.nova.dao.entity.StarTaskBindRelationDO;
import com.huike.nova.dao.entity.StarTaskCommissionPlanDO;
import com.huike.nova.dao.entity.StarTaskDO;
import com.huike.nova.dao.entity.StarTaskIdentityDO;
import com.huike.nova.dao.entity.StarTaskIdentityRelationDO;
import com.huike.nova.dao.entity.StarTaskSettleDetailDO;
import com.huike.nova.dao.entity.StarTaskUserDO;
import com.huike.nova.dao.repository.StarTaskAgentAccountDAO;
import com.huike.nova.dao.repository.StarTaskAgentAreaDAO;
import com.huike.nova.dao.repository.StarTaskAgentDAO;
import com.huike.nova.dao.repository.StarTaskAgentDayStatisticsDAO;
import com.huike.nova.dao.repository.StarTaskApplyListAuditRecordDAO;
import com.huike.nova.dao.repository.StarTaskApplyListDAO;
import com.huike.nova.dao.repository.StarTaskBalanceAccountDAO;
import com.huike.nova.dao.repository.StarTaskBalanceLogDAO;
import com.huike.nova.dao.repository.StarTaskBindRelationDAO;
import com.huike.nova.dao.repository.StarTaskCommissionPlanDAO;
import com.huike.nova.dao.repository.StarTaskDAO;
import com.huike.nova.dao.repository.StarTaskIdentityDAO;
import com.huike.nova.dao.repository.StarTaskIdentityRelationDAO;
import com.huike.nova.dao.repository.StarTaskSettleDetailDAO;
import com.huike.nova.dao.repository.StarTaskUserDAO;
import com.huike.nova.service.business.CommonService;
import com.huike.nova.service.business.common.DingDingCommonService;
import com.huike.nova.service.business.startask.common.StarTaskAsyncService;
import com.huike.nova.service.business.startask.common.StarTaskCommonService;
import com.huike.nova.service.business.startask.common.StarTaskJobService;
import com.huike.nova.service.business.startask.mina.SubscribeMessageService;
import com.huike.nova.service.domain.mapper.startask.web.StarTaskWebTaskServiceObjMapper;
import com.huike.nova.service.domain.param.activity.DayStatisticsActivityDayParam;
import com.huike.nova.service.domain.param.startask.web.task.TaskApplyStatusOperationParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version StarTaskJobServiceImpl.java, v 0.1 2023-12-10 11:49 AM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class StarTaskJobServiceImpl implements StarTaskJobService {

    private StarTaskDAO starTaskDAO;

    private StarTaskUserDAO starTaskUserDAO;

    private StarTaskApplyListDAO starTaskApplyListDAO;

    private StarTaskIdentityDAO starTaskIdentityDAO;

    private StarTaskAsyncService starTaskAsyncService;

    private StarTaskBindRelationDAO starTaskBindRelationDAO;

    private StarTaskIdentityRelationDAO starTaskIdentityRelationDAO;

    private StarTaskBalanceLogDAO starTaskBalanceLogDAO;

    private StarTaskBalanceAccountDAO starTaskBalanceAccountDAO;

    private TransactionTemplate transactionTemplate;

    private StarTaskCommonService starTaskCommonService;

    private DingDingCommonService dingDingCommonService;

    private RedissonClient redissonClient;

    private StarTaskCommissionPlanDAO starTaskCommissionPlanDAO;

    private StarTaskSettleDetailDAO starTaskSettleDetailDAO;

    private StarTaskAgentDAO starTaskAgentDAO;

    private StarTaskAgentDayStatisticsDAO starTaskAgentDayStatisticsDAO;

    private StarTaskAgentAreaDAO starTaskAgentAreaDao;

    private StarTaskAgentAccountDAO starTaskAgentAccountDAO;

    private CommonService commonService;

    private SubscribeMessageService subscribeMessageService;

    private StarTaskApplyListAuditRecordDAO starTaskApplyListAuditRecordDAO;

    private StarTaskWebTaskServiceObjMapper starTaskWebTaskServiceObjMapper;

    private SysConfig sysConfig;


    /**
     * 任务结束时间状态修改
     */
    @Override
    public void updateStarTaskByEndTime() {
        String applyEndTime = DateUtil.format(DateTime.now(), FsDateUtils.SIMPLE_DATE_FORMAT);
        // 查看所有任务结束的任务:流式
        starTaskDAO.findStarTaskByApplyEndTime(applyEndTime, resultContext -> {
            val resultObject = resultContext.getResultObject();
            if (Objects.isNull(resultObject)) {
                return;
            }
            String taskId = resultObject.getTaskId();
            String lock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_STAR_TASK, taskId);
            try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
                redisLockHelper.tryLock();
                LogUtil.info(log, "updateStarTaskByEndTime >> 报名时间到期结束报名任务 >> taskId={}", taskId);
                // 再查询一次
                StarTaskDO starTask = starTaskDAO.getStarTaskByTaskId(taskId);
                if (Objects.isNull(starTask)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("查询信息为空");
                }
                Integer taskStatus = starTask.getTaskStatus();
                if (TaskStatusEnum.SETTLEMENT_IN_PROCESS.getValue().equals(taskStatus)
                        || TaskStatusEnum.COMPLETED.getValue().equals(taskStatus)
                        || TaskStatusEnum.CANCELED.getValue().equals(taskStatus)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务状态发生改变");
                }
                // 更新任务状态值
                starTaskDAO.updateStarTaskStatus(taskId, TaskStatusEnum.SETTLEMENT_IN_PROCESS.getValue());

                // 修改对应的任务清单状态值
                //starTaskCommonService.updateStatusToCanceled(taskId, CommonConstant.INTEGER_ONE);
            } catch (Exception e) {
                LogUtil.warn(log, "StarTaskJobServiceImpl" + "." + "updateStarTaskByEndTime" + " >>>>> " + "执行加锁方法失败", e);
            }
        });
    }

    /**
     * 结算完成任务状态修改
     */
    @Override
    public void finishStarTask(String taskId) {
        // 查询所有结算中的任务:流式
        starTaskDAO.findStarTaskToSettlement(taskId, resultContext -> {
            val starTaskDO = resultContext.getResultObject();
            if (Objects.isNull(starTaskDO)) {
                return;
            }
            // 任务锁
            String lock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_STAR_TASK, starTaskDO.getTaskId());
            try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
                redisLockHelper.tryLock();
                // 查询参与达人任务列表
                List<StarTaskApplyListDO> applyList = starTaskApplyListDAO.findApplyListByTaskId(starTaskDO.getTaskId());
                // 判断是否存在未完成的达人任务
                boolean flag = Boolean.TRUE;
                BigDecimal finishAmount = BigDecimal.ZERO;
                BigDecimal platformMoney = BigDecimal.ZERO;
                for (StarTaskApplyListDO applyListDO : applyList) {
                    String applyLock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_APPLY_LIST, applyListDO.getApplyId());
                    try (val applyRedisLockHelper = new RedisLockHelper(applyLock, redissonClient)) {
                        applyRedisLockHelper.tryLock();
                        StarTaskApplyListDO applyDetail = starTaskApplyListDAO.getApplyListByApplyId(applyListDO.getApplyId());
                        if (Objects.isNull(applyDetail)) {
                            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("清单数据为空");
                        }
                        Integer applyStatus = applyDetail.getApplyStatus();
                        //如果报名的列表中只要有（待提交链接，待审核链接，平台审核，已驳回）1,2,5,6的状态，就不能修改为已完成。
                        if (ApplyListStatusEnum.WAITING_SUBMIT.getValue().equals(applyStatus) || ApplyListStatusEnum.LINK_AUDIT.getValue().equals(applyStatus) ||
                                ApplyListStatusEnum.PLATFORM_AUDIT.getValue().equals(applyStatus) || ApplyListStatusEnum.REJECTED.getValue().equals(applyStatus)) {
                            flag = Boolean.FALSE;
                        }
                        // 计算金额
                        if (ApplyListStatusEnum.COMPLETED.getValue().equals(applyStatus) || ApplyListStatusEnum.PLATFORM_COMPLETED.getValue().equals(applyStatus)) {
                            finishAmount = finishAmount.add(applyDetail.getTotalAmount());
                            platformMoney = platformMoney.add(applyDetail.getTotalAmount().subtract(applyDetail.getTaskMoney()));
                        }
                    } catch (Exception e) {
                        LogUtil.error(log, "StarTaskJobServiceImpl" + "." + "finishStarTask" + " >>>>> " + "执行加锁方法失败", e);
                        ExceptionUtil.toCommonException(e);
                    }
                }
                // 已完成后，需要进行结算逻辑
                if (flag) {
                    String identityLock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_APPLY_WITHDRAWAL, starTaskDO.getIdentityId());
                    try (val identityRedisLockHelper = new RedisLockHelper(identityLock, redissonClient)) {
                        identityRedisLockHelper.tryLock();
                        BigDecimal finalFinishAmount = finishAmount;
                        transactionTemplate.execute(status -> {
                            // 更新任务状态值,结算完成后需要修改任务表的completeAmount字段
                            starTaskDAO.updateCompleteAmount(starTaskDO.getTaskId(), finalFinishAmount);
                            // 计算金额，多余的金额退回商户账户
                            BigDecimal totalAmount = starTaskDO.getTotalAmount();
                            BigDecimal amount = totalAmount.subtract(finalFinishAmount);
                            // 退款给商家
                            starTaskCommonService.refundToMerchant(starTaskDO, amount, "结算退款");
                            return true;
                        });
                    } catch (Exception e) {
                        LogUtil.error(log, "StarTaskJobServiceImpl" + "." + "finishStarTask" + " >>>>> " + "执行加锁方法失败", e);
                        ExceptionUtil.toCommonException(e);
                    }
                    // 商家返利
                    StarTaskIdentityRelationDO identityRelationDO = starTaskIdentityRelationDAO.findListByIdentityId(starTaskDO.getIdentityId());
                    // 存在，队长需要更新余额，增加一条返还奖励明细
                    BigDecimal merchantReward = BigDecimal.ZERO;
                    if (Objects.nonNull(identityRelationDO) && StringUtils.isNotBlank(identityRelationDO.getParentId()) && finishAmount.compareTo(BigDecimal.ZERO) == 1) {
                        merchantReward = starTaskCommonService.buildMerchantReturnReward(starTaskDO, identityRelationDO, finishAmount);
                    }
                    if (StringUtils.isNotBlank(starTaskDO.getAgentId())) {
                        // 增加区代任务结算详情
                        this.saveSettleDetail(starTaskDO, finishAmount, platformMoney, merchantReward);
                    }
                }
            } catch (Exception e) {
                LogUtil.error(log, "StarTaskJobServiceImpl" + "." + "finishStarTask" + " >>>>> " + "执行加锁方法失败", e);
            }
        });
    }

    /**
     * 增加区代任务结算详情
     *
     * @param starTaskDO
     * @param finishAmount
     * @param platformMoney
     * @param merchantReward
     */
    private void saveSettleDetail(StarTaskDO starTaskDO, BigDecimal finishAmount, BigDecimal platformMoney, BigDecimal merchantReward) {
        String identityId = starTaskDO.getIdentityId();
        String taskId = starTaskDO.getTaskId();
        merchantReward = merchantReward.divide(new BigDecimal("0.94"), 2, BigDecimal.ROUND_HALF_UP);
        StarTaskSettleDetailDO settleDetailDO = new StarTaskSettleDetailDO();
        settleDetailDO.setTaskId(taskId);
        settleDetailDO.setTaskTitle(starTaskDO.getTaskTitle());
        settleDetailDO.setAgentId(starTaskDO.getAgentId());
        settleDetailDO.setIdentityId(identityId);
        // 获取身份信息
        StarTaskIdentityDO identity = starTaskIdentityDAO.getIdentityByIdentityId(identityId);
        if (Objects.nonNull(identity)) {
            settleDetailDO.setPhoneNumber(identity.getPhoneNumber());
        }
        settleDetailDO.setProvince(starTaskDO.getAgentProvince());
        settleDetailDO.setCity(starTaskDO.getAgentCity());
        settleDetailDO.setPrepaidTaskAmount(starTaskDO.getTotalAmount());
        settleDetailDO.setTaskTransactionAmount(finishAmount);
        settleDetailDO.setMerchantReward(merchantReward);
        // 获取达人返回奖励
        List<StarTaskBalanceLogDO> list = starTaskBalanceLogDAO.findStarRewardByTaskId(taskId);
        BigDecimal starReward = list.stream().map(StarTaskBalanceLogDO::getChangeAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(new BigDecimal("0.94"), 2, BigDecimal.ROUND_HALF_UP);
        settleDetailDO.setStarReward(starReward);
        BigDecimal commissionSettle = platformMoney.subtract(merchantReward.add(starReward));
        settleDetailDO.setCommissionSettle(commissionSettle);
        starTaskSettleDetailDAO.saveSettleDetail(settleDetailDO);
        // 佣金结算金额为负数，发送钉钉消息
        if (commissionSettle.compareTo(BigDecimal.ZERO) == -1) {
            // 发送一个钉钉消息
            dingDingCommonService.sendStarTaskSettleDetail("区代佣金结算", "任务id:" + taskId + "出现负数");
        }
    }

    /**
     * 更新平台审核状态
     */
    @Override
    public void updatePlatformAudit() {
        // 提交时间
        String submitTime = DateUtil.format(DateUtils.addDays(DateTime.now().toJdkDate(), -3), FsDateUtils.SIMPLE_DATETIME_FORMAT);
        // 查询待平台审核的报名清单:流式
        starTaskApplyListDAO.findToPlatformAudit(submitTime, resultContext -> {
            val resultObject = resultContext.getResultObject();
            if (Objects.isNull(resultObject)) {
                return;
            }
            String applyId = resultObject.getApplyId();
            String lock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_APPLY_LIST, applyId);
            try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
                redisLockHelper.tryLock();
                // 判断状态是否已经发生改变
                StarTaskApplyListDO taskApplyListDO = starTaskApplyListDAO.getApplyListByApplyId(applyId);
                if (Objects.isNull(taskApplyListDO)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("查询信息为空");
                }
                String taskId = taskApplyListDO.getTaskId();
                StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(taskId);
                if (null == starTaskDO) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务错误或不存在");
                }
                if (!ApplyListStatusEnum.LINK_AUDIT.getValue().equals(taskApplyListDO.getApplyStatus())) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("状态发生改变");
                }
                // 超过72小时未审核的，直接审核通过
                TaskApplyStatusOperationParam param = new TaskApplyStatusOperationParam();
                param.setApplyId(applyId);
                param.setOperateType(CommonConstant.INTEGER_TWO);
                this.applyStatusOperation(param, "", "");
            } catch (Exception e) {
                LogUtil.warn(log, "StarTaskJobServiceImpl" + "." + "updatePlatformAudit" + " >>>>> " + "执行加锁方法失败", e);
            }
        });
    }

    /**
     * 初始化身份关联
     */
    @Override
    public void initIdentityRelationJobHandler() {
        // 查询所有身份数据
        List<StarTaskIdentityDO> identityList = starTaskIdentityDAO.findAllIdentityList();
        if (CollectionUtil.isEmpty(identityList)) {
            return;
        }
        for (StarTaskIdentityDO starTaskIdentityDO : identityList) {
            try {
                String identityId = starTaskIdentityDO.getIdentityId();
                // 冗余手机号
                StarTaskUserDO userDO = starTaskUserDAO.getUserInfoByUserId(starTaskIdentityDO.getUserId());
                if (Objects.isNull(userDO)) {
                    LogUtil.info(log, "StarTaskJobServiceImpl" + "." + "initIdentityRelationJobHandler" + " >>>>> " + "手机号查询失败,userId={}", starTaskIdentityDO.getUserId());
                    continue;
                }
                if (StringUtils.isBlank(starTaskIdentityDO.getPhoneNumber())) {
                    starTaskIdentityDAO.updatePhone(userDO, identityId);
                }
                // 是否存在关系
                StarTaskIdentityRelationDO identityRelationDO = starTaskIdentityRelationDAO.findListByIdentityId(identityId);
                if (Objects.nonNull(identityRelationDO)) {
                    continue;
                }
                // 新增邀请人关系信息
                StarTaskIdentityRelationDO relation = new StarTaskIdentityRelationDO();
                relation.setUserId(starTaskIdentityDO.getUserId());
                relation.setIdentityId(identityId);
                relation.setIdentityType(starTaskIdentityDO.getIdentityType());
                // 上级id,根据用户id和类型
                StarTaskIdentityDO identityInfo = starTaskIdentityDAO.getIdentityInfo(starTaskIdentityDO.getInviterId(), starTaskIdentityDO.getIdentityType());
                if (Objects.nonNull(identityInfo)) {
                    relation.setParentId(identityInfo.getIdentityId());
                    // 上上级id
                    StarTaskIdentityDO taskIdentityDO = starTaskIdentityDAO.getIdentityInfo(identityInfo.getInviterId(), identityInfo.getIdentityType());
                    if (Objects.nonNull(taskIdentityDO)) {
                        relation.setGrandparentId(taskIdentityDO.getIdentityId());
                    }
                }
                relation.setDistributionType(DistributionTypeEnum.DEFAULT.getValue());
                starTaskIdentityRelationDAO.saveIdentityRelation(relation);
            } catch (Exception e) {
                LogUtil.warn(log, "StarTaskJobServiceImpl" + "." + "initIdentityRelationJobHandler" + " >>>>> " + "初始化身份关联异常", e);
            }
        }
    }

    /**
     * 解绑重复达人
     */
    @Override
    public void unbindRepeatStar() {
        // 查询重复的达人数据
        List<StarTaskBindRelationDO> list = starTaskBindRelationDAO.findRepeatStar();
        // 需要保留最早达人绑定记录，解绑其他的达人数据，取消进行中的任务
        for (StarTaskBindRelationDO starTaskBindRelationDO : list) {
            String starId = starTaskBindRelationDO.getStarId();
            if (StringUtils.isBlank(starId)) {
                continue;
            }
            // 根据达人id查询绑定成功记录列表
            List<StarTaskBindRelationDO> starList = starTaskBindRelationDAO.findStarListByStarId(starId);
            if (CollectionUtil.isNotEmpty(starList)) {
                // 遍历列表，从索引1开始处理
                for (int i = 1; i < starList.size(); i++) {
                    try {
                        StarTaskBindRelationDO item = starList.get(i);
                        // 在这里对数据进行手动解绑
                        starTaskAsyncService.operateUnbind(item.getRelationId());
                        // 睡眠1s
                        Thread.sleep(1000);
                    } catch (Exception e) {
                        LogUtil.error(log, "StarTaskJobServiceImpl" + "." + "unbindRepeatStar" + " >>>>> " + "解绑重复达人异常", e);
                    }
                }
            }
        }
    }

    /**
     * 余额数据清洗（余额不能出错）
     */
    @Override
    public void cleanBalanceJobHandler() {
        // 查询所有的账号数据
        List<StarTaskBalanceAccountDO> allAccountList = starTaskBalanceAccountDAO.findAllAccountList();
        for (StarTaskBalanceAccountDO starTaskBalanceAccountDO : allAccountList) {
            try {
                String identityId = starTaskBalanceAccountDO.getIdentityId();
                // 查询身份信息
                StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(identityId);
                if (Objects.isNull(identityDO)) {
                    continue;
                }
                // ailike_b_star_task_balance_log根据remark_type 等于8获取奖励金余额
                List<StarTaskBalanceLogDO> list = starTaskBalanceLogDAO.findListByIdentityIdAndRemarkType(identityId, BalanceLogRemarkTypeEnum.RETURN_REWARD.getValue());
                // 累计奖励金
                BigDecimal returnReward = list.stream().map(StarTaskBalanceLogDO::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 奖励金提现余额字段
                BigDecimal availableBalance = starTaskBalanceAccountDO.getAvailableBalance();
                BigDecimal rewardBalance;
                if (availableBalance.compareTo(returnReward) >= 0) {
                    // 1.总余额 > = 计算出奖励金余额，存库的奖励金余额 = 计算出奖励金余额
                    rewardBalance = returnReward;
                } else {
                    // 2.总余额 < 计算出奖励金余额，存库的奖励金余额 = 现有的总余额
                    rewardBalance = availableBalance;
                }
                LogUtil.info(log, "StarTaskJobServiceImpl.cleanBalanceJobHandler >>>>> 数据更新,identityId={},returnReward={},rewardBalance={}", identityId, returnReward, rewardBalance);
                // 更新数据库
                starTaskBalanceAccountDAO.cleanBalance(identityId, returnReward, rewardBalance);
            } catch (Exception e) {
                LogUtil.warn(log, "StarTaskJobServiceImpl.cleanBalanceJobHandler >>>>> 余额数据清洗异常", e);
            }
        }
    }

    /**
     * 统计任务报名完成量设置任务状态完成
     *
     * @param taskId
     */
    @Override
    public void statisticsTaskIsFinish(String taskId) {
        StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(taskId);
        if (ObjectUtil.isNull(starTaskDO)) {
            return;
        }

        // 统计任务报名完成的数量
        int taskApplyFinishNum = starTaskApplyListDAO.taskApplyFinishCount(taskId);

        // 统计任务的佣金计划人数
        List<StarTaskCommissionPlanDO> list = starTaskCommissionPlanDAO.findCommissionPlanByTaskId(taskId);
        int needStartNum = list.stream().mapToInt(StarTaskCommissionPlanDO::getNeededCount).sum();

        //如果完成的人数和需要的人生相同就设置任务的状态为已完成
        if (taskApplyFinishNum == needStartNum) {
            //设置任务为结算中
            starTaskDO.setTaskStatus(TaskStatusEnum.SETTLEMENT_IN_PROCESS.getValue());
            starTaskDAO.updateStarTask(starTaskDO);

            //将结算中的任务进行完成操作
            this.finishStarTask(starTaskDO.getTaskId());
        }
    }

    /**
     * 区代日统计
     *
     * @param param
     */
    @Override
    public void agentDayStatistics(String param) {
        // 时间转换
        DayStatisticsActivityDayParam paramObj = Supplier.Util.safe(
                        () -> JSONObject.parseObject(param, DayStatisticsActivityDayParam.class),
                        new DayStatisticsActivityDayParam())
                .get();
        // 传入日期 2024-02-14
        DateTime calculateDay = Supplier.Util.safe(() ->
                        DateUtil.parse(paramObj.getCalculateDay(), FsDateUtils.NUMBER_DATE_FORMAT), DateTime.now())
                .get();
        Date nDate = ObjectUtil.defaultIfNull(calculateDay, new Date());
        // 转为开始统计的起始
        LocalDateTime calcLocalDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(nDate.getTime()), CommonConstant.GMT_8)
                .toLocalDate().atStartOfDay();
        // 获取时间（昨日）
        LocalDateTime ldtStartTime = calcLocalDateTime.minusDays(1L);
        LocalDateTime ldtEndTime = calcLocalDateTime;
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(FsDateUtils.SIMPLE_DATE_FORMAT);
        String startTime = dateTimeFormatter.format(ldtStartTime);
        String endTime = dateTimeFormatter.format(ldtEndTime);
        // 显示统计日期：需要进行统计的日期
        Integer dayTime = FsDateUtils.dateToInteger(DateTimeFormatter.ofPattern(FsDateUtils.NORM_DATE_PATTERN, Locale.CHINA).format(ldtStartTime));
        // 根据代理商纬度进行区代日统计
        starTaskAgentDAO.findAgentToSettlement(resultContext -> {
            val starTaskDO = resultContext.getResultObject();
            if (Objects.isNull(starTaskDO)) {
                return;
            }
            List<StarTaskAgentDayStatisticsDO> list = Lists.newArrayList();
            String agentId = starTaskDO.getAgentId();
            try {
                // 第一部分：统计区代任务结束数据
                List<StarTaskSettleDetailDO> settleDetailList = this.buildSettleDetail(dayTime, starTaskDO, list, startTime, endTime);
                // 第二部分：统计区代日数据商家和达人数据
                List<AgentAreaResultDTO> settleCityList = starTaskAgentAreaDao.findSettleCityList(agentId);
                if (CollectionUtil.isNotEmpty(settleCityList)) {
                    for (AgentAreaResultDTO resultDTO : settleCityList) {
                        String city = resultDTO.getCity();
                        // 查询新增商家数和达人数
                        List<StarTaskIdentityDO> identityDOList = starTaskIdentityDAO.countUserByCityAndTime(city, ldtStartTime.toEpochSecond(ZoneOffset.of("+8")), ldtEndTime.toEpochSecond(ZoneOffset.of("+8")));
                        Map<Integer, List<StarTaskIdentityDO>> map = identityDOList.stream().collect(Collectors.groupingBy(StarTaskIdentityDO::getIdentityType));
                        // 查询发布任务数
                        Integer tasksPublished = starTaskDAO.countTasksPublished(agentId, startTime, endTime, city);
                        StarTaskAgentDayStatisticsDO settleDetailDO = new StarTaskAgentDayStatisticsDO();
                        settleDetailDO.setStatisticsId(IdWorkerUtil.getSingleId());
                        settleDetailDO.setTasksPublished(tasksPublished);
                        settleDetailDO.setNewMerchants(map.getOrDefault(StarTaskIdentityTypeEnum.MERCHANT.getValue(), Lists.newArrayList()).size());
                        settleDetailDO.setNewStars(map.getOrDefault(StarTaskIdentityTypeEnum.STAR.getValue(), Lists.newArrayList()).size());
                        settleDetailDO.setCalculateDay(dayTime);
                        settleDetailDO.setStatisticalType(AgentDayStatisticalTypeEnum.AREA_AGENT_USER.getValue());
                        settleDetailDO.setAgentId(agentId);
                        settleDetailDO.setProvince(resultDTO.getProvince());
                        settleDetailDO.setCity(city);
                        list.add(settleDetailDO);
                    }
                }
                String lock = StrUtil.format(StarTaskAgentWebRedisPrefixConstant.STAR_TASK_AGENT_ACCOUNT_CACHE_KEY, agentId);
                try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
                    redisLockHelper.tryLock();
                    transactionTemplate.execute(status -> {
                        if (CollectionUtil.isNotEmpty(settleDetailList)) {
                            // 更新状态
                            starTaskSettleDetailDAO.batchUpdateSettleDetail(settleDetailList);
                            // 增加代理商余额
                            BigDecimal availableBalance = settleDetailList.stream().map(StarTaskSettleDetailDO::getCommissionSettle).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal taskTransactionAmount = settleDetailList.stream().map(StarTaskSettleDetailDO::getTaskTransactionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            starTaskAgentAccountDAO.updateAvailableBalance(agentId, availableBalance, taskTransactionAmount);
                        }
                        if (CollectionUtil.isNotEmpty(list)) {
                            // 批量新增
                            starTaskAgentDayStatisticsDAO.batchAgentDayStatistics(list);
                        }
                        return true;
                    });

                } catch (Exception e) {
                    LogUtil.warn(log, "StarTaskJobServiceImpl" + " >>>>> " + "agentDayStatistics" + "  锁释放失败", e);
                    throw ExceptionUtil.toCommonException(e);
                }
            } catch (Exception e) {
                LogUtil.warn(log, "StarTaskJobServiceImpl.agentDayStatistics >>>>> 区代日统计异常,agentId={}", agentId, e);
            }
        });
        // 加上redis缓存
        redissonClient.getBucket(StrUtil.format(StarTaskAgentWebRedisPrefixConstant.STAR_TASK_AGENT_WEB_HOME_UPDATE_TIME)).set(DateUtil.now());
        LogUtil.info(log, "StarTaskJobServiceImpl.agentDayStatistics >>>>> 区代日统计结束");
    }

    /**
     * 报名清单状态操作
     *
     * @param param
     * @param operatorId
     * @param contactName
     */
    @Override
    public void applyStatusOperation(TaskApplyStatusOperationParam param, String operatorId, String contactName) {
        Integer operateType = param.getOperateType();
        String applyId = param.getApplyId();
        String rejectReason = param.getRejectReason();
        Integer applyStatus;
        StarTaskApplyListDO applyListDO = starTaskApplyListDAO.getApplyListByApplyId(applyId);
        if (null == applyId) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("报名清单异常");
        }
        if (CommonConstant.INTEGER_ONE.equals(operateType)) {
            applyStatus = ApplyListStatusEnum.REJECTED.getValue();
        } else if (CommonConstant.INTEGER_TWO.equals(operateType)) {
            applyStatus = ApplyListStatusEnum.PLATFORM_COMPLETED.getValue();
        } else if (CommonConstant.INTEGER_THREE.equals(operateType)) {
            // 取消任务
            applyListDO.setCancelReason(param.getRejectReason());
            starTaskAsyncService.cancelRegistration(applyListDO);
            return;
        } else {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("操作类型错误");
        }
        if (!ApplyListStatusEnum.LINK_AUDIT.getValue().equals(applyListDO.getApplyStatus()) && !ApplyListStatusEnum.PLATFORM_AUDIT.getValue().equals(applyListDO.getApplyStatus())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("状态已变更，请稍后再试");
        }
        String taskId = applyListDO.getTaskId();
        StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(taskId);
        if (null == starTaskDO) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务不存在或错误");
        }
        StarTaskApplyListAuditRecordDO starTaskApplyListAuditRecordDO = starTaskWebTaskServiceObjMapper.toStarTaskApplyListAuditRecordDO(applyListDO);
        starTaskApplyListAuditRecordDO.setId(null);
        starTaskApplyListAuditRecordDO.setRecordId(commonService.buildIncr());
        starTaskApplyListAuditRecordDO.setAuditTime(new Date());
        starTaskApplyListAuditRecordDO.setAuditStatus(operateType);
        starTaskApplyListAuditRecordDO.setCreateTime(null);
        starTaskApplyListAuditRecordDO.setUpdateTime(null);
        starTaskApplyListAuditRecordDO.setRejectReason(rejectReason);
        starTaskApplyListAuditRecordDO.setOperatorId(operatorId);
        starTaskApplyListAuditRecordDO.setOperatorName(contactName);
        String identityLock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_APPLY_WITHDRAWAL, applyListDO.getIdentityId());
        try (val redisLockHelper = new RedisLockHelper(identityLock, redissonClient)) {
            redisLockHelper.tryLock();
            // 事物
            transactionTemplate.execute(status -> {
                starTaskApplyListDAO.applyStatusOperation(applyId, applyStatus, rejectReason, null);
                starTaskApplyListAuditRecordDAO.save(starTaskApplyListAuditRecordDO);
                if (ApplyListStatusEnum.PLATFORM_COMPLETED.getValue().equals(applyStatus)) {
                    starTaskCommonService.updateTaskMoney(applyListDO.getIdentityId(), applyListDO, starTaskDO, StarTaskPresetOperatorEnum.SYSTEM.getOperatorId(), StarTaskPresetOperatorEnum.SYSTEM.getName());
                }
                return Boolean.TRUE;
            });
            List<String> templateDataList = new ArrayList<>();
            templateDataList.add(starTaskDO.getTaskTitle().length() > CommonConstant.INTEGER_TWENTY ? starTaskDO.getTaskTitle().substring(0, CommonConstant.INTEGER_SEVENTEEN) + StringPool.ELLIPSIS : starTaskDO.getTaskTitle());
            templateDataList.add(applyStatus.equals(ApplyListStatusEnum.REJECTED.getValue()) ? "驳回" : "通过");
            templateDataList.add(DateUtil.format(new Date(), FsDateUtils.SIMPLE_DATETIME_FORMAT));
            templateDataList.add(applyStatus.equals(ApplyListStatusEnum.REJECTED.getValue()) ? (param.getRejectReason().length() > CommonConstant.INTEGER_TWENTY ? param.getRejectReason().substring(0, CommonConstant.INTEGER_SEVENTEEN) + StringPool.ELLIPSIS : param.getRejectReason()) : "您提交的作品已通过审核，快来查看吧");
            subscribeMessageService.sendSubscribeMessage(applyListDO.getApplyId(), starTaskDO.getAppletId(), sysConfig.getWxSubscribeMessageWorksAuditResult(), "master/taskSquare/index?active=masterApplyList&enterScene=1", templateDataList, SubscribeMessageTypeEnum.SUBMIT_LINK.getValue());
        } catch (Exception e) {
            LogUtil.error(log, "StarTaskWebTaskServiceImpl" + "." + "applyStatusOperation" + " >>>>> " + "执行加锁方法失败", e);
            throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage(e.getMessage());
        }
        if (ApplyListStatusEnum.PLATFORM_COMPLETED.getValue().equals(applyStatus)) {
            // 查询是否存在上级团长
            StarTaskIdentityRelationDO identityRelationDO = starTaskIdentityRelationDAO.findListByIdentityId(applyListDO.getIdentityId());
            // 存在，团长需要更新余额，增加一条返还奖励明细
            if (Objects.nonNull(identityRelationDO) && StringUtils.isNotBlank(identityRelationDO.getParentId())) {
                starTaskCommonService.buildStarReturnReward(applyListDO, starTaskDO, identityRelationDO);
            }
            // 如果同意操作，统计任务数量,如果完成数量和商家任务需要人数相同，将任务的状态修改为已完成
            this.statisticsTaskIsFinish(taskId);
        }
    }

    private List<StarTaskSettleDetailDO> buildSettleDetail(Integer dayTime, StarTaskAgentDO starTaskDO, List<StarTaskAgentDayStatisticsDO> list, String startTime, String endTime) {
        String agentId = starTaskDO.getAgentId();
        // 根据区代id查询今天结算详情
        List<StarTaskSettleDetailDO> settleDetailList = starTaskSettleDetailDAO.findSettleDetail(agentId, startTime, endTime);
        if (CollectionUtil.isNotEmpty(settleDetailList)) {
            Map<String, List<StarTaskSettleDetailDO>> map = settleDetailList.stream().collect(Collectors.groupingBy(StarTaskSettleDetailDO::getCity));
            for (Map.Entry<String, List<StarTaskSettleDetailDO>> stringListEntry : map.entrySet()) {
                String key = stringListEntry.getKey();
                List<StarTaskSettleDetailDO> value = stringListEntry.getValue();
                BigDecimal commissionSettle = BigDecimal.ZERO;
                BigDecimal prepaidTaskAmount = BigDecimal.ZERO;
                BigDecimal merchantReward = BigDecimal.ZERO;
                BigDecimal starReward = BigDecimal.ZERO;
                BigDecimal taskTransactionAmount = BigDecimal.ZERO;
                String singleId = IdWorkerUtil.getSingleId();
                for (StarTaskSettleDetailDO settleDetailDO : value) {
                    prepaidTaskAmount = prepaidTaskAmount.add(settleDetailDO.getPrepaidTaskAmount());
                    merchantReward = merchantReward.add(settleDetailDO.getMerchantReward());
                    starReward = starReward.add(settleDetailDO.getStarReward());
                    commissionSettle = commissionSettle.add(settleDetailDO.getCommissionSettle());
                    taskTransactionAmount = taskTransactionAmount.add(settleDetailDO.getTaskTransactionAmount());
                    settleDetailDO.setSettleStatus(CommonConstant.INTEGER_TWO);
                    settleDetailDO.setUpdateTime(new Date());
                    settleDetailDO.setStatisticsId(singleId);
                }
                StarTaskAgentDayStatisticsDO settleDetailDO = new StarTaskAgentDayStatisticsDO();
                settleDetailDO.setStatisticsId(singleId);
                settleDetailDO.setAgentId(agentId);
                settleDetailDO.setCity(key);
                settleDetailDO.setProvince(value.get(0).getProvince());
                settleDetailDO.setCommissionSettle(commissionSettle);
                settleDetailDO.setTasksCompleted(value.size());
                settleDetailDO.setPrepaidTaskAmount(prepaidTaskAmount);
                settleDetailDO.setTaskTransactionAmount(taskTransactionAmount);
                settleDetailDO.setMerchantReward(merchantReward);
                settleDetailDO.setStarReward(starReward);
                settleDetailDO.setCalculateDay(dayTime);
                settleDetailDO.setStatisticalType(AgentDayStatisticalTypeEnum.TASK_SETTLEMENT.getValue());
                list.add(settleDetailDO);
            }
        }
        return settleDetailList;
    }
}