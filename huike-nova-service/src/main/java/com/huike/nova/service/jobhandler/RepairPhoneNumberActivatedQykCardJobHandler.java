package com.huike.nova.service.jobhandler;

import com.huike.nova.service.business.qyk.mina.QykToolsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 修复已激活但是号码为空的商圈卡
 *
 * <AUTHOR> (<EMAIL>)
 * @version RepairPhoneNumberActivatedQykCardJobHandler.java, v1.0 2024-12-21 23:55 John Exp$
 */
@Component
@Slf4j
@JobHandler("repairPhoneNumberActivatedQykCardJobHandler")
@AllArgsConstructor
public class RepairPhoneNumberActivatedQyk<PERSON>ardJobHandler extends AbsJobHandler {

    private QykToolsService qykToolsService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        qykToolsService.fixActivatedButDontHavePhoneNumbersQykCard(null);
        return ReturnT.SUCCESS;
    }
}
