/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.listener;

import cn.hutool.core.util.StrUtil;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.huike.nova.common.constant.startask.mina.StarTaskMinaRedisPrefixConstant;
import com.huike.nova.common.enums.startask.mina.TaskStatusEnum;
import com.huike.nova.common.util.ExceptionUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.dao.entity.StarTaskDO;
import com.huike.nova.dao.repository.StarTaskBalanceAccountDAO;
import com.huike.nova.dao.repository.StarTaskDAO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version StarTaskPayTimeListener.java, v 0.1 2023-12-14 2:08 PM ruanzy
 */
@Slf4j(topic = "mq")
@Component
@AllArgsConstructor
public class StarTaskPayTimeListener implements MessageListener {

    private static final Integer INTEGER_THIRTY = 30;

    private StarTaskDAO starTaskDAO;

    private StarTaskBalanceAccountDAO starTaskBalanceAccountDAO;

    private RedissonClient redissonClient;

    private TransactionTemplate transactionTemplate;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        LogUtil.info(log, "StarTaskPayTimeListener.consume >> 消息处理开始");
        String taskId = new String(message.getBody(), StandardCharsets.UTF_8);
        if (StringUtils.isBlank(taskId)) {
            LogUtil.info(log, "StarTaskPayTimeListener:接收到的消息为空！message={}", message);
            return Action.CommitMessage;
        }
        String lock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_STAR_TASK, taskId);
        try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
            redisLockHelper.tryLock();
            // 查询任务数据
            StarTaskDO starTaskDO = starTaskDAO.getStarTaskByTaskId(taskId);
            if (Objects.isNull(starTaskDO)) {
                return Action.CommitMessage;
            }
            // 判断状态是否是未支付
            if (!TaskStatusEnum.WAITING_FOR_PAYMENT.getValue().equals(starTaskDO.getTaskStatus())) {
                LogUtil.warn(log, "StarTaskPayTimeListener >> 达人任务状态不是未支付, taskId={}", taskId);
                return Action.CommitMessage;
            }
            Date createTime = starTaskDO.getCreateTime();
            // 获取当前时间
            Date currentTime = new Date();
            // 计算时间差（分钟）
            long millisecondsDifference = currentTime.getTime() - createTime.getTime();
            long minutesDifference = millisecondsDifference / (60 * 1000);

            // 如果时间差超过30分钟，则更改订单状态
            if (minutesDifference < INTEGER_THIRTY) {
                return Action.ReconsumeLater;
            }
            String identityLock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_APPLY_WITHDRAWAL, starTaskDO.getIdentityId());
            try (val identityRedisLockHelper = new RedisLockHelper(identityLock, redissonClient)) {
                identityRedisLockHelper.tryLock();
                // 事物
                transactionTemplate.execute(status -> {
                    // 超时未支付状态变成取消
                    starTaskDAO.updateTaskToPayOvertime(taskId, TaskStatusEnum.CANCELED.getValue());
                    // 冻结金额退还给商家余额
                    starTaskBalanceAccountDAO.rechargeByIdentityId(starTaskDO.getIdentityId(), starTaskDO.getFrozenBalanceAmount());
                    return Boolean.TRUE;
                });
            } catch (Exception e) {
                LogUtil.error(log, "StarTaskPayTimeListener" + "." + "consume" + " >>>>> " + "执行加锁方法失败", e);
                throw ExceptionUtil.toCommonException(e);
            }
        } catch (Exception e) {
            LogUtil.error(log, "StarTaskPayTimeListener" + "." + "consume" + " >>>>> " + "执行加锁方法失败", e);
        }
        LogUtil.info(log, "StarTaskPayTimeListener.consume >> 消息处理结束");
        return Action.CommitMessage;
    }
}