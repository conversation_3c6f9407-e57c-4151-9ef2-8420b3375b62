/**
 * ailike.com
 * Copyright (C) 2021-2023 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.pay.GetAppreciationOpeningInfoModel;
import com.huike.nova.service.domain.model.pay.GetMerchantOpeningStatusModel;
import com.huike.nova.service.domain.param.appreciation.QueryOpeningRecordParam;
import com.huike.nova.service.domain.param.pay.GetAppreciationOpeningInfoParam;
import com.huike.nova.service.domain.param.pay.GetMerchantOpeningStatusParam;

/**
 * 增值开通记录接口
 *
 * <AUTHOR>
 * @version AppreciationOpeningRecordService.java, v 0.1 2023-02-06 4:10 下午 mayucong
 */
public interface AppreciationOpeningRecordService {

    /**
     * 分页查询增值服务开通记录列表
     *
     * @param param
     * @return {@link PageResult<GetAppreciationOpeningInfoModel>}
     * <AUTHOR>
     */
    PageResult<GetAppreciationOpeningInfoModel> pageQueryOpeningRecord(PageParam<QueryOpeningRecordParam> param);

    /**
     * 查询增值服务开通信息
     *
     * @param param
     * @return {@link GetAppreciationOpeningInfoModel}
     * <AUTHOR>
     */
    GetAppreciationOpeningInfoModel getAppreciationOpeningInfo(GetAppreciationOpeningInfoParam param);

    /**
     * 查询（商家）增值服务开通信息
     *
     * @param param
     * @return {@link GetMerchantOpeningStatusModel}
     * <AUTHOR>
     */
    GetMerchantOpeningStatusModel getMerchantOpening(GetMerchantOpeningStatusParam param);
}
