/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.merchant.MerchantAgentGetModel;
import com.huike.nova.service.domain.model.merchant.QueryMerchantOfAgentModel;
import com.huike.nova.service.domain.param.merchant.MerchantAgentGetParam;
import com.huike.nova.service.domain.param.merchant.QueryMerchantOfAgentParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version MerchantAgentService.java, v 0.1 2022-12-05 11:05 zhangling
 */
public interface MerchantAgentService {

    /**
     * 获取商户对应的服务商手机号
     *
     * @param param
     * @return
     */
    MerchantAgentGetModel getAgentPhone(MerchantAgentGetParam param);

    /**
     * 查询代理商下所有商家
     *
     * @param param
     * @return {@link List <QueryMerchantOfAgentModel>}
     * <AUTHOR>
     */
    List<QueryMerchantOfAgentModel> findByAgentMcnId(QueryMerchantOfAgentParam param);

    /**
     * 分页查询代理商下所有商家
     *
     * @param param
     * @return {@link PageResult<QueryMerchantOfAgentModel>}
     * <AUTHOR>
     */
    PageResult<QueryMerchantOfAgentModel> pageListByAgentMcnId(PageParam<QueryMerchantOfAgentParam> param);
}