package com.huike.nova.service.business.startask.web;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.dao.domain.param.startask.ApplyAuditRecordsPageListDTO;
import com.huike.nova.dao.domain.param.startask.WebApplyListParamDTO;
import com.huike.nova.dao.domain.param.startask.WebTaskListParamDTO;
import com.huike.nova.service.domain.model.startask.web.task.ApplyAuditRecordsPageListModel;
import com.huike.nova.service.domain.model.startask.web.task.FindOperationRecordListModel;
import com.huike.nova.service.domain.model.startask.web.task.GetTaskDetailModel;
import com.huike.nova.service.domain.model.startask.web.task.WebApplyListModel;
import com.huike.nova.service.domain.model.startask.web.task.WebTaskListModel;
import com.huike.nova.service.domain.param.startask.web.task.AddOrUpdateOperationRecordParam;
import com.huike.nova.service.domain.param.startask.web.task.ApplyAuditRecordsExcelParam;
import com.huike.nova.service.domain.param.startask.web.task.ApplyAuditRecordsPageListParam;
import com.huike.nova.service.domain.param.startask.web.task.FindOperationRecordListParam;
import com.huike.nova.service.domain.param.startask.web.task.GetTaskDetailParam;
import com.huike.nova.service.domain.param.startask.web.task.ModifyTaskAssigneeParam;
import com.huike.nova.service.domain.param.startask.web.task.OperateTaskParam;
import com.huike.nova.service.domain.param.startask.web.task.TaskApplyStatusOperationParam;
import com.huike.nova.service.domain.param.startask.web.task.WebApplyListParam;
import com.huike.nova.service.domain.param.startask.web.task.WebTaskListParam;
import io.reactivex.Observable;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年12月08日 15:22
 */
public interface StarTaskWebTaskService {

    /**
     * 任务管理列表
     *
     * @param param 入参
     * @return 出参
     */
    PageResult<WebTaskListModel> list(PageParam<WebTaskListParam> param);

    /**
     * 任务详情
     *
     * @param param 入参
     * @return 出参
     */
    GetTaskDetailModel getTaskDetail(GetTaskDetailParam param);

    /**
     * 任务操作
     *
     * @param param 入参
     */
    void operateTask(OperateTaskParam param);

    /**
     * 报名清单列表
     *
     * @param param 入参
     * @return 出参
     */
    PageResult<WebApplyListModel> applyList(PageParam<WebApplyListParam> param);

    /**
     * 报名清单审核记录分页列表
     *
     * @param param 入参
     * @return 出参
     */
    PageResult<ApplyAuditRecordsPageListModel> applyAuditRecordsPageList(PageParam<ApplyAuditRecordsPageListParam> param);

    /**
     * 报名清单状态操作
     *
     * @param param 入参
     */
    void applyStatusOperation(TaskApplyStatusOperationParam param);

    /**
     * 列表导出
     *
     * @param param 入参
     */
    void requestExport(WebTaskListParam param);

    /**
     * 报名清单导出
     *
     * @param param 入参
     */
    void requestExportApplyList(WebApplyListParam param);

    /**
     * 任务列表导出（异步任务）
     *
     * @param fileName 文件名称
     * @param param    参数
     * @return rxJava Observable对象
     */
    Observable<File> export(String fileName, WebTaskListParamDTO param);

    /**
     * 任务报名清单导出（异步任务）
     *
     * @param fileName 文件名称
     * @param param    参数
     * @return rxJava Observable对象
     */
    Observable<File> exportApplyList(String fileName, WebApplyListParamDTO param);

    /**
     * 报名清单审核记录导出（异步任务）
     *
     * @param param 入参
     */
    void applyAuditRecordsExcel(ApplyAuditRecordsExcelParam param);

    /**
     * 报名审核记录导出（异步任务）
     *
     * @param taskName 任务名称
     * @param param    入参
     * @return rxJava Observable对象
     */
    Observable<File> exportApplyAuditRecord(String taskName, ApplyAuditRecordsPageListDTO param);

    /**
     * 修改任务跟进人
     *
     * @param param
     */
    void modifyTaskAssignee(ModifyTaskAssigneeParam param);

    /**
     * 运营记录列表
     *
     * @param param
     * @return
     */
    List<FindOperationRecordListModel> findOperationRecordList(FindOperationRecordListParam param);

    /**
     * 添加/修改运营记录
     *
     * @param param
     */
    void addOrUpdateOperationRecord(AddOrUpdateOperationRecordParam param);
}
