package com.huike.nova.service.jobhandler;

import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.util.TraceIdGenerator;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

/**
 * JobHandler基类，包装了一些基本方法
 *
 * <AUTHOR> (<EMAIL>)
 * @version AbsJobHandler.java, v1.0 10/01/2024 09:33 John Exp$
 */
@Slf4j
public abstract class AbsJobHandler extends IJobHandler {
    /**
     * 获得或者生成一个新的TraceId
     *
     * @return traceId
     */
    protected String getOrGenerateTraceId() {
        String traceId = MDC.get(CommonConstant.TRACE_ID);
        if (StringUtils.isBlank(traceId)) {
            traceId = TraceIdGenerator.generate();
            MDC.put(CommonConstant.TRACE_ID, traceId);
        }
        return traceId;
    }
}
