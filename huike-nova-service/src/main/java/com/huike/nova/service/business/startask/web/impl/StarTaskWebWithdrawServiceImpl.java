package com.huike.nova.service.business.startask.web.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.constant.startask.agentweb.StarTaskAgentWebRedisPrefixConstant;
import com.huike.nova.common.constant.startask.mina.StarTaskMinaRedisPrefixConstant;
import com.huike.nova.common.constant.startask.web.StarTaskWebRedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.startask.WithdrawTypeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum;
import com.huike.nova.common.enums.startask.mina.WithdrawApplyStatusEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogRemarkTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.common.util.ExcelWriterHelper;
import com.huike.nova.common.util.ExceptionUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.dao.domain.param.startask.WithdrawPageListParamDTO;
import com.huike.nova.dao.domain.result.startask.WithdrawPageListResultDTO;
import com.huike.nova.dao.entity.StarTaskBalanceAccountDO;
import com.huike.nova.dao.entity.StarTaskBalanceLogDO;
import com.huike.nova.dao.entity.StarTaskWithdrawApplyDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.StarTaskAgentAccountDAO;
import com.huike.nova.dao.repository.StarTaskBalanceAccountDAO;
import com.huike.nova.dao.repository.StarTaskBalanceLogDAO;
import com.huike.nova.dao.repository.StarTaskWithdrawApplyDAO;
import com.huike.nova.service.business.startask.web.StarTaskWebExportService;
import com.huike.nova.service.business.startask.web.StarTaskWebWithdrawService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.web.StarTaskWebWithdrawServiceObjMapper;
import com.huike.nova.service.domain.model.startask.web.login.StarTaskWebLoginModel;
import com.huike.nova.service.domain.model.startask.web.merchant.WithdrawPageListModel;
import com.huike.nova.service.domain.param.startask.web.merchant.WithdrawApplyStatusOperationParam;
import com.huike.nova.service.domain.param.startask.web.merchant.WithdrawPageListParam;
import com.huike.nova.service.domain.result.startask.web.StarTaskWithdrawalApplyListResult;
import io.reactivex.Observable;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023年12月11日 09:54
 */
@Slf4j
@Service
@AllArgsConstructor
public class StarTaskWebWithdrawServiceImpl implements StarTaskWebWithdrawService {

    private StarTaskWithdrawApplyDAO starTaskWithdrawApplyDAO;

    private StarTaskWebWithdrawServiceObjMapper starTaskWebWithdrawServiceObjMapper;

    private StarTaskAgentAccountDAO starTaskAgentAccountDAO;

    private StarTaskWebExportService starTaskWebExportService;

    private StarTaskBalanceAccountDAO starTaskBalanceAccountDAO;

    private RedissonClient redissonClient;

    private TransactionTemplate transactionTemplate;

    private StarTaskBalanceLogDAO starTaskBalanceLogDAO;


    /**
     * 提现申请分页列表
     *
     * @param param 入参
     * @return 出参
     */
    @Override
    public PageResult<WithdrawPageListModel> pageList(PageParam<WithdrawPageListParam> param) {
        // 获取登录信息
        PageParam<WithdrawPageListParamDTO> pageParam = starTaskWebWithdrawServiceObjMapper.toWithdrawPageListParamDTO(param);
        WithdrawPageListParamDTO paramQuery = pageParam.getQuery();
        String phoneNumber = paramQuery.getPhoneNumber();
        if (StringUtils.isNotBlank(phoneNumber)) {
            paramQuery.setPhoneNumber(FieldEncryptUtil.encode(phoneNumber));
        }
        Page<WithdrawPageListResultDTO> activityPage = starTaskWithdrawApplyDAO.pageList(pageParam);
        List<WithdrawPageListResultDTO> resultDTOList = activityPage.getRecords();
        if (CollectionUtil.isNotEmpty(resultDTOList)) {
            for (WithdrawPageListResultDTO item : resultDTOList) {
                item.setPhoneNumber(FieldEncryptUtil.decode(item.getPhoneNumber()));
                // 区代佣金类型手机号切换
                if (WithdrawTypeEnum.AGENT_COMMISSION.getValue().equals(paramQuery.getWithdrawType())) {
                    item.setPhoneNumber(FieldEncryptUtil.decode(item.getAgentPhoneNumber()));
                }
                String remitTime = item.getRemitTime();
                if (CommonConstant.INITIAL_TIME_STR.equals(remitTime)) {
                    item.setRemitTime(StringPool.EMPTY);
                }
                // 身份证解密
                item.setIdCardNo(FieldEncryptUtil.decode(item.getIdCardNo()));
            }
        }
        return starTaskWebWithdrawServiceObjMapper.toWithdrawPageListModel(activityPage);
    }

    @Override
    public void requestExport(WithdrawPageListParam param) {
        val requestParam = starTaskWebWithdrawServiceObjMapper.toWithdrawPageListParamDTO(param);
        starTaskWebExportService.applyExportWithdrawalList(requestParam, LoginUtil.getWebStarTaskLoginBasicInfo());
    }

    /**
     * 提现申请状态操作
     *
     * @param param 入参
     */
    @Override
    public void applyStatusOperation(WithdrawApplyStatusOperationParam param) {
        List<String> applyCodeList = param.getApplyCodeList();
        Integer applyStatus = this.checkOperateType(applyCodeList, param.getOperateType());
        // 获取登录信息
        StarTaskWebLoginModel loginInfo = LoginUtil.getWebStarTaskLoginBasicInfo();
        for (String applyCode : applyCodeList) {
            // 审核提现申请单
            auditWithdrawApply(applyCode, applyStatus, loginInfo);
        }
    }

    private Integer checkOperateType(List<String> applyCodeList, Integer operateType) {
        Integer applyStatus;
        // 打款状态判断
        if (CommonConstant.INTEGER_ONE.equals(operateType)) {
            applyStatus = WithdrawApplyStatusEnum.PAYMENT_SUCCESSFUL.getValue();
        } else if (CommonConstant.INTEGER_TWO.equals(operateType)) {
            applyStatus = WithdrawApplyStatusEnum.PAYMENT_FAILED.getValue();
        } else {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("操作类型错误");
        }
        List<StarTaskWithdrawApplyDO> applyDOList = starTaskWithdrawApplyDAO.findWaitingForPaymentListByApplyCodeList(applyCodeList);
        if (applyCodeList.size() != applyDOList.size()) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("状态已发生变更，请刷新后再试");
        }
        return applyStatus;
    }

    /**
     * 审核提现申请单
     *
     * @param applyCode   申请单号
     * @param applyStatus 变更状态
     * @param loginInfo   登录信息
     */
    private void auditWithdrawApply(String applyCode, Integer applyStatus, StarTaskWebLoginModel loginInfo) {
        LogUtil.info(log, "StarTaskWebWithdrawServiceImpl.auditWithdrawApply >> 接口开始 >> applyCode = {},applyStatus = {},loginInfo, = {}", applyCode, applyStatus, loginInfo);
        RLock applyCodeLock = redissonClient.getLock(StrUtil.format(StarTaskWebRedisPrefixConstant.STAR_TASK_WEB_KEY_APPLY_WITHDRAWAL, applyCode));
        try {
            if (!applyCodeLock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                LogUtil.info(log, "StarTaskWebWithdrawServiceImpl.auditWithdrawApply >> 锁获取失败 >> applyCode = {}", applyCode);
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("锁获取失败");
            }
            StarTaskWithdrawApplyDO withdrawApplyDO = starTaskWithdrawApplyDAO.getWithdrawApplyByApplyCode(applyCode);
            // 判断更改前状态是否为待打款
            Integer changeFrontApplyStatus = withdrawApplyDO.getApplyStatus();
            if (!WithdrawApplyStatusEnum.WAITING_FOR_PAYMENT.getValue().equals(changeFrontApplyStatus)) {
                return;
            }
            String identityId = withdrawApplyDO.getIdentityId();
            RLock lock = redissonClient.getLock(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_APPLY_WITHDRAWAL, identityId));
            try {
                if (!lock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES)) {
                    LogUtil.info(log, "StarTaskWebWithdrawServiceImpl.auditWithdrawApply >> 锁获取失败 >> identityId = {}", identityId);
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("锁获取失败");
                }
                // 事务
                transactionTemplate.execute(status -> {
                    // 操作状态
                    starTaskWithdrawApplyDAO.applyStatusOperation(applyCode, applyStatus, loginInfo.getOperatorId(), loginInfo.getContactName());
                    changeWithdrawApplyAccount(withdrawApplyDO, applyStatus);
                    return Boolean.TRUE;
                });
            } catch (CommonException commonException) {
                throw commonException;
            } catch (Exception e) {
                LogUtil.info(log, "StarTaskWebWithdrawServiceImpl.auditWithdrawApply >> 锁定identityId全局异常 >> identityId = {},e={}", identityId, e);
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("审核失败");
            } finally {
                try {
                    // 锁不为空  是否还是锁定状态  当前执行线程的锁
                    if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                } catch (Exception e) {
                    LogUtil.info(log, "StarTaskWebWithdrawServiceImpl.auditWithdrawApply >> identityId锁释放失败");
                }
            }
        } catch (CommonException commonException) {
            throw commonException;
        } catch (Exception e) {
            LogUtil.info(log, "StarTaskWebWithdrawServiceImpl.auditWithdrawApply >> applyCode全局异常 ", e);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("审核异常");
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (applyCodeLock != null && applyCodeLock.isLocked() && applyCodeLock.isHeldByCurrentThread()) {
                    applyCodeLock.unlock();
                }
            } catch (Exception e) {
                LogUtil.info(log, "StarTaskWebWithdrawServiceImpl.auditWithdrawApply >> applyCode锁释放失败");
            }
        }
    }

    /**
     * 变更提现申请账户
     *
     * @param withdrawApplyDO 提现申请单
     * @param applyStatus     更改状态
     */
    private void changeWithdrawApplyAccount(StarTaskWithdrawApplyDO withdrawApplyDO, Integer applyStatus) {
        LogUtil.info(log, "StarTaskWebWithdrawServiceImpl.changeWithdrawApplyAccount >> 变更提现申请账户接口开始");
        String identityId = withdrawApplyDO.getIdentityId();
        StarTaskBalanceAccountDO accountDO = starTaskBalanceAccountDAO.getBalanceAccountByIdentityId(identityId);
        if (Objects.isNull(accountDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账户信息不存在");
        }
        // 可提现金额
        BigDecimal availableBalance = BigDecimal.ZERO;
        // 奖励金余额
        BigDecimal rewardBalance = BigDecimal.ZERO;
        boolean flag = WithdrawTypeEnum.REWARD.getValue().equals(withdrawApplyDO.getWithdrawType());
        // 变更余额
        BigDecimal withdrawalFrozenBalance = withdrawApplyDO.getWithdrawAmount();
        if (WithdrawApplyStatusEnum.PAYMENT_FAILED.getValue().equals(applyStatus)) {
            // 加到可提现金额上
            availableBalance = withdrawalFrozenBalance;
            // 打款失败变更金额换为负数
            withdrawalFrozenBalance = withdrawalFrozenBalance.negate();
            if (flag) {
                // 奖励金余额
                rewardBalance = withdrawApplyDO.getWithdrawAmount();
            }
        }
        StarTaskBalanceLogDO starTaskBalanceLogDO = starTaskBalanceLogDAO.queryLastByRelationNo(flag ? BalanceLogRemarkTypeEnum.REWARD_WITHDRAW.getValue() : BalanceLogRemarkTypeEnum.WITHDRAW.getValue(), withdrawApplyDO.getApplyCode());
        if (Objects.isNull(starTaskBalanceLogDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("记录表数据为空");
        }
        starTaskBalanceLogDO.setUpdateTime(new Date());
        starTaskBalanceLogDO.setChangeExpenditureTotalIncome(accountDO.getExpenditureTotalIncome());
        starTaskBalanceLogDO.setAfterExpenditureTotalIncome(accountDO.getExpenditureTotalIncome());
        starTaskBalanceLogDO.setChangeIncomeTotalIncome(accountDO.getIncomeTotalIncome());
        starTaskBalanceLogDO.setAfterIncomeTotalIncome(accountDO.getIncomeTotalIncome());
        starTaskBalanceLogDO.setChangeAvailableBalance(accountDO.getAvailableBalance());
        starTaskBalanceLogDO.setChangeWithdrawalFrozenBalance(accountDO.getWithdrawalFrozenBalance());
        starTaskBalanceLogDO.setAfterWithdrawalFrozenBalance(accountDO.getWithdrawalFrozenBalance().subtract(withdrawApplyDO.getWithdrawAmount()));

        if (WithdrawApplyStatusEnum.PAYMENT_SUCCESSFUL.getValue().equals(applyStatus)) {
            starTaskBalanceLogDO.setAfterAvailableBalance(accountDO.getAvailableBalance());
        } else if (WithdrawApplyStatusEnum.PAYMENT_FAILED.getValue().equals(applyStatus)) {
            starTaskBalanceLogDO.setAfterAvailableBalance(accountDO.getAvailableBalance().add(withdrawApplyDO.getWithdrawAmount()));
        } else {
            return;
        }
        LogUtil.info(log, "StarTaskWebWithdrawServiceImpl.changeWithdrawApplyAccount >> 变更提现申请账户数据库更新,availableBalance={},withdrawalFrozenBalance={},rewardBalance={}", availableBalance, withdrawalFrozenBalance, rewardBalance);
        starTaskBalanceLogDAO.updateById(starTaskBalanceLogDO);
        starTaskBalanceAccountDAO.updateWithdrawAmountAndWithdrawalFrozenAmount(identityId, availableBalance, withdrawalFrozenBalance, rewardBalance);
    }

    @Override
    public Observable<File> export(String fileName, WithdrawPageListParamDTO param) {
        return Observable.create(emitter -> {
            try (val writer = ExcelWriterHelper.create(fileName, StarTaskWithdrawalApplyListResult.class)) {
                // 设置读取参数
                starTaskWithdrawApplyDAO.export(param, resultContext -> {
                    if (Objects.isNull(resultContext)) {
                        return;
                    }
                    val data = resultContext.getResultObject();
                    StarTaskWithdrawalApplyListResult result = starTaskWebWithdrawServiceObjMapper.toStarTaskWithdrawalApplyListResult(data);
                    result.setPhoneNumber(FieldEncryptUtil.decode(result.getPhoneNumber()));
                    String agentPhoneNumber = data.getAgentPhoneNumber();
                    // 区代佣金类型手机号切换
                    if (StringUtils.isNotBlank(agentPhoneNumber)) {
                        result.setPhoneNumber(FieldEncryptUtil.decode(agentPhoneNumber));
                    }
                    result.setIdCardNo(FieldEncryptUtil.decode(result.getIdCardNo()));
                    // 打款状态
                    val applyStatusEnum = WithdrawApplyStatusEnum.getByValue(data.getApplyStatus());
                    if (Objects.nonNull(applyStatusEnum)) {
                        result.setApplyStatusName(applyStatusEnum.getName());
                    }
                    val identityTypeEnum = StarTaskIdentityTypeEnum.getByValue(data.getIdentityType());
                    if (Objects.nonNull(identityTypeEnum)) {
                        result.setIdentityTypeName(identityTypeEnum.getName());
                    }
                    if (CommonConstant.INITIAL_TIME_STR.equals(data.getRemitTime())) {
                        result.setRemitTime(StringPool.EMPTY);
                    }
                    writer.write(result);
                });
                emitter.onNext(writer.finish());
                emitter.onComplete();
            } catch (Exception ex) {
                emitter.onError(ex);
            }
        });
    }

    /**
     * 提现申请状态操作(区代佣金)
     *
     * @param param
     */
    @Override
    public void operationApplyStatusToAgent(WithdrawApplyStatusOperationParam param) {
        List<String> applyCodeList = param.getApplyCodeList();
        // 校验操作类型
        Integer applyStatus = this.checkOperateType(applyCodeList, param.getOperateType());
        // 获取登录信息
        StarTaskWebLoginModel loginInfo = LoginUtil.getWebStarTaskLoginBasicInfo();
        for (String applyCode : applyCodeList) {
            String lock = StrUtil.format(StarTaskWebRedisPrefixConstant.STAR_TASK_WEB_KEY_APPLY_WITHDRAWAL, applyCode);
            try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
                redisLockHelper.tryLock();
                // 审核提现申请单
                this.auditAgentWithdrawApply(applyCode, applyStatus, loginInfo);
            } catch (Exception e) {
                LogUtil.warn(log, "StarTaskWebWithdrawServiceImpl" + " >>>>> " + "auditAgentWithdrawApply" + "  锁释放失败", e);
                throw ExceptionUtil.toCommonException(e);
            }
        }
    }

    private void auditAgentWithdrawApply(String applyCode, Integer applyStatus, StarTaskWebLoginModel loginInfo) {
        // 业务逻辑
        StarTaskWithdrawApplyDO withdrawApplyDO = starTaskWithdrawApplyDAO.getWithdrawApplyByApplyCode(applyCode);
        // 判断更改前状态是否为待打款
        Integer changeFrontApplyStatus = withdrawApplyDO.getApplyStatus();
        if (!WithdrawApplyStatusEnum.WAITING_FOR_PAYMENT.getValue().equals(changeFrontApplyStatus)) {
            return;
        }
        String agentId = withdrawApplyDO.getAgentId();
        if (StringUtils.isBlank(agentId)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("区代信息不能为空");
        }
        String lock = StrUtil.format(StarTaskAgentWebRedisPrefixConstant.STAR_TASK_AGENT_ACCOUNT_CACHE_KEY, agentId);
        try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
            redisLockHelper.tryLock();
            // 事务
            transactionTemplate.execute(status -> {
                // 操作状态
                starTaskWithdrawApplyDAO.applyStatusOperation(applyCode, applyStatus, loginInfo.getOperatorId(), loginInfo.getContactName());
                BigDecimal availableBalance = BigDecimal.ZERO;
                BigDecimal withdrawalFrozenBalance = withdrawApplyDO.getWithdrawAmount().negate();
                if (WithdrawApplyStatusEnum.PAYMENT_FAILED.getValue().equals(applyStatus)) {
                    availableBalance = withdrawApplyDO.getWithdrawAmount().negate();
                }
                // 修改金额
                starTaskAgentAccountDAO.updateWithdrawAmount(agentId, availableBalance, withdrawalFrozenBalance);
                return Boolean.TRUE;
            });
        } catch (Exception e) {
            LogUtil.warn(log, "StarTaskWebWithdrawServiceImpl" + " >>>>> " + "auditAgentWithdrawApply" + "  锁释放失败", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }
}
