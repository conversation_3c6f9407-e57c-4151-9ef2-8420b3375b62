/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.service.domain.model.material.CategoryListModel;

/**
 * <AUTHOR>
 * @version CategoryService.java, v 0.1 2022-09-08 11:13 zhangling
 */
public interface CategoryService {

    /**
     * 查询活动类目场景
     * @param categoryId
     * @return
     */
    CategoryListModel findCategoryByCategoryId(String categoryId);
}