/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.startask.web.merchant.DefaultDistributionRateModel;
import com.huike.nova.service.domain.model.startask.web.merchant.QueryDefaultTaskMoneyModel;
import com.huike.nova.service.domain.model.startask.web.merchant.WebMerchantListModel;
import com.huike.nova.service.domain.param.startask.web.merchant.UpdateTaskMoneyParam;
import com.huike.nova.service.domain.param.startask.web.merchant.WebMerchantListParam;

/**
 * <AUTHOR>
 * @version AgentWebMerchantService.java, v 0.1 2024-05-22 10:32 AM ruanzy
 */
public interface AgentWebMerchantService {

    /**
     * 商家列表
     *
     * @param param 入参
     * @return 出参
     */
    PageResult<WebMerchantListModel> list(PageParam<WebMerchantListParam> param);

    /**
     * 商家列表导出
     *
     * @param param 入参
     */
    void requestExport(WebMerchantListParam param);

    /**
     * 查询默认任务金
     *
     * @return
     */
    QueryDefaultTaskMoneyModel queryDefaultTaskMoney();

    /**
     * 查询默认的分销比例
     *
     * @return
     */
    DefaultDistributionRateModel findDefaultDistributionRate();

    /**
     * 更改商家任务金限制
     *
     * @param param
     */
    void updateTaskMoney(UpdateTaskMoneyParam param);
}