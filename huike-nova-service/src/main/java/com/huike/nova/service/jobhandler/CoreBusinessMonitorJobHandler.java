package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.google.common.base.Joiner;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.util.TraceIdGenerator;
import com.huike.nova.service.business.CoreBusinessMonitorService;
import com.huike.nova.service.business.common.DingDingCommonService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Consumer;

/**
 * 核心业务监控服务
 *
 * <AUTHOR> (<EMAIL>)
 * @version CoreBizMonitorJobHandler.java, v1.0 06/14/2023 16:36 John Exp$
 */
@Component
@Slf4j
@JobHandler("coreBusinessMonitorJobHandler")
@AllArgsConstructor
public class CoreBusinessMonitorJobHandler extends IJobHandler {

    private CoreBusinessMonitorService service;

    private DingDingCommonService dingDingCommonService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        MDC.put(CommonConstant.TRACE_ID, TraceIdGenerator.generate());
        XxlJobLogger.log("CoreBusinessMonitorJobHandler.execute >> 核心业务监控开始：time = {}", DateUtil.now());
        List<String> notifyList = Lists.newArrayList();
        // 检查抖音视频合成业务
        try {
            service.checkDouyinPublishVideoCallback(notifyList::add);
        } catch (Exception e) {
            XxlJobLogger.log("CoreBusinessMonitorJobHandler.execute >> 抖音发布视频检查异常：错误原因: {}", e);
        }

        try {
            service.checkAliyunIceCallback(notifyList::add);
        } catch (Exception e) {
            XxlJobLogger.log("CoreBusinessMonitorJobHandler.execute >> 阿里云视频合成检查异常：错误原因: {}", e);
        }
        val content = Joiner.on("<br/>").join(notifyList);
        dingDingCommonService.sendCallbackNotificationMessage("回调通知", "视频回调通知", content);

        XxlJobLogger.log("CoreBusinessMonitorJobHandler.execute >> 核心业务监控结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}
