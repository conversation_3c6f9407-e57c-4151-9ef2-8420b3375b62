/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.alibaba.fastjson.JSONObject;
import com.huike.nova.dao.domain.result.WechatGrouponDailyAmountDTO;
import com.huike.nova.service.domain.param.activity.DayStatisticsActivityDayParam;
import com.huike.nova.service.domain.param.job.DouyinLifeRpaAfterSaleRefundParam;
import com.huike.nova.service.domain.param.qyk.mina.tools.MerchantOrderSyncJobParam;
import com.huike.nova.service.domain.param.qyk.mina.tools.MerchantOrderSyncParam;
import com.huike.nova.service.domain.param.qyk.mina.tools.QykToolsSaveCardParam;
import com.huike.nova.service.domain.param.qyk.mina.tools.SaveSpVerifyCouponRecordFromBCouponVerifyRecordParam;

import javax.annotation.Nullable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version TaskService.java, v 0.1 2022-09-07 9:32 AM ruanzy
 */
public interface TaskService {

    /**
     * 每日活动数据统计
     *
     * @param param
     */
    void statisticsActivityDay(String param);

    /**
     * 每日活动数据统计
     *
     * @param param 参数
     */
    void statisticsActivityDayV2(DayStatisticsActivityDayParam param);

    /**
     * 抖音话题刷新脚本
     */
    void tiktokTopicRefreshJob();

    /**
     * 抖音热词刷新脚本
     */
    void tiktokSentenceRefreshJob();

    /**
     * 用户抖音视频数据刷新脚本
     *
     * @param startTime
     * @param endTime
     */
    void userVideoDataRefreshJob(String startTime, String endTime);

    /**
     * 用户抖音视频刷新脚本
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void userVideoDataRefreshJobV3(String startTime, String endTime);

    /**
     * 同步素材人声
     */
    void syncIceVoiceData();

    /**
     * 测试异步线程
     */
    void async();

    /**
     * 退款订单同步状态
     */
    void refundOrderSyncStatusJob();

    /**
     * 抖音订单支付失败状态订正
     */
    void correctionDouyinOrderPayFailed(@Nullable Date startTime, @Nullable Date endTime);

    /**
     * 每日商品数据统计
     *
     * @param param
     */
    void statisticsGoodsDay(String param);

    /**
     * 每日核销数据统计
     *
     * @param s
     */
    void statisticsVerifyDay(String s);

    /**
     * 达人账号过期通知
     */
    void starAccountExpireNotice();

    /**
     * 日统计外卖订单
     */
    void countDayTakeoutOrder();

    /**
     * 刷新抖音refresh_token
     */
    void tiktokRefreshToken();

    /**
     * 刷新达人推发布状态
     *
     * @param supActivityId 指定某一个达人推活动
     */
    void refreshStarSupActivityStatus(String supActivityId);

    /**
     * 按日统计达人推数据
     *
     * @param supActivityId 指定某一个达人推活动
     */
    void statisticsStarSupActivityDayData(String supActivityId, Date calculateDay);

    /**
     * 按日统计达人推数据
     */
    void statisticsStarSupActivityDayDataV2(Date calculateDay);

    /**
     * 新增客资信息
     */
    void saveCustomerData();

    List<String> onceBatchDealHistoryStarPushData(List<String> supActivityIdList);

    /**
     * 新增库存
     */
    void saveStock();

    /**
     * 修复订单快照
     *
     * @param s
     */
    void repairOrderSnapshot(String s);

    /**
     * 每日活动数据统计脚本修复
     *
     * @param param
     */
    void repairActivityStatistics(String param);

    /**
     * 客资宝商品数据同步
     *
     * @param customerDataId
     */
    void customerProductDataSync(String customerDataId);


    void fixStarSupActivityVideoReleaseCount(Date startTime, Date endTime, String supActivityId);

    /**
     * 来探呗小程序-身份认证认证状态清除
     *
     * @param phone
     * @param identityType
     * @param appletId
     */
    void updateAuthenticate(String phone, Integer identityType, String appletId);

    /**
     * 来探呗小程序-达人身份解绑
     *
     * @param relationId
     */
    void operateStarUnbind(String relationId);

    /**
     * 自动结束达人报名超时任务请求
     *
     * @param taskIdList
     * @param lastDayNum
     */
    void autoCancelStarApply(List<String> taskIdList,Integer lastDayNum);

    /**
     * 保存用户权益卡信息
     *
     * @param outOrderSn
     */
    void saveUserQykCard(String outOrderSn);

    /**
     * 保存用户商圈卡信息（测试方法）
     *
     * @param param 保存商圈卡参数
     */
    void saveToolsUserQykCard(QykToolsSaveCardParam param);

    /**
     * 删除测试的用户商圈卡
     *
     * @param outOrderSnList 外部订单号
     */
    JSONObject deleteDebugUserQykCard(List<String> outOrderSnList);

    /**
     * 批量生成生成商圈卡
     *
     * @param outOrderSnList 外部订单Id
     */
    void batchSaveUserQykCard(List<String> outOrderSnList);

    /**
     * 修复视频播放数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void fixVideoReleaseCount(String startTime, String endTime);

    /**
     * 检查所有素材不足且手动合成的达人推活动
     */
    void checkInsufficientMaterialStarSupActivityJobHandler();

    /**
     * 发送订阅消息
     */
    void sendSubscribe();

    /**
     * 修复服务商验券表的卡券核销记录
     *
     * @param startTime 开始时间，格式为yyyy-MM-dd
     * @param endTime 结束时间，格式为yyyy-MM-dd
     * @return 更新条数
     */
    void fillUpServiceProviderCouponRecordPhoneNumber(String startTime, String endTime);

    /**
     * 同步抖音订单
     *
     * @param param 请求参数
     */
    void synchronizeDouyinOrders(MerchantOrderSyncParam param);

    /**
     * 同步抖音订单任务
     *
     * @param param 请求参数
     */
    void synchronizeDouyinOrdersTask(MerchantOrderSyncJobParam param);

    /**
     * 自动同步抖音订单
     *
     * @param callFromSyncJob 是否来自同步任务
     */
    void autoSyncDouyinOrders(boolean callFromSyncJob);

    /**
     * 用户服务修复外部订单
     *
     * @param accountId 抖音来客Id
     * @param originOutOrderSn 外部订单号
     */
    void taskServiceFixOutOrderSn(String accountId, String originOutOrderSn);

    /**
     * 测试更新库
     */
    void testUpdateAll();

    /**
     * 猫酷会员订单退款积分扣减
     *
     * @param startTime
     *
     */
    void mallCooReduceAddPoints(String startTime);

    /**
     * 从核销记录中找到表写入服务商核销记录中
     *
     * @param param 参数
     */
    void saveServiceProviderCouponServiceFromVerifyRecords(SaveSpVerifyCouponRecordFromBCouponVerifyRecordParam param);

    /**
     * 抖音来客核销后退款订单同步
     *
     * @param param 售后单获取参数
     */
    void syncDouyinLifeAfterSaleMerchantRefundList(DouyinLifeRpaAfterSaleRefundParam param);

    /**
     * 处理抖音来客核销后退款状态
     */
    void handleDouyinLifeAfterSaleRefundApply();

    /**
     * 微信每日退款提醒
     *
     * @param date 统计日期
     * @param atList AT列表：需要通知的人
     */
    WechatGrouponDailyAmountDTO wechatDailyGrouponRefundNotice(String date, List<String> atList);
}