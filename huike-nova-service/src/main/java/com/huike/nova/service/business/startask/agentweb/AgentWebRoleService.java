/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.oem.operation.employee.PageQueryRoleModel;
import com.huike.nova.service.domain.model.oem.operation.employee.QueryAllGrantModel;
import com.huike.nova.service.domain.model.oem.operation.employee.SelectRoleListModel;
import com.huike.nova.service.domain.model.startask.agentweb.role.AgentWebEmployeeDetailModel;
import com.huike.nova.service.domain.model.startask.agentweb.role.AgentWebPageEmployeeListModel;
import com.huike.nova.service.domain.param.oem.operation.employee.AddOrUpdateRoleParam;
import com.huike.nova.service.domain.param.startask.agentweb.role.AgentWebEmployeeDetailParam;
import com.huike.nova.service.domain.param.startask.agentweb.role.AgentWebOperateEmployeeParam;
import com.huike.nova.service.domain.param.startask.agentweb.role.AgentWebUpdateEmployeeParam;
import com.huike.nova.service.domain.param.startask.web.role.StarTaskAddEmployeeParam;
import com.huike.nova.service.domain.param.startask.web.role.StarTaskPageEmployeeListParam;

/**
 * <AUTHOR>
 * @version AgentWebRoleService.java, v 0.1 2024-05-21 4:23 PM ruanzy
 */
public interface AgentWebRoleService {

    /**
     * 新增员工
     *
     * @param param
     */
    void addEmployee(StarTaskAddEmployeeParam param);

    /**
     * 修改员工
     *
     * @param param
     */
    void updateEmployee(AgentWebUpdateEmployeeParam param);

    /**
     * 分页查询员工列表
     *
     * @param param
     * @return
     */
    PageResult<AgentWebPageEmployeeListModel> pageEmployeeList(PageParam<StarTaskPageEmployeeListParam> param);

    /**
     * 员工详情
     *
     * @param param
     * @return
     */
    AgentWebEmployeeDetailModel getEmployeeDetail(AgentWebEmployeeDetailParam param);

    /**
     * 员工操作
     *
     * @param param
     */
    void operateEmployee(AgentWebOperateEmployeeParam param);

    /**
     * 分页查看角色列表
     *
     * @param param
     * @return
     */
    PageResult<PageQueryRoleModel> pageQueryRole(PageParam param);

    /**
     * 角色权限列表
     *
     * @return
     */
    QueryAllGrantModel queryAllGrant();

    /**
     * 新增或修改角色
     *
     * @param param
     */
    void addOrUpdateRole(AddOrUpdateRoleParam param);

    /**
     * 下拉获取角色列表
     *
     * @return
     */
    SelectRoleListModel selectRoleList();
}