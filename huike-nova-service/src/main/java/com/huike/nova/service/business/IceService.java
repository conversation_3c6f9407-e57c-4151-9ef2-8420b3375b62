/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.service.domain.model.ice.MaterialSyntheticModel;
import com.huike.nova.service.domain.model.ice.MediaProducingJobModel;
import com.huike.nova.service.domain.param.ice.IceCallbackParam;
import com.huike.nova.service.domain.param.ice.MaterialSyntheticParam;
import com.huike.nova.service.domain.param.ice.MaterialUploadParam;
import com.huike.nova.service.domain.param.sundry.RepairIceCallbackParam;

/**
 * <AUTHOR>
 * @version IceService.java, v 0.1 2022-09-05 10:45 AM ruanzy
 */
public interface IceService {

    /**
     * 上传ICE素材
     *
     * @param param
     * @return
     */
    String iceMaterialUpload(MaterialUploadParam param);

    /**
     * 素材合成视频
     *
     * @param param
     * @return
     */
    MaterialSyntheticModel iceMaterialSynthetic(MaterialSyntheticParam param);

    /**
     * 视频任务合成结果回调
     *
     * @param param
     */
    void iceCallback(IceCallbackParam param);

    /**
     * 通过rpa方式合成的视频放入redis队列
     * @param jobId
     * @param videoPath
     */
    void pushDataToQueueRpa(String jobId, String videoPath);

    /**
     * 根据任务Id查询任务详情
     *
     * @param jobId 任务Id
     */
    MediaProducingJobModel queryTaskStatusByJobId(String jobId);

    /**
     * 修复ICE回调
     *
     * @param jobId 任务Id
     * @param callbackUrl 回调地址
     */
    void repairIceCallback(String jobId, String callbackUrl);

    /**
     * 修复所有的RPA ICE回调
     *
     * @param param 修复参数
     */
    void repairAllRpaIceCallback(RepairIceCallbackParam param);
}