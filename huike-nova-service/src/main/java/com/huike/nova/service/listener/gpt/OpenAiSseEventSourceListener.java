/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.listener.gpt;

import com.annimon.stream.function.Consumer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.TraceIdGenerator;
import com.huike.nova.service.enums.GPTServiceProviderEnum;
import com.unfbx.chatgpt.entity.chat.ChatChoice;
import com.unfbx.chatgpt.entity.chat.ChatCompletionResponse;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RAtomicLong;
import org.slf4j.MDC;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;

/**
 * OpenAI 流式接口监听
 *
 * @deprecated 已过时，请使用{@link OpenAiSseEventSourceListener2} 代替
 * <AUTHOR>
 * @version OpenAiSseEventSourceListener.java, v 0.1 2023-10-18 3:51 PM ruanzy
 */
@Slf4j
@Getter
@Deprecated
public class OpenAiSseEventSourceListener extends EventSourceListener {

    /**
     * GPT 服务提供商
     */
    private final GPTServiceProviderEnum gptServiceProvider;

    /**
     * SSe对象
     */
    private final SseEmitter sseEmitter;

    /**
     * 消息唯一ID
     */
    private final String contentId;

    /**
     * 登录账号ID
     */
    private final String accountId;

    /**
     * 调用次数的Atomic
     */
    private final RAtomicLong atomicLong;

    /**
     * 错误回调
     */
    private BiConsumer<Throwable, Response> onFailCallback;

    public OpenAiSseEventSourceListener(GPTServiceProviderEnum gptServiceProvider, SseEmitter sseEmitter, String contentId, String accountId, RAtomicLong atomicLong) {
        this.gptServiceProvider = gptServiceProvider;
        this.sseEmitter = sseEmitter;
        this.contentId = contentId;
        this.accountId = accountId;
        this.atomicLong = atomicLong;
    }

    /**
     * 设置错误回调函数
     *
     * @param onFailCallback 回调函数
     */
    public void setOnFailCallback(BiConsumer<Throwable, Response> onFailCallback) {
        this.onFailCallback = onFailCallback;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
        MDC.put(CommonConstant.TRACE_ID, TraceIdGenerator.generate());
        LogUtil.info(log, "OpenAiSseEventSourceListener.onOpen >> SSE.{} >> 建立SSE连接, contentId={}, accountId={}", gptServiceProvider, contentId, accountId);
    }

    /**
     * {@inheritDoc}
     */
    @SneakyThrows
    @Override
    public void onEvent(@NotNull EventSource eventSource, String id, String type, String data) {
        if (data.equals("[DONE]")) {
            LogUtil.info(log, "OpenAiSseEventSourceListener.onEvent >> SSE.{} >> SSE返回数据结束, id:{}", gptServiceProvider, id);
            sseEmitter.send(SseEmitter.event()
                    .id(contentId)
                    .data("[DONE]")
                    .reconnectTime(3000));
            // 传输完成后自动关闭sse
            sseEmitter.complete();
            return;
        }
        ObjectMapper mapper = new ObjectMapper();
        // 读取Json
        ChatCompletionResponse completionResponse = mapper.readValue(data, ChatCompletionResponse.class);
        try {
            ChatChoice chatChoice = completionResponse.getChoices().get(0);
            // 判断是否结束
            if (Objects.isNull(chatChoice.getFinishReason())) {
                sseEmitter.send(SseEmitter.event()
                        .id(contentId)
                        .data(chatChoice.getDelta().getContent())
                        .reconnectTime(3000));
            }
        } catch (Exception e) {
            LogUtil.info(log, "OpenAiSseEventSourceListener.onEvent >> SSE.{} >> SSE信息推送失败, data={}", gptServiceProvider, data);
            // 是否会导致前端出现error
            eventSource.cancel();
        }
    }


    @Override
    public void onClosed(@NotNull EventSource eventSource) {
        LogUtil.info(log, "OpenAiSseEventSourceListener.onClosed >> SSE.{} >> SSE关闭连接..", gptServiceProvider);
        //不管是否成功，新增缓存
        if (atomicLong.isExists()) {
            //设置缓存
            atomicLong.getAndIncrement();
        } else {
            // 获取当前时间
            LocalTime now = LocalTime.now();
            // 计算今天的最后一秒钟
            LocalTime endOfDay = LocalTime.MAX;
            // 计算还剩下多少秒时间
            long secondsLeft = now.until(endOfDay, ChronoUnit.SECONDS);
            //设置缓存
            atomicLong.getAndIncrement();
            atomicLong.expire(secondsLeft, TimeUnit.SECONDS);
        }
    }


    @SneakyThrows
    @Override
    public void onFailure(@NotNull EventSource eventSource, Throwable t, Response response) {
        MDC.put(CommonConstant.TRACE_ID, TraceIdGenerator.generate());
        if (Objects.nonNull(t)) {
            LogUtil.warn(log, "OpenAiSseEventSourceListener.onFailure >> SSE.{} >> SSE连接异常, 错误:{}", gptServiceProvider, StringUtils.defaultIfBlank(t.getMessage(), t.getClass().getName()));
        }
        if (Objects.nonNull(onFailCallback)) {
            onFailCallback.accept(t, response);
        }
        // 关闭连接
        Consumer.Util.safe(EventSource::cancel).accept(eventSource);
        if (Objects.isNull(response)) {
            return;
        }
        ResponseBody body = response.body();
        if (Objects.nonNull(body)) {
            LogUtil.info(log, "OpenAiSseEventSourceListener.onFailure >> SSE.{} >> sse连接异常data: {}，异常: {}", gptServiceProvider, body.string(), t);
        } else {
            LogUtil.info(log, "OpenAiSseEventSourceListener.onFailure >> SSE.{} >> sse连接异常data: {}，异常: {}", gptServiceProvider, response, t);
        }

    }

}