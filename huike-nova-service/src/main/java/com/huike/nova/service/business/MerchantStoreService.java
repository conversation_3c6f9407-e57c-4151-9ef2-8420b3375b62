/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.service.domain.model.store.StoreInfoGetModel;
import com.huike.nova.service.domain.param.store.StoreInfoGetParam;

/**
 * <AUTHOR>
 * @version MerchantStoreService.java, v 0.1 2022-11-08 15:45 zhangling
 */
public interface MerchantStoreService {

    /**
     * 查询门店信息
     * @param param
     * @return
     */
    StoreInfoGetModel getStoreInfo(StoreInfoGetParam param);
}