package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.zbt.CheckBanWordModel;
import com.huike.nova.service.domain.model.zbt.CommentScriptDetailModel;
import com.huike.nova.service.domain.model.zbt.FindListTemplateContentModel;
import com.huike.nova.service.domain.model.zbt.GetSelectCommentScriptModel;
import com.huike.nova.service.domain.model.zbt.GetSelectReplyScriptModel;
import com.huike.nova.service.domain.model.zbt.GetSelectSoundScriptModel;
import com.huike.nova.service.domain.model.zbt.GetSoundProductModel;
import com.huike.nova.service.domain.model.zbt.GetStoreConfigModel;
import com.huike.nova.service.domain.model.zbt.PageListAudioModel;
import com.huike.nova.service.domain.model.zbt.PageListCommentContentModel;
import com.huike.nova.service.domain.model.zbt.PageListCommentScriptModel;
import com.huike.nova.service.domain.model.zbt.PageListReplyContentModel;
import com.huike.nova.service.domain.model.zbt.PageListReplyScriptModel;
import com.huike.nova.service.domain.model.zbt.PageListSoundContentModel;
import com.huike.nova.service.domain.model.zbt.PageListSoundScriptModel;
import com.huike.nova.service.domain.model.zbt.PageListTemplateModel;
import com.huike.nova.service.domain.model.zbt.ReplyScriptDetailModel;
import com.huike.nova.service.domain.model.zbt.SoundKeywordListModel;
import com.huike.nova.service.domain.model.zbt.SoundScriptDetailModel;
import com.huike.nova.service.domain.model.zbt.SoundTitleListModel;
import com.huike.nova.service.domain.param.zbt.AddAudioParam;
import com.huike.nova.service.domain.param.zbt.AddCommentContentParam;
import com.huike.nova.service.domain.param.zbt.AddCommentScriptParam;
import com.huike.nova.service.domain.param.zbt.AddReplyScriptContentKeywordParam;
import com.huike.nova.service.domain.param.zbt.AddReplyScriptContentSoundParam;
import com.huike.nova.service.domain.param.zbt.AddReplyScriptParam;
import com.huike.nova.service.domain.param.zbt.AddSoundContentParam;
import com.huike.nova.service.domain.param.zbt.AddSoundScriptParam;
import com.huike.nova.service.domain.param.zbt.BatchAddSoundContentParam;
import com.huike.nova.service.domain.param.zbt.BatchAddTemplateParam;
import com.huike.nova.service.domain.param.zbt.BatchDeleteAudioParam;
import com.huike.nova.service.domain.param.zbt.CheckBanWordParam;
import com.huike.nova.service.domain.param.zbt.FindListTemplateContentParam;
import com.huike.nova.service.domain.param.zbt.GetSoundProductParam;
import com.huike.nova.service.domain.param.zbt.GetStoreConfigParam;
import com.huike.nova.service.domain.param.zbt.PageListAudioParam;
import com.huike.nova.service.domain.param.zbt.PageListCommentContentParam;
import com.huike.nova.service.domain.param.zbt.PageListCommentScriptParam;
import com.huike.nova.service.domain.param.zbt.PageListReplyContentParam;
import com.huike.nova.service.domain.param.zbt.PageListReplyScriptParam;
import com.huike.nova.service.domain.param.zbt.PageListSoundContentParam;
import com.huike.nova.service.domain.param.zbt.PageListSoundScriptParam;
import com.huike.nova.service.domain.param.zbt.PageListTemplateParam;
import com.huike.nova.service.domain.param.zbt.SelectCommentScriptParam;
import com.huike.nova.service.domain.param.zbt.SelectReplyScriptParam;
import com.huike.nova.service.domain.param.zbt.SelectSoundScriptParam;
import com.huike.nova.service.domain.param.zbt.SoundRelationProductParam;
import com.huike.nova.service.domain.param.zbt.SoundTitleListParam;
import com.huike.nova.service.domain.param.zbt.UpdateCommentContentParam;
import com.huike.nova.service.domain.param.zbt.UpdateCommentScriptParam;
import com.huike.nova.service.domain.param.zbt.UpdateReplyScriptContentKeywordParam;
import com.huike.nova.service.domain.param.zbt.UpdateReplyScriptContentSoundParam;
import com.huike.nova.service.domain.param.zbt.UpdateReplyScriptParam;
import com.huike.nova.service.domain.param.zbt.UpdateSoundContentParam;
import com.huike.nova.service.domain.param.zbt.UpdateSoundScriptParam;
import com.huike.nova.service.domain.param.zbt.UpdateStoreConfigParam;
import com.huike.nova.service.domain.param.zbt.UpdateTitleSortParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年08月29日 11:56
 */

public interface ZbtMaterialService {

    /**
     * 分页查询口播列表
     *
     * @param param 分页参数
     * @return 分页结果
     */
    PageResult<PageListSoundScriptModel> pageListSoundScript(PageParam<PageListSoundScriptParam> param);

    /**
     * 选择口播脚本
     *
     * @param param 参数
     */
    void selectSoundScript(SelectSoundScriptParam param);

    /**
     * 添加口播脚本
     *
     * @param param 参数
     * @return 脚本id
     */
    String addSoundScript(AddSoundScriptParam param);

    /**
     * 添加口播内容
     *
     * @param param 参数
     */
    void addSoundContent(AddSoundContentParam param);

    /**
     * 修改口播脚本
     *
     * @param param 参数
     */
    void updateSoundScript(UpdateSoundScriptParam param);

    /**
     * 删除口播脚本
     *
     * @param scriptId 脚本id
     */
    void deleteSoundScript(String scriptId);

    /**
     * 口播脚本复制创建
     *
     * @param scriptId 脚本id
     */
    void soundScriptCopyCreate(String scriptId);

    /**
     * 更新口播内容
     *
     * @param param 参数
     */
    void updateSoundContent(UpdateSoundContentParam param);

    /**
     * 删除口播内容
     *
     * @param titleId 标题id
     */
    void deleteSoundContent(String titleId);

    /**
     * 口播关键词列表
     *
     * @return SoundKeywordListModel
     */
    SoundKeywordListModel soundKeywordList();

    /**
     * 获取选择的口播脚本
     *
     * @param storeId 门店id
     * @return GetSelectSoundScriptModel
     */
    GetSelectSoundScriptModel getSelectSoundScript(String storeId);


    /**
     * 分页查询评论脚本列表
     *
     * @param param 分页参数
     * @return 分页结果
     */
    PageResult<PageListCommentScriptModel> pageListCommentScript(PageParam<PageListCommentScriptParam> param);

    /**
     * 选择评论脚本
     *
     * @param param 参数
     */
    void selectCommentScript(SelectCommentScriptParam param);

    /**
     * 添加评论脚本
     *
     * @param param 参数
     * @return 脚本id
     */
    String addCommentScript(AddCommentScriptParam param);

    /**
     * 修改评论脚本
     *
     * @param param 入参
     */
    void updateCommentScript(UpdateCommentScriptParam param);

    /**
     * 删除评论脚本
     *
     * @param scriptId 脚本id
     */
    void deleteCommentScript(String scriptId);

    /**
     * 获取选择的评论脚本
     *
     * @param storeId 门店id
     * @return GetSelectCommentScriptModel
     */
    GetSelectCommentScriptModel getSelectCommentScript(String storeId);

    /**
     * 评论脚本复制创建
     *
     * @param scriptId 脚本id
     */
    void commentScriptCopyCreate(String scriptId);

    /**
     * 添加评论内容
     *
     * @param param 参数
     */
    void addCommentContent(AddCommentContentParam param);

    /**
     * 更新评论内容
     *
     * @param param 参数
     */
    void updateCommentContent(UpdateCommentContentParam param);

    /**
     * 删除评论内容
     *
     * @param commentId 评论内容id
     */
    void deleteCommentContent(String commentId);

    /**
     * 添加音频
     *
     * @param param 参数
     */
    void addAudio(AddAudioParam param);

    /**
     * 分页查询音频列表
     *
     * @param param 分页参数
     * @return 分页结果
     */
    PageResult<PageListAudioModel> pageListAudio(PageParam<PageListAudioParam> param);

    /**
     * 批量删除音频
     *
     * @param param 参数
     */
    void batchDeleteAudio(BatchDeleteAudioParam param);

    /**
     * 更新门店配置
     *
     * @param param 参数
     */
    void updateStoreConfig(UpdateStoreConfigParam param);

    /**
     * 分页查询口播内容列表
     *
     * @param param 分页参数
     * @return 分页结果
     */
    PageResult<PageListSoundContentModel> pageListSoundContent(PageParam<PageListSoundContentParam> param);

    /**
     * 口播脚本详情
     *
     * @param scriptId 脚本id
     * @return 脚本信息
     */
    SoundScriptDetailModel soundScriptDetail(String scriptId);

    /**
     * 评论脚本详情
     *
     * @param scriptId 脚本id
     * @return 脚本信息
     */
    CommentScriptDetailModel commentScriptDetail(String scriptId);

    /**
     * 获取门店配置
     *
     * @param param 参数
     * @return 结果
     */
    GetStoreConfigModel getStoreConfig(GetStoreConfigParam param);

    /**
     * 分页查询评论内容列表
     *
     * @param param 分页参数
     * @return 分页结果
     */
    PageResult<PageListCommentContentModel> pageListCommentContent(PageParam<PageListCommentContentParam> param);

    /**
     * 批量添加口播内容
     *
     * @param param 参数
     * @return 口播内容id
     */
    List<String> batchAddSoundContent(BatchAddSoundContentParam param);

    /**
     * 更新口播脚本内容
     *
     * @param scriptId 脚本id
     */
    void updateSoundTextContent(String scriptId);

    /**
     * 口播模版分页列表
     *
     * @param param 参数
     */
    PageResult<PageListTemplateModel> pageListTemplate(PageParam<PageListTemplateParam> param);


    /**
     * 口播模版列表
     *
     * @param param 参数
     */
    List<FindListTemplateContentModel> findListTemplateContent(FindListTemplateContentParam param);

    /**
     * 校验违禁词
     *
     * @param param 参数
     */
    CheckBanWordModel checkBanWord(CheckBanWordParam param);

    /**
     * 口播标题列表
     *
     * @param param 参数
     */
    List<SoundTitleListModel> soundTitleList(SoundTitleListParam param);

    /**
     * 修改口播标题排序
     *
     * @param param 参数
     */
    void updateTitleSort(List<UpdateTitleSortParam> param);

    /**
     * 获取口播商品
     *
     * @param param 参数
     */
    GetSoundProductModel getSoundProduct(GetSoundProductParam param);

    /**
     * 回复脚本分页列表
     *
     * @param param 参数
     */
    PageResult<PageListReplyScriptModel> pageListReplyScript(PageParam<PageListReplyScriptParam> param);

    /**
     * 选择回复脚本
     *
     * @param param 参数
     */
    void selectReplyScript(SelectReplyScriptParam param);

    /**
     * 添加回复脚本
     *
     * @param param 参数
     * @return 脚本id
     */
    String addReplyScript(AddReplyScriptParam param);

    /**
     * 编辑回复脚本
     *
     * @param param 参数
     */
    void updateReplyScript(UpdateReplyScriptParam param);

    /**
     * 删除回复脚本
     *
     * @param scriptId 脚本id
     */
    void deleteReplyScript(String scriptId);

    /**
     * 获取选中回复脚本
     *
     * @param storeId 门店id
     */
    GetSelectReplyScriptModel getSelectReplyScript(String storeId);

    /**
     * 回复脚本详情
     *
     * @param scriptId 脚本id
     */
    ReplyScriptDetailModel replyScriptDetail(String scriptId);


    /**
     * 添加回复脚本内容-关键词
     *
     * @param param 参数
     */
    void addReplyScriptContentKeyword(AddReplyScriptContentKeywordParam param);

    /**
     * 更新回复脚本内容-关键词
     *
     * @param param 参数
     */
    void updateReplyScriptContentKeyword(UpdateReplyScriptContentKeywordParam param);

    /**
     * 添加回复脚本内容-口播
     *
     * @param param 参数
     */
    void addReplyScriptContentSound(AddReplyScriptContentSoundParam param);

    /**
     * 更新回复脚本内容-口播
     *
     * @param param 参数
     */
    void updateReplyScriptContentSound(UpdateReplyScriptContentSoundParam param);

    /**
     * 删除回复脚本内容-口播
     *
     * @param soundId 口播id
     */
    void deleteReplyScriptContentSound(String soundId);

    /**
     * 删除回复脚本内容
     *
     * @param scriptContentId 脚本内容id
     */
    void deleteReplyScriptContent(String scriptContentId);

    /**
     * 回复脚本复制创建
     *
     * @param scriptId 脚本id
     */
    void replyScriptCopyCreate(String scriptId);

    /**
     * 回复脚本内容列表
     *
     * @param param 参数
     */
    PageResult<PageListReplyContentModel> pageListReplyContent(PageParam<PageListReplyContentParam> param);

    /**
     * 批量添加回复脚本内容-关键词
     *
     * @param param 参数
     */
    void batchAddReplyScriptContentSound(List<AddReplyScriptContentSoundParam> param);

    /**
     * 批量添加模版
     *
     * @param param 参数
     */
    void batchAddTemplate(List<BatchAddTemplateParam> param);

    /**
     * 口播关联商品
     *
     * @param param 参数
     */
    void soundRelationProduct(SoundRelationProductParam param);

    /**
     * 补充口播文案
     *
     * @param soundIdList 口播id列表
     */
    void supplementSoundText(List<String> soundIdList);

    /**
     * 更新口播文案
     *
     * @param soundId 口播id
     * @param text    文案
     */
    void updateReplyScriptContentSoundText(String soundId, String text);
}
