/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.mina;


import com.huike.nova.service.domain.model.startask.mina.templatemessage.WxSubscribeMessageListQueryModel;
import com.huike.nova.service.domain.param.startask.mina.templatemessage.WxSubscribeMessageListQueryParam;
import com.huike.nova.service.domain.param.startask.mina.templatemessage.WxSubscribeMessageReportParam;
import com.huike.nova.service.domain.param.wechatapi.WxSubscribeMessageTemplateDataModel;

import java.util.List;

/**
 * <AUTHOR>
 * @version SubscribeMessageService.java, v 0.1 2019-06-15 11:09 wangyi
 */
public interface SubscribeMessageService {

    /**
     * 订阅消息记录上报
     *
     * @param openId      用户id
     * @param templateIds 模版id列表
     */
    void wxSubscribeMessageReport(String openId, List<String> templateIds, Integer type, String businessId);

    /**
     * 清除订阅记录
     *
     * @param templateId 模版id
     * @param businessId 业务id
     */
    void removeRecord(String templateId, String businessId, Integer type);

    /**
     * 查询订阅记录
     *
     * @param businessId     业务id
     * @param templateId 门店id
     * @return 订阅记录
     */
    String queryRecord(String businessId, String templateId, Integer type);

    /**
     * 获取订阅消息发送消息体
     *
     * @param messageId 消息id
     * @param data      数据
     * @return 数据
     */
    List<WxSubscribeMessageTemplateDataModel> getSubscribeMessageData(String messageId, List<String> data);

    /**
     * 发送订阅消息
     *
     * @param businessId 业务id
     * @param appletId   小程序id
     * @param templateId 模版id
     * @param page       跳转页面
     * @param data       数据
     */
    void sendSubscribeMessage(String businessId, String appletId, String templateId, String page, List<String> data, Integer type);

    /**
     * 微信订阅消息列表查询
     *
     * @param param 查询参数
     * @return 订阅消息列表
     */
    WxSubscribeMessageListQueryModel wxSubscribeMessageListQuery(WxSubscribeMessageListQueryParam param);

    /**
     * 微信订阅消息上报
     *
     * @param param 上报参数
     */
    void wxSubscribeMessageReport(WxSubscribeMessageReportParam param);
}