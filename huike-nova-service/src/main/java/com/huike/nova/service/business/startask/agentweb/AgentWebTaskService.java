/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.startask.web.task.WebTaskListModel;
import com.huike.nova.service.domain.param.startask.web.task.WebApplyListParam;
import com.huike.nova.service.domain.param.startask.web.task.WebTaskListParam;

/**
 * <AUTHOR>
 * @version AgentWebTaskService.java, v 0.1 2024-05-22 4:34 PM ruanzy
 */
public interface AgentWebTaskService {

    /**
     * 任务管理列表
     *
     * @param param 入参
     * @return 出参
     */
    PageResult<WebTaskListModel> list(PageParam<WebTaskListParam> param);

    /**
     * 列表导出
     *
     * @param param 入参
     */
    void requestExport(WebTaskListParam param);

    /**
     * 报名清单导出
     *
     * @param param
     */
    void requestExportApplyList(WebApplyListParam param);
}