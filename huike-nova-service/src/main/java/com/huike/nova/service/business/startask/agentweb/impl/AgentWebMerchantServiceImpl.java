/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.startask.web.StarTaskWebCommonConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum;
import com.huike.nova.common.enums.startask.web.DistributionTypeEnum;
import com.huike.nova.common.enums.startask.web.OperateExportBusinessTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.common.util.ExceptionUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.param.startask.WebMerchantListParamDTO;
import com.huike.nova.dao.domain.result.StarTaskBalanceLogResultDTO;
import com.huike.nova.dao.domain.result.startask.FindInviterListByInviterPhoneListDTO;
import com.huike.nova.dao.domain.result.startask.WebMerchantListResultDTO;
import com.huike.nova.dao.entity.AilikeGaodeCodeDO;
import com.huike.nova.dao.entity.StarTaskAgentAreaDO;
import com.huike.nova.dao.entity.StarTaskAgentDO;
import com.huike.nova.dao.entity.StarTaskIdentityDO;
import com.huike.nova.dao.entity.StarTaskIdentityRelationDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.AilikeGaodeCodeDAO;
import com.huike.nova.dao.repository.StarTaskAgentAreaDAO;
import com.huike.nova.dao.repository.StarTaskAgentDAO;
import com.huike.nova.dao.repository.StarTaskBalanceLogDAO;
import com.huike.nova.dao.repository.StarTaskIdentityDAO;
import com.huike.nova.dao.repository.StarTaskIdentityRelationDAO;
import com.huike.nova.service.business.startask.agentweb.AgentWebMerchantService;
import com.huike.nova.service.business.startask.web.StarTaskWebExportService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.web.StarTaskWebMerchantServiceObjMapper;
import com.huike.nova.service.domain.model.startask.agentweb.login.StarTaskAgentWebLoginModel;
import com.huike.nova.service.domain.model.startask.web.merchant.DefaultDistributionRateModel;
import com.huike.nova.service.domain.model.startask.web.merchant.QueryDefaultTaskMoneyModel;
import com.huike.nova.service.domain.model.startask.web.merchant.WebMerchantListModel;
import com.huike.nova.service.domain.param.startask.web.merchant.UpdateTaskMoneyParam;
import com.huike.nova.service.domain.param.startask.web.merchant.WebMerchantListParam;
import jodd.util.StringPool;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version AgentWebMerchantServiceImpl.java, v 0.1 2024-05-22 10:33 AM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class AgentWebMerchantServiceImpl implements AgentWebMerchantService {

    private StarTaskWebMerchantServiceObjMapper starTaskWebMerchantServiceObjMapper;

    private StarTaskBalanceLogDAO starTaskBalanceLogDAO;

    private StarTaskIdentityDAO starTaskIdentityDAO;

    private StarTaskAgentDAO starTaskAgentDAO;

    private StarTaskAgentAreaDAO starTaskAgentAreaDAO;

    private SysConfig sysConfig;

    private StarTaskIdentityRelationDAO starTaskIdentityRelationDAO;

    private AilikeGaodeCodeDAO ailikeGaodeCodeDAO;

    private RedissonClient redissonClient;

    private StarTaskWebExportService starTaskWebExportService;


    /**
     * 商家列表
     *
     * @param param 入参
     * @return 出参
     */
    @Override
    public PageResult<WebMerchantListModel> list(PageParam<WebMerchantListParam> param) {
        // 获取登录态信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        String agentId = basicInfo.getAgentId();
        // 分页查询商家信息
        Page<WebMerchantListResultDTO> activityPage = starTaskIdentityDAO.pageList(this.buildPageParam(param, agentId));
        List<WebMerchantListResultDTO> records = activityPage.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            // 根据邀请人手机号查询邀请人信息
            List<String> inviterPhoneList = records.stream().map(WebMerchantListResultDTO::getInviterPhone).collect(Collectors.toList());
            List<FindInviterListByInviterPhoneListDTO> inviterList = starTaskIdentityDAO.findInviterListByInviterPhoneList(inviterPhoneList);
            Map<String, String> orderRefundTypeMap = inviterList.stream().collect(Collectors.toMap(FindInviterListByInviterPhoneListDTO::getInviterPhone, FindInviterListByInviterPhoneListDTO::getInviterName));
            // 查询邀请商家列表数据
            List<String> identityIdList = records.stream().map(WebMerchantListResultDTO::getIdentityId).collect(Collectors.toList());
            List<StarTaskIdentityRelationDO> listByParentIdList = CollectionUtil.newArrayList();
            Map<String, StarTaskBalanceLogResultDTO> distributionMap = Maps.newConcurrentMap();
            if (CollectionUtil.isNotEmpty(identityIdList)) {
                listByParentIdList = starTaskIdentityRelationDAO.findListByParentIdList(identityIdList);
                List<StarTaskBalanceLogResultDTO> list = starTaskBalanceLogDAO.statisticsDistributionRewardsAmount(identityIdList);
                distributionMap = list.stream().collect(Collectors.toMap(StarTaskBalanceLogResultDTO::getIdentityId, Function.identity()));
            }
            // 省市区
            List<String> adCodeList = records.stream().map(WebMerchantListResultDTO::getProvince).distinct().collect(Collectors.toList());
            adCodeList.addAll(records.stream().map(WebMerchantListResultDTO::getCity).distinct().collect(Collectors.toList()));
            List<AilikeGaodeCodeDO> codeInfoList = ailikeGaodeCodeDAO.findCodeInfoList(adCodeList);
            Map<String, AilikeGaodeCodeDO> adCodeMap = codeInfoList.stream().collect(Collectors.toMap(AilikeGaodeCodeDO::getAdcode, Function.identity()));
            // 查询代理商信息
            StarTaskAgentDO agentInfo = starTaskAgentDAO.getAgentInfo(agentId);
            if (Objects.isNull(agentInfo)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("区代信息异常");
            }
            for (WebMerchantListResultDTO record : records) {
                record.setPhoneNumber(FieldEncryptUtil.decode(record.getPhoneNumber()));
                if (CommonConstant.INTEGER_ONE.equals(record.getTaskMoneyType())) {
                    record.setMinTaskMoney(new BigDecimal(sysConfig.getStarTaskMinaMinTaskMoney()));
                    record.setPlatformRate(agentInfo.getAgentRate());
                }
                record.setInviterPhone(FieldEncryptUtil.decode(record.getInviterPhone()));
                record.setInviterName(orderRefundTypeMap.getOrDefault(record.getInviterPhone(), StringPool.EMPTY));
                // 邀请商家数
                record.setInviterMerchantCount(CommonConstant.ZERO);
                if (CollectionUtil.isNotEmpty(listByParentIdList)) {
                    record.setInviterMerchantCount(listByParentIdList.stream().filter(item -> item.getParentId().equals(record.getIdentityId())).collect(Collectors.toList()).size());
                }
                // 商户分销奖励
                record.setDistributionAwardAmount(CommonConstant.ZERO.toString());
                if (Objects.nonNull(distributionMap.get(record.getIdentityId()))) {
                    record.setDistributionAwardAmount(distributionMap.get(record.getIdentityId()).getDistributionRewardsAmount().toString());
                }
                // 分销比例
                if (DistributionTypeEnum.DEFAULT.getValue().equals(record.getDistributionType())) {
                    record.setDistributionRate(agentInfo.getMerchantRate());
                }
                // 省市区
                AilikeGaodeCodeDO provinceInfo = adCodeMap.get(record.getProvince());
                AilikeGaodeCodeDO cityInfo = adCodeMap.get(record.getCity());
                record.setProvinceName(Objects.nonNull(provinceInfo) ? provinceInfo.getAdname() : StrUtil.EMPTY);
                record.setCityName(Objects.nonNull(cityInfo) ? cityInfo.getAdname() : StrUtil.EMPTY);
            }
        }
        return starTaskWebMerchantServiceObjMapper.toActivityDetailModel(activityPage);
    }

    /**
     * 商家列表导出
     *
     * @param param 入参
     */
    @Override
    public void requestExport(WebMerchantListParam param) {
        // 获取登录态信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        String agentId = basicInfo.getAgentId();
        val queryParam = starTaskWebMerchantServiceObjMapper.toWebMerchantListParamDTO(param);
        queryParam.setIdentityType(StarTaskIdentityTypeEnum.MERCHANT.getValue());
        // 时间转换
        String endDate = param.getEndDate();
        String startDate = param.getStartDate();
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            queryParam.setStartTime(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(startDate, DatePattern.NORM_DATE_PATTERN))));
            queryParam.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(endDate, DatePattern.NORM_DATE_PATTERN))));
        }
        String merchantAccountNumber = param.getMerchantAccountNumber();
        if (StringUtils.isNotBlank(merchantAccountNumber)) {
            queryParam.setPhoneNumber(FieldEncryptUtil.encode(merchantAccountNumber));
            queryParam.setMerchantName(merchantAccountNumber);
        }
        String inviter = param.getInviter();
        if (StringUtils.isNotBlank(inviter)) {
            queryParam.setInviterPhone(FieldEncryptUtil.encode(inviter));
            queryParam.setInviterMerchantName(inviter);
        }
        // 查询区域
        if (CollectionUtil.isEmpty(param.getCityList())) {
            List<StarTaskAgentAreaDO> list = starTaskAgentAreaDAO.findByAgentId(agentId);
            queryParam.setCityList(list.stream().map(StarTaskAgentAreaDO::getCity).collect(Collectors.toList()));
        }
        queryParam.setAgentId(agentId);
        // 异步导出
        starTaskWebExportService.addAgentExportTask(OperateExportBusinessTypeEnum.AGENT_MERCHANT_MANAGEMENT, queryParam, basicInfo);
    }

    /**
     * 查询默认任务金
     *
     * @return
     */
    @Override
    public QueryDefaultTaskMoneyModel queryDefaultTaskMoney() {
        // 获取登录态信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        // 查询代理商信息
        StarTaskAgentDO agentInfo = starTaskAgentDAO.getAgentInfo(basicInfo.getAgentId());
        if (Objects.isNull(agentInfo)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("区代信息异常");
        }
        QueryDefaultTaskMoneyModel model = new QueryDefaultTaskMoneyModel();
        model.setMinTaskMoney(new BigDecimal(sysConfig.getStarTaskMinaMinTaskMoney()));
        model.setPlatformRate(agentInfo.getAgentRate());
        return model;
    }

    /**
     * 查询默认的分销比例
     *
     * @return
     */
    @Override
    public DefaultDistributionRateModel findDefaultDistributionRate() {
        // 获取登录态信息
        StarTaskAgentWebLoginModel basicInfo = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        // 查询代理商信息
        StarTaskAgentDO agentInfo = starTaskAgentDAO.getAgentInfo(basicInfo.getAgentId());
        if (Objects.isNull(agentInfo)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("区代信息异常");
        }
        DefaultDistributionRateModel model = new DefaultDistributionRateModel();
        model.setMerchantDefaultDistributionRate(agentInfo.getMerchantRate());
        model.setStarDefaultDistributionRate(agentInfo.getStarRate());
        return model;
    }

    /**
     * 更改商家任务金限制
     *
     * @param param
     */
    @Override
    public void updateTaskMoney(UpdateTaskMoneyParam param) {
        String identityId = param.getIdentityId();
        Integer taskMoneyType = param.getTaskMoneyType();
        String lock = StrUtil.format(StarTaskWebCommonConstant.CHANGE_MERCHANT_TASK_MONEY_LOCK, identityId);
        try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
            redisLockHelper.tryLock();
            // 身份信息查询
            StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(identityId);
            if (null == identityDO) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息异常");
            }
            // 校验任务金是否在0-10000之间并且最多只有两位小数
            this.validateMinTaskMoney(param.getMinTaskMoney());
            // 校验任务金
            this.validatePlatformCommissionRate(param.getPlatformRate());
            DistributionTypeEnum distributionTypeEnum = DistributionTypeEnum.getByValue(taskMoneyType);
            if (Objects.isNull(distributionTypeEnum)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("分销类型参数错误");
            }
            starTaskIdentityDAO.updateTaskMoney(identityId, param.getMinTaskMoney(), param.getPlatformRate(), taskMoneyType);
        } catch (Exception e) {
            LogUtil.warn(log, "AgentWebMerchantServiceImpl" + " >>>>> " + "updateTaskMoney" + "  锁释放失败", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }

    @NotNull
    private PageParam<WebMerchantListParamDTO> buildPageParam(PageParam<WebMerchantListParam> param, String agentId) {
        PageParam<WebMerchantListParamDTO> pageParam = starTaskWebMerchantServiceObjMapper.toWebMerchantListParamDTO(param);
        WebMerchantListParamDTO query = pageParam.getQuery();
        WebMerchantListParam paramQuery = param.getQuery();
        query.setIdentityType(StarTaskIdentityTypeEnum.MERCHANT.getValue());
        // 时间转换
        String endDate = paramQuery.getEndDate();
        String startDate = paramQuery.getStartDate();
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            query.setStartTime(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(startDate, DatePattern.NORM_DATE_PATTERN))));
            query.setEndTime(DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(endDate, DatePattern.NORM_DATE_PATTERN))));
        }
        String merchantAccountNumber = paramQuery.getMerchantAccountNumber();
        if (StringUtils.isNotBlank(merchantAccountNumber)) {
            query.setPhoneNumber(FieldEncryptUtil.encode(merchantAccountNumber));
            query.setMerchantName(merchantAccountNumber);
        }
        String inviter = paramQuery.getInviter();
        if (StringUtils.isNotBlank(inviter)) {
            query.setInviterPhone(FieldEncryptUtil.encode(inviter));
            query.setInviterMerchantName(inviter);
        }
        // 查询区域
        if (CollectionUtil.isEmpty(query.getCityList())) {
            List<StarTaskAgentAreaDO> list = starTaskAgentAreaDAO.findByAgentId(agentId);
            query.setCityList(list.stream().map(StarTaskAgentAreaDO::getCity).collect(Collectors.toList()));
        }
        return pageParam;
    }

    /**
     * 校验平台佣金比例
     *
     * @param platformCommissionRate 平台佣金比例
     */
    private void validatePlatformCommissionRate(BigDecimal platformCommissionRate) {
        // 检查数值范围是否在0到100之间
        boolean withinRange = platformCommissionRate.compareTo(BigDecimal.ZERO) >= 0 && platformCommissionRate.compareTo(new BigDecimal("100")) <= 0;

        // 检查小数位数是否不超过两位
        boolean validDecimalPlaces = platformCommissionRate.scale() <= 2;

        if (!withinRange || !validDecimalPlaces) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("平台佣金比例超出范围，当前范围为0.00-100.00");
        }
    }

    /**
     * 校验任务金额
     *
     * @param minTaskMoney 任务金额
     */
    public void validateMinTaskMoney(BigDecimal minTaskMoney) {
        // 检查数值范围是否在0到10000之间
        boolean withinRange = minTaskMoney.compareTo(BigDecimal.ZERO) >= 0 && minTaskMoney.compareTo(new BigDecimal("10000")) <= 0;

        // 检查小数位数是否不超过两位
        boolean validDecimalPlaces = minTaskMoney.scale() <= 2;

        if (!withinRange || !validDecimalPlaces) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("最小任务金取值超出范围，当前范围为0.00-10000.00");
        }

    }
}