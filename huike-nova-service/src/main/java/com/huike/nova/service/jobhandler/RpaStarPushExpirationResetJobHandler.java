package com.huike.nova.service.jobhandler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.huike.nova.common.enums.RpaVideoRecordReleasedEnum;
import com.huike.nova.common.enums.SupActivityPublishStatusEnum;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.repository.AilikeBSupActivityDAO;
import com.huike.nova.dao.repository.RpaVideoRecordDAO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * RPA 推送过期记录设置
 *
 * <AUTHOR> (<EMAIL>)
 * @version RpaStarPushExpireJobHandler.java, v1.0 10/20/2023 13:30 John Exp$
 */
@Component
@Slf4j
@JobHandler("rpaStarPushExpirationResetJobHandler")
@AllArgsConstructor
public class RpaStarPushExpirationResetJobHandler extends IJobHandler {

    private SysConfig sysConfig;

    private RpaVideoRecordDAO rpaVideoRecordDAO;

    private AilikeBSupActivityDAO ailikeBSupActivityDAO;

    private TransactionTemplate transactionTemplate;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("RpaStarPushExpirationSetupJobHandler.execute >> 达人推发送超时视频过期检测开始..time:{}", DateUtil.now());
        // 达人推发布超时时间
        val starPushVideoTimeoutSec = sysConfig.getStarPushVideoTimeoutSec();
        // 截止日期，小于该日期的达人推都会进行超时处理
        val deadlineTimeSec = DateUtil.currentSeconds() - starPushVideoTimeoutSec;
        // 获得截止日期Date对象
        val deadlineDate = new Date(TimeUnit.SECONDS.toMillis(deadlineTimeSec));
        // 查询指定时间以前已经超时的视频
        val list = ailikeBSupActivityDAO.queryListBeforeDeadlineDate(deadlineDate, null);
        if (CollectionUtil.isEmpty(list)) {
            return ReturnT.SUCCESS;
        }

        list.forEach(ailikeBSupActivityDO -> {
            // 达人推活动Id
            val supActivityId = ailikeBSupActivityDO.getSupActivityId();
            XxlJobLogger.log("RpaStarPushExpirationSetupJobHandler.execute >> 已过期的达人推记录:{}, 最后发布时间:{}",
                    supActivityId, DateUtil.format(ailikeBSupActivityDO.getRecentPublishTime(), DatePattern.NORM_DATETIME_FORMAT));
            val rpaVideoRecordList = rpaVideoRecordDAO.queryExpiredListBeforeDeadline(supActivityId, deadlineDate);
            if (CollectionUtil.isNotEmpty(rpaVideoRecordList)) {
                transactionTemplate.execute(status -> {
                    rpaVideoRecordList.forEach(rpaVideoRecordDO -> {
                        XxlJobLogger.log("RpaStarPushExpirationSetupJobHandler.execute >> 超时的达人推记录:{}, 用户Id:{}",
                                rpaVideoRecordDO.getStarPushRecordId(), rpaVideoRecordDO.getUserUniqueId());
                        rpaVideoRecordDAO.updateRpaStarPushRecordFailed(rpaVideoRecordDO.getStarPushRecordId(), rpaVideoRecordDO.getUserUniqueId(), "发布超时");
                    });
                    // 设置发布完成
                    ailikeBSupActivityDAO.updatePublishStatus(supActivityId, SupActivityPublishStatusEnum.SUCCESS.getValue());
                    return null;
                });
            } else {
                // 设置发布完成
                ailikeBSupActivityDAO.updatePublishStatus(supActivityId, SupActivityPublishStatusEnum.SUCCESS.getValue());
            }
        });
        XxlJobLogger.log("RpaStarPushExpirationSetupJobHandler.execute >> 达人推发送超时视频过期检测结束..time:{}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}
