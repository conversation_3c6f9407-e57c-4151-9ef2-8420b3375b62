/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.startask.web.export.StarTaskExportTaskModel;
import com.huike.nova.service.domain.param.startask.web.export.StarTaskWebExportTaskListParam;

/**
 * <AUTHOR>
 * @version AgentWebExportService.java, v 0.1 2024-05-22 5:36 PM ruanzy
 */
public interface AgentWebExportService {

    /**
     * 分页请求导出记录
     *
     * @param param 请求参数
     * @return 分页列表导出
     */
    PageResult<StarTaskExportTaskModel> list(PageParam<StarTaskWebExportTaskListParam> param);

    /**
     * 删除任务
     *
     * @param taskId 任务Id
     */
    void deleteTask(String taskId);
}