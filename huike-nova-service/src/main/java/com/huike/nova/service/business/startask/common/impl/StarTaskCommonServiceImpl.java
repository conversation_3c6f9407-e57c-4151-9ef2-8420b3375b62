/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.common.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.constant.startask.mina.StarTaskMinaRedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.QuotaFullEnum;
import com.huike.nova.common.enums.oem.BooleanEnum;
import com.huike.nova.common.enums.startask.mina.ApplyListStatusEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskPresetOperatorEnum;
import com.huike.nova.common.enums.startask.mina.TaskStatusEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogChangeTypeEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogRemarkTypeEnum;
import com.huike.nova.common.enums.startask.web.DistributionTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.ExceptionUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.entity.StarTaskAgentDO;
import com.huike.nova.dao.entity.StarTaskApplyListDO;
import com.huike.nova.dao.entity.StarTaskBalanceAccountDO;
import com.huike.nova.dao.entity.StarTaskBalanceLogDO;
import com.huike.nova.dao.entity.StarTaskDO;
import com.huike.nova.dao.entity.StarTaskIdentityDO;
import com.huike.nova.dao.entity.StarTaskIdentityRelationDO;
import com.huike.nova.dao.repository.StarTaskAgentDAO;
import com.huike.nova.dao.repository.StarTaskApplyListDAO;
import com.huike.nova.dao.repository.StarTaskBalanceAccountDAO;
import com.huike.nova.dao.repository.StarTaskBalanceLogDAO;
import com.huike.nova.dao.repository.StarTaskCommissionPlanDAO;
import com.huike.nova.dao.repository.StarTaskDAO;
import com.huike.nova.dao.repository.StarTaskIdentityDAO;
import com.huike.nova.dao.repository.StarTaskIdentityRelationDAO;
import com.huike.nova.service.business.startask.common.StarTaskCommonService;
import com.huike.nova.service.business.startask.mina.StarTaskWechatMinaService;
import com.huike.nova.service.domain.param.startask.mina.pay.StarTaskMinaCloseUnpaidOrderParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version StarTaskCommonServiceImpl.java, v 0.1 2023-12-13 2:42 PM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class StarTaskCommonServiceImpl implements StarTaskCommonService {

    private StarTaskBalanceAccountDAO starTaskBalanceAccountDAO;

    private StarTaskBalanceLogDAO starTaskBalanceLogDAO;

    private StarTaskApplyListDAO starTaskApplyListDAO;

    private StarTaskIdentityDAO starTaskIdentityDAO;

    private StarTaskCommissionPlanDAO starTaskCommissionPlanDAO;

    private StarTaskIdentityRelationDAO starTaskIdentityRelationDAO;

    private TransactionTemplate transactionTemplate;

    private StarTaskDAO starTaskDAO;

    private RedissonClient redissonClient;

    private StarTaskWechatMinaService starTaskWechatMinaService;

    private SysConfig sysConfig;

    private StarTaskAgentDAO starTaskAgentDAO;

    /**
     * 更新达人任务金(使用此方法外面需要添加事物和锁)
     *
     * @param identityId
     * @param applyList
     * @param starTaskDO
     * @param operatorId
     * @param operatorName
     */
    @Override
    public void updateTaskMoney(String identityId, StarTaskApplyListDO applyList, StarTaskDO starTaskDO, String operatorId, String operatorName) {
        BigDecimal taskMoney = applyList.getTaskMoney();
        // 查询账户信息（for update）
        StarTaskBalanceAccountDO taskBalanceAccountDO = starTaskBalanceAccountDAO.getBalanceAccountForUpdate(identityId);
        if (Objects.isNull(taskBalanceAccountDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账户信息异常");
        }
        // 修改达人账户
        starTaskBalanceAccountDAO.updateTaskMoney(identityId, taskMoney);
        // 添加变更纪录表
        StarTaskBalanceLogDO balanceLogDO = new StarTaskBalanceLogDO();
        balanceLogDO.setUserId(taskBalanceAccountDO.getUserId());
        balanceLogDO.setIdentityId(taskBalanceAccountDO.getIdentityId());
        balanceLogDO.setChangeType(BalanceLogChangeTypeEnum.ADDED.getValue());
        balanceLogDO.setRemarkType(BalanceLogRemarkTypeEnum.INCOME.getValue());
        balanceLogDO.setChangeRemark("任务金" + StringPool.DASH + starTaskDO.getTaskTitle());
        balanceLogDO.setChangeAmount(taskMoney);
        balanceLogDO.setChangeAvailableBalance(taskBalanceAccountDO.getAvailableBalance());
        balanceLogDO.setAfterAvailableBalance(taskBalanceAccountDO.getAvailableBalance().add(taskMoney));
        balanceLogDO.setChangeExpenditureTotalIncome(taskBalanceAccountDO.getExpenditureTotalIncome());
        balanceLogDO.setAfterExpenditureTotalIncome(taskBalanceAccountDO.getExpenditureTotalIncome());
        balanceLogDO.setChangeIncomeTotalIncome(taskBalanceAccountDO.getIncomeTotalIncome());
        balanceLogDO.setAfterIncomeTotalIncome(taskBalanceAccountDO.getIncomeTotalIncome().add(taskMoney));
        balanceLogDO.setChangeWithdrawalFrozenBalance(taskBalanceAccountDO.getWithdrawalFrozenBalance());
        balanceLogDO.setAfterWithdrawalFrozenBalance(taskBalanceAccountDO.getWithdrawalFrozenBalance());
        balanceLogDO.setRelationNumber(starTaskDO.getTaskId());
        balanceLogDO.setOperatorId(operatorId);
        balanceLogDO.setOperatorName(operatorName);
        starTaskBalanceLogDAO.saveBalanceLog(balanceLogDO);
    }

    /**
     * 构建团长返还奖励
     *
     * @param applyList
     * @param starTaskDO
     * @param identityRelationDO
     */
    @Override
    public void buildStarReturnReward(StarTaskApplyListDO applyList, StarTaskDO starTaskDO, StarTaskIdentityRelationDO identityRelationDO) {
        String identityId = identityRelationDO.getIdentityId();
        String parentId = identityRelationDO.getParentId();
        String identityLock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_APPLY_WITHDRAWAL, parentId);
        try (val redisLockHelper = new RedisLockHelper(identityLock, redissonClient)) {
            redisLockHelper.tryLock();
            // 查询账户信息（for update）
            StarTaskBalanceAccountDO distributionAccountDO = starTaskBalanceAccountDAO.getBalanceAccountForUpdate(parentId);
            if (Objects.isNull(distributionAccountDO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账户信息异常");
            }
            // 达人返还逻辑计算，返还奖励 = 任务金（抽点前）* 分佣比例
            BigDecimal distributionRate = this.getDistributionRate(starTaskDO, parentId, Boolean.FALSE);
            BigDecimal changeAmount = applyList.getTotalAmount().multiply(distributionRate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            // 更新账户奖励金
            starTaskBalanceAccountDAO.updateReturnReward(parentId, changeAmount);
            // 添加变更纪录表
            StarTaskBalanceLogDO distributionBalanceLogDO = new StarTaskBalanceLogDO();
            distributionBalanceLogDO.setUserId(distributionAccountDO.getUserId());
            distributionBalanceLogDO.setIdentityId(parentId);
            distributionBalanceLogDO.setChangeType(BalanceLogChangeTypeEnum.ADDED.getValue());
            distributionBalanceLogDO.setRemarkType(BalanceLogRemarkTypeEnum.RETURN_REWARD.getValue());
            distributionBalanceLogDO.setChangeRemark(BalanceLogRemarkTypeEnum.RETURN_REWARD.getName() + StringPool.DASH + starTaskDO.getTaskTitle());
            distributionBalanceLogDO.setChangeAmount(changeAmount);
            distributionBalanceLogDO.setChangeAvailableBalance(distributionAccountDO.getAvailableBalance());
            distributionBalanceLogDO.setAfterAvailableBalance(distributionAccountDO.getAvailableBalance().add(changeAmount));
            distributionBalanceLogDO.setChangeExpenditureTotalIncome(distributionAccountDO.getExpenditureTotalIncome());
            distributionBalanceLogDO.setAfterExpenditureTotalIncome(distributionAccountDO.getExpenditureTotalIncome());
            distributionBalanceLogDO.setChangeIncomeTotalIncome(distributionAccountDO.getIncomeTotalIncome());
            distributionBalanceLogDO.setAfterIncomeTotalIncome(distributionAccountDO.getIncomeTotalIncome());
            distributionBalanceLogDO.setChangeWithdrawalFrozenBalance(distributionAccountDO.getWithdrawalFrozenBalance());
            distributionBalanceLogDO.setAfterWithdrawalFrozenBalance(distributionAccountDO.getWithdrawalFrozenBalance());
            distributionBalanceLogDO.setRelationNumber(identityId);
            distributionBalanceLogDO.setTaskId(starTaskDO.getTaskId());
            distributionBalanceLogDO.setApplyId(applyList.getApplyId());
            distributionBalanceLogDO.setTaskMoney(applyList.getTaskMoney());
            distributionBalanceLogDO.setDayTime(DateUtil.formatDate(new Date()));
            starTaskBalanceLogDAO.saveBalanceLog(distributionBalanceLogDO);
            // 是否更新成有效达人
            if (BooleanEnum.NO.getValue().equals(identityRelationDO.getIsEffective())) {
                starTaskIdentityRelationDAO.updateIsEffective(identityId, BooleanEnum.YES.getValue());
            }
        } catch (Exception e) {
            LogUtil.error(log, "StarTaskCommonServiceImpl" + "." + "buildStarReturnReward" + " >>>>> " + "执行加锁方法失败", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }

    /**
     * 构建商家返还奖励
     *
     * @param starTaskDO
     * @param identityRelationDO
     * @param completeAmount
     */
    @Override
    public BigDecimal buildMerchantReturnReward(StarTaskDO starTaskDO, StarTaskIdentityRelationDO identityRelationDO, BigDecimal completeAmount) {
        String parentId = identityRelationDO.getParentId();
        String identityId = identityRelationDO.getIdentityId();
        String identityLock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_APPLY_WITHDRAWAL, parentId);
        try (val redisLockHelper = new RedisLockHelper(identityLock, redissonClient)) {
            redisLockHelper.tryLock();
            // 查询账户信息（for update）
            StarTaskBalanceAccountDO distributionAccountDO = starTaskBalanceAccountDAO.getBalanceAccountForUpdate(parentId);
            if (Objects.isNull(distributionAccountDO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账户信息异常");
            }
            // 获取商家分佣比例
            BigDecimal distributionRate = this.getDistributionRate(starTaskDO, parentId, Boolean.TRUE);
            // 商家返还奖励 = 任务实际总任务金（抽点前） * 分佣比例
            BigDecimal changeAmount = completeAmount.multiply(distributionRate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            // 更新账户奖励金
            starTaskBalanceAccountDAO.updateReturnReward(parentId, changeAmount);
            // 添加变更纪录表
            StarTaskBalanceLogDO distributionBalanceLogDO = new StarTaskBalanceLogDO();
            distributionBalanceLogDO.setUserId(distributionAccountDO.getUserId());
            distributionBalanceLogDO.setIdentityId(parentId);
            distributionBalanceLogDO.setChangeType(BalanceLogChangeTypeEnum.ADDED.getValue());
            distributionBalanceLogDO.setRemarkType(BalanceLogRemarkTypeEnum.RETURN_REWARD.getValue());
            distributionBalanceLogDO.setChangeRemark(BalanceLogRemarkTypeEnum.RETURN_REWARD.getName() + StringPool.DASH + starTaskDO.getTaskTitle());
            distributionBalanceLogDO.setChangeAmount(changeAmount);
            distributionBalanceLogDO.setChangeAvailableBalance(distributionAccountDO.getAvailableBalance());
            distributionBalanceLogDO.setAfterAvailableBalance(distributionAccountDO.getAvailableBalance().add(changeAmount));
            distributionBalanceLogDO.setChangeExpenditureTotalIncome(distributionAccountDO.getExpenditureTotalIncome());
            distributionBalanceLogDO.setAfterExpenditureTotalIncome(distributionAccountDO.getExpenditureTotalIncome());
            distributionBalanceLogDO.setChangeIncomeTotalIncome(distributionAccountDO.getIncomeTotalIncome());
            distributionBalanceLogDO.setAfterIncomeTotalIncome(distributionAccountDO.getIncomeTotalIncome());
            distributionBalanceLogDO.setChangeWithdrawalFrozenBalance(distributionAccountDO.getWithdrawalFrozenBalance());
            distributionBalanceLogDO.setAfterWithdrawalFrozenBalance(distributionAccountDO.getWithdrawalFrozenBalance());
            distributionBalanceLogDO.setRelationNumber(identityId);
            distributionBalanceLogDO.setTaskId(starTaskDO.getTaskId());
            distributionBalanceLogDO.setTaskMoney(completeAmount);
            distributionBalanceLogDO.setDayTime(DateUtil.formatDate(new Date()));
            starTaskBalanceLogDAO.saveBalanceLog(distributionBalanceLogDO);
            // 是否更新成有效商家
            if (BooleanEnum.NO.getValue().equals(identityRelationDO.getIsEffective())) {
                starTaskIdentityRelationDAO.updateIsEffective(identityId, BooleanEnum.YES.getValue());
            }
            return changeAmount;
        } catch (Exception e) {
            LogUtil.error(log, "StarTaskCommonServiceImpl" + "." + "buildMerchantReturnReward" + " >>>>> " + "执行加锁方法失败", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }

    /**
     * 获取分销比例逻辑
     *
     * @param starTaskDO
     * @param parentId
     * @param flag
     * @return
     */
    private BigDecimal getDistributionRate(StarTaskDO starTaskDO, String parentId, boolean flag) {
        // 查询上级商家信息
        StarTaskIdentityRelationDO parentIdentityRelationDO = starTaskIdentityRelationDAO.findListByIdentityId(parentId);
        if (Objects.isNull(parentIdentityRelationDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("上级商家信息异常");
        }
        StarTaskIdentityDO parentIdentityDO = starTaskIdentityDAO.getIdentityByIdentityId(parentId);
        if (Objects.isNull(parentIdentityDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("上级商家信息异常");
        }
        // 分销比例逻辑
        BigDecimal distributionRate;
        if (StringUtils.isNotBlank(starTaskDO.getAgentId())) {
            // 区代逻辑,校验上级账号是否在代理区域内，若不是，也按照代理区域内的默认分销比例
            StarTaskAgentDO agentDO = starTaskAgentDAO.checkAreaAgentByCity(parentIdentityDO.getCity());
            if (Objects.nonNull(agentDO) && starTaskDO.getAgentId().equals(agentDO.getAgentId()) && DistributionTypeEnum.CUSTOM.getValue().equals(parentIdentityRelationDO.getDistributionType())) {
                distributionRate = parentIdentityRelationDO.getDistributionRate();
            } else {
                // 查询当前区代默认
                StarTaskAgentDO agentInfo = starTaskAgentDAO.getAgentInfo(starTaskDO.getAgentId());
                if (Objects.isNull(agentInfo)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("代理商信息异常");
                }
                distributionRate = flag ? agentInfo.getMerchantRate() : agentInfo.getStarRate();
            }
        } else {
            BigDecimal platformRate = flag ? sysConfig.getStarTaskMinaMerchantDistributionRate() : sysConfig.getStarTaskMinaStarDistributionRate();
            // 原本逻辑，默认用系统默认:不默认用上级分销比例
            distributionRate = DistributionTypeEnum.DEFAULT.getValue().equals(parentIdentityRelationDO.getDistributionType()) ? platformRate : parentIdentityRelationDO.getDistributionRate();
        }
        return distributionRate;
    }

    /**
     * 取消任务退款到余额
     *
     * @param starTaskDO
     */
    @Override
    public void cancelTaskToRefund(StarTaskDO starTaskDO) {
        // 待支付不需要退款到余额
        if (TaskStatusEnum.WAITING_FOR_PAYMENT.getValue().equals(starTaskDO.getTaskStatus())) {
            cancelTaskIfHasPayment(starTaskDO);

            // 退款给商家
        } else {
            this.refundToMerchant(starTaskDO, starTaskDO.getTotalAmount(), "取消任务退款");
        }
    }

    @Override
    public void refundToMerchant(StarTaskDO starTaskDO, BigDecimal amount, String prefix) {
        refundToMerchant(BalanceLogRemarkTypeEnum.REFUND_TO_BALANCE, starTaskDO, amount, prefix);
    }

    /**
     * 通过
     *
     * @param starTaskDO 任务记录
     */
    private void cancelTaskIfHasPayment(StarTaskDO starTaskDO) {
        // 获得冻结的余额
        val frozenBalanceAmount = starTaskDO.getFrozenBalanceAmount();
        if (frozenBalanceAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("冻结余额不能小于0");
        }
        // 如果当前有冻结余额存在，则需要取消冻结金额，并加回到余额中
        if (frozenBalanceAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 查询当前的余额记录
            refundToMerchant(BalanceLogRemarkTypeEnum.BALANCE_UNFROZEN, starTaskDO, frozenBalanceAmount, "余额解冻");
        }
        // 订单关闭
        val closeOrderParam = new StarTaskMinaCloseUnpaidOrderParam();
        closeOrderParam.setTaskId(starTaskDO.getTaskId());
        starTaskWechatMinaService.asyncCloseUnpaidOrder(closeOrderParam);
    }

    /**
     * 退款或者余额解冻给商家
     *
     * @param remarkTypeEnum 备注类型：REFUND_TO_BALANCE 、 BALANCE_UNFROZEN
     * @param starTaskDO     任务记录
     * @param amount         金额
     * @param prefix         显示前缀
     */
    private void refundToMerchant(BalanceLogRemarkTypeEnum remarkTypeEnum, StarTaskDO starTaskDO, BigDecimal amount, String prefix) {
        // 查询账户信息（for update）
        StarTaskBalanceAccountDO balanceAccount = starTaskBalanceAccountDAO.getBalanceAccountForUpdate(starTaskDO.getIdentityId());
        if (Objects.isNull(balanceAccount)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账户信息异常");
        }
        starTaskBalanceAccountDAO.updateToFinishStarTask(starTaskDO.getIdentityId(), amount);
        // 增加交易明细纪录
        StarTaskBalanceLogDO balanceLogDO = new StarTaskBalanceLogDO();
        balanceLogDO.setUserId(starTaskDO.getUserId());
        balanceLogDO.setIdentityId(starTaskDO.getIdentityId());
        balanceLogDO.setChangeType(BalanceLogChangeTypeEnum.ADDED.getValue());
        balanceLogDO.setRemarkType(remarkTypeEnum.getValue());
        balanceLogDO.setChangeRemark(prefix + StringPool.DASH + starTaskDO.getTaskTitle());
        balanceLogDO.setChangeAmount(amount);
        balanceLogDO.setChangeAvailableBalance(balanceAccount.getAvailableBalance());
        balanceLogDO.setAfterAvailableBalance(balanceAccount.getAvailableBalance().add(amount));
        balanceLogDO.setChangeExpenditureTotalIncome(balanceAccount.getExpenditureTotalIncome());
        balanceLogDO.setAfterExpenditureTotalIncome(balanceAccount.getExpenditureTotalIncome().subtract(amount));
        balanceLogDO.setChangeIncomeTotalIncome(balanceAccount.getIncomeTotalIncome());
        balanceLogDO.setAfterIncomeTotalIncome(balanceAccount.getIncomeTotalIncome());
        balanceLogDO.setChangeWithdrawalFrozenBalance(balanceAccount.getWithdrawalFrozenBalance());
        balanceLogDO.setAfterWithdrawalFrozenBalance(balanceAccount.getWithdrawalFrozenBalance());
        balanceLogDO.setRelationNumber(starTaskDO.getTaskId());
        balanceLogDO.setOperatorId(StarTaskPresetOperatorEnum.SYSTEM.getOperatorId());
        balanceLogDO.setOperatorName(StarTaskPresetOperatorEnum.SYSTEM.getName());
        starTaskBalanceLogDAO.saveBalanceLog(balanceLogDO);
    }

    /**
     * 手动取消任务需要修改
     *
     * @param taskId        任务id
     * @param operationType 操作类型 1任务截止 2商家结束
     */
    @Override
    public void updateStatusToCanceled(String taskId, Integer operationType) {
        List<StarTaskApplyListDO> list = starTaskApplyListDAO.findApplyListByTaskId(taskId);
        // 循环
        for (StarTaskApplyListDO applyListDO : list) {
            String applyId = applyListDO.getApplyId();
            String lock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_APPLY_LIST, applyId);
            try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
                redisLockHelper.tryLock();
                // 判断状态是否已经发生改变
                StarTaskApplyListDO taskApplyListDO = starTaskApplyListDAO.getApplyListByApplyId(applyId);
                if (Objects.isNull(taskApplyListDO)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("查询信息为空");
                }
                Integer applyStatus = taskApplyListDO.getApplyStatus();
                if (ApplyListStatusEnum.WAITING_SUBMIT.getValue().equals(applyStatus) || ApplyListStatusEnum.REJECTED.getValue().equals(applyStatus) ||
                        ApplyListStatusEnum.getMerchantPreStatusList.contains(applyStatus)) {
                    // 更新参与人数
                    String auditReason;
                    if (CommonConstant.INTEGER_ONE.equals(operationType)) {
                        auditReason = "任务截止；";
                    } else {
                        auditReason = "商家结束任务；";
                    }
                    if (ApplyListStatusEnum.WAITING_SUBMIT.getValue().equals(applyStatus)) {
                        auditReason = auditReason + "未提交链接";
                    } else if (ApplyListStatusEnum.MERCHANT_PRE_AUDIT.getValue().equals(applyStatus)) {
                        auditReason = auditReason + "实探商家未审核";
                    } else if (ApplyListStatusEnum.MERCHANT_PRE_REFUND.getValue().equals(applyStatus)) {
                        auditReason = auditReason + "实探商家驳回";
                    } else {
                        auditReason = auditReason + "审核驳回：" + applyListDO.getRejectReason();
                    }
                    applyListDO.setCancelReason(auditReason);
                    this.updateParticipationCount(applyListDO);
                }
            } catch (Exception e) {
                LogUtil.error(log, "StarTaskCommonServiceImpl" + "." + "updateStatusToCanceled" + " >>>>> " + "执行加锁方法失败", e);
                throw ExceptionUtil.toCommonException(e);
            }
        }
    }

    /**
     * 更新参与人数
     *
     * @param applyListDO
     */
    private void updateParticipationCount(StarTaskApplyListDO applyListDO) {
        String lock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_UPDATE_PARTICIPATION_COUNT, applyListDO.getTaskId());
        try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
            redisLockHelper.tryLock();
            // 事物
            transactionTemplate.execute(status -> {
                // 修改任务状态为已取消
                starTaskApplyListDAO.applyStatusOperation(applyListDO.getApplyId(), ApplyListStatusEnum.CANCELLED.getValue(), null, applyListDO.getCancelReason());
                if (!ApplyListStatusEnum.getMerchantPreStatusList.contains(applyListDO.getApplyStatus())) {
                    // 取消需要修改人数
                    starTaskCommissionPlanDAO.updateParticipationCount(applyListDO.getCommissionPlanId());
                    // 更新任务的名额未经满状态
                    starTaskDAO.updateIsQuotaFull(applyListDO.getTaskId(), QuotaFullEnum.NOT_FULL.getValue());
                }
                return Boolean.TRUE;
            });
        } catch (Exception e) {
            LogUtil.warn(log, "StarTaskCommonServiceImpl.updateStatusToCanceled >> 更新佣金计划异常, 错误原因: ", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }
}