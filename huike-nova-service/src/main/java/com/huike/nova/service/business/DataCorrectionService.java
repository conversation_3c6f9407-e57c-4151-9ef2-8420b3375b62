package com.huike.nova.service.business;

import com.huike.nova.service.domain.param.correction.FixOrderDiscountParam;
import com.huike.nova.service.domain.param.correction.FixServiceProviderCouponVerifyRecordParam;

/**
 * 工具服务
 *
 * <AUTHOR> (<EMAIL>)
 * @version ToolsService.java, v1.0 2024/11/8 16:42 John Exp$
 */
public interface DataCorrectionService {
    /**
     * 修复订单折扣
     *
     * @param param 参数
     */
    void fixOrderDiscount(FixOrderDiscountParam param);

    /**
     * 修复服务商验券表
     *
     * @param param 参数
     */
    void fixServiceProviderCouponVerifyRecord(FixServiceProviderCouponVerifyRecordParam param);
}
