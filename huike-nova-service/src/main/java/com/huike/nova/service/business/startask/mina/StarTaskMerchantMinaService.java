/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.mina;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.startask.mina.merchant.ApplyListModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.ExportApplyListModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.FindMerchantCategoryListModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.PaymentModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.PublishStarTaskModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.PublishTaskDetailModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.PublishTaskListModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.QueryMerchantInfoModel;
import com.huike.nova.service.domain.model.startask.mina.merchant.QueryTaskMoneyModel;
import com.huike.nova.service.domain.param.startask.mina.merchant.ApplyListParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.BalancePaymentParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.ExportApplyListParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.OperateApplyListParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.OperateStarTaskParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.PublishStarTaskParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.PublishTaskDetailParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.PublishTaskListParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.QueryMerchantInfoParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.QueryTaskMoneyParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.StarTaskMinaPaymentParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.UpdateMerchantInfoParam;
import com.huike.nova.service.domain.param.startask.mina.merchant.UpdateStarTaskParam;

/**
 * <AUTHOR>
 * @version StarTaskMerchantMinaService.java, v 0.1 2023-11-25 2:16 PM ruanzy
 */
public interface StarTaskMerchantMinaService {

    /**
     * 发布任务
     *
     * @param param
     * @return
     */
    PublishStarTaskModel publish(PublishStarTaskParam param);

    /**
     * 商家发布任务列表
     *
     * @param param
     * @return
     */
    PageResult<PublishTaskListModel> list(PageParam<PublishTaskListParam> param);

    /**
     * 任务列表详情
     *
     * @param param
     * @return
     */
    PublishTaskDetailModel detail(PublishTaskDetailParam param);

    /**
     * 发布任务修改
     *
     * @param param
     */
    void update(UpdateStarTaskParam param);

    /**
     * 发布任务操作
     *
     * @param param
     */
    void operate(OperateStarTaskParam param);

    /**
     * 报名清单列表
     *
     * @param param
     * @return
     */
    PageResult<ApplyListModel> applyList(PageParam<ApplyListParam> param);

    /**
     * 报名清单操作
     *
     * @param param
     */
    void operateApplyList(OperateApplyListParam param);

    /**
     * 报名清单导出
     *
     * @param param
     * @return
     */
    ExportApplyListModel exportApplyList(ExportApplyListParam param);

    /**
     * 余额支付
     *
     * @param param
     */
    void balancePayment(BalancePaymentParam param);

    /**
     * 组合支付：余额支付+收银台支付
     *
     * @param param 参数
     */
    PaymentModel payment(StarTaskMinaPaymentParam param);

    /**
     * 查询任务金额
     *
     * @param param 入参
     * @return 出参
     */
    QueryTaskMoneyModel queryTaskMoney(QueryTaskMoneyParam param);

    /**
     * 查询商家信息
     *
     * @param param 入参
     * @return 出参
     */
    QueryMerchantInfoModel queryMerchantInfo(QueryMerchantInfoParam param);

    /**
     * 查询商家分类列表
     *
     * @return 出参
     */
    FindMerchantCategoryListModel findMerchantCategoryList();

    /**
     * 更新商家信息
     *
     * @param param 入参
     */
    void updateMerchantInfo(UpdateMerchantInfoParam param);

    /**
     * 检查手机号是否在白名单中
     *
     * @param phoneNumber 手机号
     * @return 是否在白名单中
     */
    Boolean checkAreaPhoneWhiteList(String phoneNumber);
}