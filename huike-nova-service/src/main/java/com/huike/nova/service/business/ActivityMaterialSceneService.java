/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.material.ScenesMaterialDetailModel;
import com.huike.nova.service.domain.model.material.ScenesModel;
import com.huike.nova.service.domain.param.material.ActivityParam;
import com.huike.nova.service.domain.param.material.HistoryScenesMaterialParam;
import com.huike.nova.service.domain.param.material.SaveActivityScenesParam;

/**
 * <AUTHOR>
 * @version ActivityMaterialSceneService.java, v 0.1 2022-09-01 14:46 zhangling
 */
public interface ActivityMaterialSceneService {

    /**
     * 根据活动ID获取活动下的场景素材
     *
     * @param param
     * @return
     */
    ScenesModel findSceneList(ActivityParam param);

    /**
     * 新增活动素材
     *
     * @param param
     * @return
     */
    boolean saveMaterialScene(SaveActivityScenesParam param);

    /**
     * 根据materialSceneId删除场景素材
     *
     * @param materialSceneId
     * @param activityId
     * @return
     */
    boolean deleteByMaterialSceneId(String materialSceneId, String activityId);

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    PageResult<ScenesMaterialDetailModel> findHistorySceneMaterialPage(PageParam<HistoryScenesMaterialParam> param);

    /**
     * 修改活动素材时长
     *
     * @param materialSceneId 场景素材id
     * @param videoTime       视频时长
     */
    void updateActivitySceneMaterialTime(String materialSceneId, Integer videoTime);
}