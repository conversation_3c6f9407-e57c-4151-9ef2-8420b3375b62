/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.enums.AliOssEnum;
import com.huike.nova.service.domain.model.oss.AliyunOssTokenModel;
import com.huike.nova.service.domain.model.oss.UploadPictureModel;
import com.huike.nova.service.domain.param.oss.AliyunOssTokenParam;

import java.io.File;

/**
 * <AUTHOR>
 * @version OssManager.java, v 0.1 2022-09-03 09:41 zhangling
 */
public interface OssManager {

    /**
     * 上传图片
     *
     * @param body
     * @return
     */
    UploadPictureModel uploadPicture(byte[] body);

    /**
     * 上传图片Custom Path
     *
     * @param body 图片字节
     * @param path 自定义路径
     * @return UploadPictureModel
     */
    String uploadPictureCustomPath(byte[] body, String path);

    /**
     * 获得阿里云 oss 的 token
     *
     * @param param AliyunOssTokenParam
     * @return AliyunOssTokenModel
     */
    AliyunOssTokenModel getAliyunOssToken(AliyunOssTokenParam param);

    /**
     * 上传文件
     *
     * @param file
     * @param aliOssEnum
     * @return
     */
    String uploadFile(File file, AliOssEnum aliOssEnum);

    /**
     * 上传文件(内网)
     *
     * @param file
     * @param aliOssEnum
     * @return
     */
    String uploadFileInner(File file, AliOssEnum aliOssEnum);

    /**
     * 下载文件
     *
     * @param fileName
     * @param aliOssEnum
     * @return
     */
    String downloadFile(String fileName, AliOssEnum aliOssEnum);
}