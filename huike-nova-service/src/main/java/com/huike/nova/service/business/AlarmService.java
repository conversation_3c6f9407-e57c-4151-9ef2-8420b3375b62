package com.huike.nova.service.business;

import com.huike.nova.service.domain.param.sundry.AlarmParam;

import javax.annotation.Nullable;

/**
 * 报警服务
 *
 * <AUTHOR> (<EMAIL>)
 * @version AlarmService.java, v1.0 11/13/2023 14:01 John Exp$
 */
public interface AlarmService {

    /**
     * 记录报警信息
     *
     * @param alarmParam 报警参数
     * @param sendToDingDing 是否发送到钉钉 true-是 false-否
     */
    void logAlarm(AlarmParam alarmParam, boolean sendToDingDing);


    /**
     * 记录全局错误告警信息
     *
     * @param category 分类
     * @param subCategory 子分类
     * @param message 消息描述
     * @param throwable 异常信息
     */
    void logErrorToDb(String category, String subCategory, String message, Throwable throwable, @Nullable String traceId);

    /**
     * 将DB中的AlarmLog通知到钉钉中
     */
    void alarmDbToDingDing();
}
