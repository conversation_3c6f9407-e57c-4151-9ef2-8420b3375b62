/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.star.PageQueryStarModel;
import com.huike.nova.service.domain.model.supactivity.CreateSupActivityModel;
import com.huike.nova.service.domain.model.supactivity.SupActivityDetailModel;
import com.huike.nova.service.domain.model.supactivity.SupActivityListModel;
import com.huike.nova.service.domain.model.supactivity.TiktokVideoDataUpdateModel;
import com.huike.nova.service.domain.model.supactivity.VideoDataListModel;
import com.huike.nova.service.domain.model.supactivity.WebSupActivityDetailModel;
import com.huike.nova.service.domain.param.star.PageQueryStarParam;
import com.huike.nova.service.domain.param.supactivity.ExportVideoDataParam;
import com.huike.nova.service.domain.param.supactivity.SaveSupActivityParam;
import com.huike.nova.service.domain.param.supactivity.SupActivityListParam;
import com.huike.nova.service.domain.param.supactivity.SupActivityUpdateParam;
import com.huike.nova.service.domain.param.supactivity.TiktokVideoDataUpdateParam;
import com.huike.nova.service.domain.param.supactivity.VideoDataListParam;
import com.huike.nova.service.domain.param.supactivity.VideoSynthesisRpaParam;

/**
 * <AUTHOR>
 * @version SupActivityService.java, v 0.1 2022-09-29 14:49 zhangling
 */
public interface SupActivityService {


    /**
     * 分页查询活动列表
     *
     * @param param
     * @return
     */
    PageResult<SupActivityListModel> pageQuerySupActivity(PageParam<SupActivityListParam> param);

    /**
     * 创建顾客推
     *
     * @param param 入参
     * @return CreateSupActivityModel
     */
    CreateSupActivityModel createSupActivity(SaveSupActivityParam param);

    /**
     * 关闭活动
     *
     * @param supActivityId
     * @param supActivityStatus
     * @return
     */
    boolean closeSupActivity(String supActivityId, Integer supActivityStatus);

    /**
     * 删除活动
     *
     * @param supActivityId
     * @param
     * @return
     */
    boolean deleteSupActivity(String supActivityId);

    /**
     * 获取顾客推详情
     *
     * @param supActivityId
     * @return
     */
    SupActivityDetailModel getSupActivityBySupActivityId(String supActivityId);

    /**
     * 获取Web顾客推详情
     *
     * @param supActivityId
     * @param platformType
     * @return
     */
    WebSupActivityDetailModel getWebSupActivityBySupActivityId(String supActivityId, Integer platformType);

    /**
     * 查询活动明细列表视频数据
     *
     * @param param
     * @return
     */
    PageResult<VideoDataListModel> pageQueryTikTokVideoData(PageParam<VideoDataListParam> param);

    /**
     * 修改顾客推信息
     *
     * @param param
     */
    void updateSupActivity(SupActivityUpdateParam param);

    /**
     * 导出视频数据
     *
     * @param param
     * @return
     */
    String exportVideoData(ExportVideoDataParam param);

    /**
     * 达人推选择达人列表
     *
     * @param paramDTO
     * @return
     */
    PageResult<PageQueryStarModel> starSupActivityStarList(PageParam<PageQueryStarParam> paramDTO);

    /**
     * 批量合成视频
     *
     * @param param
     */
    void rpaVideoSynthesis(VideoSynthesisRpaParam param);

    /**
     * 失败重试
     *
     * @param supActivityId
     */
    void failedRetry(String supActivityId);

    /**
     * 更新抖音视频数据
     *
     * @param param 顾客推id
     * @return TiktokVideoDataUpdateResponse
     */
    TiktokVideoDataUpdateModel tiktokVideoDataUpdate(TiktokVideoDataUpdateParam param);

}