package com.huike.nova.service.business.startask.mina.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Maps;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.WechatMinaConstants;
import com.huike.nova.common.constant.startask.StarTaskConstant;
import com.huike.nova.common.constant.startask.mina.StarTaskMinaRedisPrefixConstant;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.FbPayStatusEnum;
import com.huike.nova.common.enums.OrderPayStatusEnum;
import com.huike.nova.common.enums.OrderRefundStatusEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum;
import com.huike.nova.common.enums.startask.mina.TaskStatusEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogChangeTypeEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogRemarkTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.ExceptionUtil;
import com.huike.nova.common.util.FsDateUtils;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.common.util.StringUtil2;
import com.huike.nova.common.util.XorCipherUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.entity.StarTaskBalanceAccountDO;
import com.huike.nova.dao.entity.StarTaskBalanceLogDO;
import com.huike.nova.dao.entity.StarTaskDO;
import com.huike.nova.dao.entity.StarTaskRechargeOrderDO;
import com.huike.nova.dao.repository.StarTaskBalanceAccountDAO;
import com.huike.nova.dao.repository.StarTaskBalanceLogDAO;
import com.huike.nova.dao.repository.StarTaskDAO;
import com.huike.nova.dao.repository.StarTaskRechargeOrderDAO;
import com.huike.nova.dao.repository.StarTaskWechatMinaConfigDAO;
import com.huike.nova.sdk.payment.fubei.domain.response.FbPayQueryOrderResponse;
import com.huike.nova.sdk.wechat.mina.WechatMinaApi;
import com.huike.nova.sdk.wechat.mina.model.domain.request.WechatMinaClientTokenRequest;
import com.huike.nova.sdk.wechat.mina.model.domain.request.WechatMinaCode2SessionRequest;
import com.huike.nova.sdk.wechat.mina.model.domain.request.WechatMinaGetUserPhoneNumber;
import com.huike.nova.sdk.wechat.mina.model.domain.response.WechatMinaClientTokenResponse;
import com.huike.nova.sdk.wechat.mina.model.domain.response.WechatMinaCode2SessionResponse;
import com.huike.nova.sdk.wechat.mina.model.domain.response.WechatMinaResponse;
import com.huike.nova.sdk.wechat.mina.model.domain.response.WechatMinaUserPhoneInfoResponse;
import com.huike.nova.service.business.common.DingDingCommonService;
import com.huike.nova.service.business.fubei.FubeiPayService;
import com.huike.nova.service.business.startask.mina.StarTaskWechatMinaService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.mina.StarTaskStarMinaServiceObjMapper;
import com.huike.nova.service.domain.model.mina.customer.MinaLoginModel;
import com.huike.nova.service.domain.model.mina.wechat.WechatMinaGetUserPhoneNumberModel;
import com.huike.nova.service.domain.model.payment.FubeiPayPrepayModel;
import com.huike.nova.service.domain.model.payment.NovaPrepayModel;
import com.huike.nova.service.domain.model.startask.mina.common.MinaChannelConfigModel;
import com.huike.nova.service.domain.param.startask.mina.pay.StarTaskMinaCloseUnpaidOrderParam;
import com.huike.nova.service.domain.param.startask.mina.pay.StarTaskMinaPrepayParam;
import com.huike.nova.service.domain.param.startask.payment.fubei.FubeiPayPrepayParam;
import com.huike.nova.service.enums.payment.PaymentPayTypeEnum;
import com.huike.nova.service.enums.payment.PaymentPayWayEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 微信小程序接口
 *
 * <AUTHOR> (<EMAIL>)
 * @version StarTaskWechatMinaServiceImpl.java, v1.0 12/09/2023 09:49 John Exp$
 */
@Slf4j
@AllArgsConstructor
@Service
public class StarTaskWechatMinaServiceImpl implements StarTaskWechatMinaService, InitializingBean {

    private WechatMinaApi wechatMinaApi;

    private RedissonClient redissonClient;

    private StarTaskWechatMinaConfigDAO starTaskWechatMinaConfigDAO;

    private StarTaskRechargeOrderDAO starTaskRechargeOrderDAO;

    private StarTaskStarMinaServiceObjMapper starTaskStarMinaServiceObjMapper;

    private FubeiPayService fubeiPayService;

    private DingDingCommonService dingDingCommonService;

    private StarTaskDAO starTaskDAO;

    private StarTaskBalanceAccountDAO starTaskBalanceAccountDAO;

    private StarTaskBalanceLogDAO starTaskBalanceLogDAO;

    private TransactionTemplate transactionTemplate;

    private SysConfig sysConfig;

    @Override
    public MinaChannelConfigModel getMinaChannelConfig(String channelCode) {
        channelCode = StringUtils.defaultIfBlank(channelCode, StarTaskConstant.DEFAULT_MINA_CHANNEL);
        val key = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CONFIG, channelCode);
        // 从Redis中获得通道配置
        if (redissonClient.<String>getBucket(key).isExists()) {
            return JSONObject.parseObject(redissonClient.<String>getBucket(key).get(), MinaChannelConfigModel.class);
        }
        val starTaskWechatMinaConfigDO = starTaskWechatMinaConfigDAO.queryByChannelCode(channelCode);
        if (Objects.isNull(starTaskWechatMinaConfigDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("通道类型不存在");
        }
        val model = starTaskStarMinaServiceObjMapper.toMinaChannelConfigModel(starTaskWechatMinaConfigDO);
        redissonClient.<String>getBucket(key).set(JSONObject.toJSONString(model), 1, TimeUnit.HOURS);
        return model;
    }

    /**
     * 微信小程序登录
     *
     * @param channelCode 小程序应用通道
     * @param jsCode      小程序获取手机号码的授权码
     * @return 登录信息
     */
    @Override
    public MinaLoginModel toMinaLogin(String channelCode, String jsCode) {
        val minaConfig = getMinaChannelConfig(channelCode);
        val request = new WechatMinaCode2SessionRequest(
                minaConfig.getAppId(),
                minaConfig.getAppSecret(),
                jsCode
        );
        val data = wechatMinaApi.getRequest(WechatMinaConstants.API_MINA_CODE_TO_SESSION, request);
        val response = JSONObject.parseObject(data, WechatMinaCode2SessionResponse.class);
        checkWechatResponse(minaConfig.getAppId(), response);
        val minaLoginModel = new MinaLoginModel();
        minaLoginModel.setOpenid(response.getOpenId());
        minaLoginModel.setUnionId(response.getUnionId());
        return minaLoginModel;
    }

//    根据类型获取模版
//    根据openid和模版id列表保存订阅记录
//    触发之后 根据openid进行通知

    /**
     * 获得用户手机号
     *
     * @param channelCode 通道代码是
     * @param code        小程序获取手机号码的授权码
     * @return 手机号码信息
     */
    @Override
    public WechatMinaGetUserPhoneNumberModel getUserPhoneNumber(String channelCode, String code) {
        if (StringUtils.isBlank(code)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("code不能为空");
        }
        val minaChannelConfig = getMinaChannelConfig(channelCode);
        val accessToken = getMinaAccessToken(minaChannelConfig.getAppId(), minaChannelConfig.getAppSecret());
        val param = new WechatMinaGetUserPhoneNumber(code, null);
        val data = wechatMinaApi.postRequest(WechatMinaConstants.API_WXA_GET_USER_PHONE_NUMBER, accessToken, param);
        val response = JSONObject.parseObject(data, WechatMinaUserPhoneInfoResponse.class);
        checkWechatResponse(minaChannelConfig.getAppId(), response);
        val phoneInfo = response.getPhoneInfo();
        if (Objects.isNull(phoneInfo)) {
            throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("返回的phone_info数据为空");
        }
        val modelBuilder = WechatMinaGetUserPhoneNumberModel.builder()
                .phoneNumber(phoneInfo.getPhoneNumber())
                .purePhoneNumber(phoneInfo.getPurePhoneNumber())
                .countryCode(phoneInfo.getCountryCode());
        if (Objects.nonNull(phoneInfo.getWatermark())) {
            modelBuilder.timestamp(phoneInfo.getWatermark().getTimestamp())
                    .appId(phoneInfo.getWatermark().getAppId());
        }
        return modelBuilder.build();
    }

    /**
     * 请求小程序的AccessToken
     *
     * @return accessToken
     */
    @Override
    public WechatMinaClientTokenResponse requestMinaAccessToken(String appId, String appSecret) {
        val request = new WechatMinaClientTokenRequest(appId, appSecret);
        val data = wechatMinaApi.getRequest(WechatMinaConstants.API_OBTAIN_TOKEN, request);
        val response = JSONObject.parseObject(data, WechatMinaClientTokenResponse.class);
        checkWechatResponse(appId, response);
        return response;
    }

    @Override
    public NovaPrepayModel prepay(StarTaskMinaPrepayParam param) {
        val channelConfig = getMinaChannelConfig(param.getChannelCode());
        // 商家的登录信息
        val starTaskMinaLoginModel = LoginUtil.getStarTaskLoginBasicInfo(StarTaskIdentityTypeEnum.MERCHANT.getValue());
        // 组装参数
        val fubeiPrePayParam = new FubeiPayPrepayParam()
                .setOrderSn(param.getOrderSn())
                .setTotalAmount(param.getPayAmount())
                .setPayToken(param.getPayToken())
                .setDesc(StringUtil2.removeEmojis(param.getDesc()))
                .setAppId(channelConfig.getAppId())
                .setStoreId(channelConfig.getFubeiPayStoreId())
                .setPayUserId(param.getOpenId())
                .setPayType(PaymentPayTypeEnum.WECHAT)
                .setPayWayEnum(PaymentPayWayEnum.MINA)
                .setNotifyUrl(channelConfig.getFubeiPayCallbackUrl());
        val timeoutExpiredDt = LocalDateTime.now(CommonConstant.GMT_8).plusMinutes(sysConfig.getStarTaskPaymentExpiryMinutes());
        fubeiPrePayParam.setTimeoutExpired(timeoutExpiredDt.format(DateTimeFormatter.ofPattern(FsDateUtils.NUMBER_DATE_TIME_FORMAT)));
        LogUtil.info(log, "StarTaskWechatMinaServiceImpl.prepay >> 微信小程序预下单 >> 通道配置:{}, 付呗参数:{}", JSONObject.toJSONString(param), JSONObject.toJSONString(fubeiPrePayParam));
        // 预下单
        val prepayModel = fubeiPayService.prepay(channelConfig.getChannelCode(), fubeiPrePayParam);
        LogUtil.info(log, "StarTaskWechatMinaServiceImpl.prepay >> 微信小程序预下单 >> 预下单结果:{}", JSONObject.toJSONString(prepayModel));

        // 写入到数据库中
        insertPrepayOrderToDb(starTaskMinaLoginModel.getUserId(), starTaskMinaLoginModel.getIdentityId(), param.getTaskId(), prepayModel, channelConfig);
        return fubeiPayService.toNovaPrepayPayModel(prepayModel);
    }

    @Override
    @Async
    public void asyncCloseUnpaidOrder(StarTaskMinaCloseUnpaidOrderParam param) {
        if (StringUtils.isBlank(param.getTaskId())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务Id不能为空");
        }
        val unpaidOrderList = starTaskRechargeOrderDAO.queryOrdersByTaskId(param.getTaskId());
        if (CollectionUtil.isNotEmpty(param.getExcludeOrderSn())) {
            unpaidOrderList.removeIf(p -> param.getExcludeOrderSn().contains(p.getOrderSn()));
        }
        unpaidOrderList.forEach(p -> {
            try {
                val result = fubeiPayService.closeOrder(p.getChannelCode(), p.getOrderSn());
                if (FbPayStatusEnum.CLOSE.getValue().equals(result.getOrderStatus())) {
                    starTaskRechargeOrderDAO.updatePayStatus(p.getOrderSn(), OrderPayStatusEnum.TIMEOUT.getValue());
                }
            } catch (Exception ex) {
                LogUtil.error(log, "StarTaskWechatMinaServiceImpl.closeUnpaidOrder >> 关闭未支付订单失败 >> 订单号:{}", p.getOrderSn(), ex);
            }
        });
    }

    @Override
    public void handlePaySuccessCallback(String channelCode, String content) {
        val config = getMinaChannelConfig(channelCode);
        // 签名校验未通过
        val data = new AtomicReference<String>();
        if (fubeiPayService.isCallbackSignatureCheckFailed(content, config.getFubeiPayAppSecret(), data::set)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("签名不符合规则");
        }
        // 写入订单成功状态
        val order = JSONObject.parseObject(data.get(), FbPayQueryOrderResponse.class);
        val key = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_ORDER_LOCK, order.getOrderSn());
        try (val locker = new RedisLockHelper(key, redissonClient)) {
            locker.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES);
            // 查询订单消息
            val orderDO = starTaskRechargeOrderDAO.queryOrder(order.getMerchantOrderSn());
            // ! 更新订单状态为已支付
            if (Objects.nonNull(orderDO)) {
                // 设置订单状态
                orderDO.setPayTime(Convert.toLong(order.getFinishTime()));
                orderDO.setTotalAmount(order.getTotalAmount());
                orderDO.setNetAmount(order.getNetAmount());
                orderDO.setPayCustomerUid(order.getUserId());
                orderDO.setOutOrderSn(order.getOrderSn());
                orderDO.setChannelOrderSn(order.getChannelOrderSn());
                orderDO.setInsOrderSn(order.getInsOrderSn());
                orderDO.setMerchantRate(order.getMerchantRate());
                orderDO.setFee(order.getMerchantRate());
                orderDO.setPayChannel(WechatMinaConstants.PAY_CHANNEL_FUBEI);
                val payTypeEnum = PaymentPayTypeEnum.getEnumByFubeiPayType(order.getPayType());
                if (Objects.nonNull(payTypeEnum)) {
                    orderDO.setPayType(payTypeEnum.getCode());
                }
                // 仅处理支付成功订单
                val payStatus = FbPayStatusEnum.getByValue(order.getOrderStatus());
                if (Objects.equals(payStatus, FbPayStatusEnum.SUCCESS)) {
                    orderDO.setPayStatus(OrderPayStatusEnum.SUCCESS.getValue());
                }
                orderDO.setUpdateTime(new Date());
                // 更新订单信息
                starTaskRechargeOrderDAO.getBaseMapper().updateById(orderDO);

                // ! 以下更新任务状态、解冻余额、原先的余额解冻记录
                // 查询任务记录
                if (StringUtils.isNotBlank(orderDO.getTaskId())) {
                    val taskKey = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_OPERATE_STAR_TASK, orderDO.getTaskId());
                    try (val taskLocker = new RedisLockHelper(taskKey, redissonClient)) {
                        taskLocker.tryLock();
                        // 查询任务记录
                        val starTaskDO = starTaskDAO.getStarTaskByTaskId(orderDO.getTaskId());
                        // 更新收到回调时间的任务状态
                        starTaskRechargeOrderDAO.updateTaskStatus(orderDO.getOrderSn(), starTaskDO.getTaskStatus(), TaskStatusEnum.CANCELED.getValue().equals(starTaskDO.getTaskStatus()));

                        // 支付回调订单仅在等待支付时才进行余额和状态操作
                        // 仅在等待支付状态下，才更新任务状态为：平台审核中
                        if (!TaskStatusEnum.WAITING_FOR_PAYMENT.getValue().equals(starTaskDO.getTaskStatus())) {
                            LogUtil.warn(log, "StarTaskWechatMinaServiceImpl.handlePaySuccessCallback >> 任务状态错误(不为等待支付状态), 任务Id:{}, 状态: {}",
                                    starTaskDO.getTaskId(), starTaskDO.getTaskStatus());
                            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("任务状态不为等待状态，将跳过处理");
                        }

                        // 更新余额记录
                        transactionTemplate.executeWithoutResult(status -> {
                            // 更新余额记录，添加组合记录这条数据
                            updateBalance(starTaskDO, orderDO);
                            starTaskDO.setTaskStatus(TaskStatusEnum.PLATFORM_UNDER_REVIEW.getValue());
                            // 解冻任务中的冻结余额，并赋值到抵扣余额中(如果有冻结余额的话）
                            val frozenBalanceAmount = starTaskDO.getFrozenBalanceAmount();
                            if (frozenBalanceAmount.compareTo(BigDecimal.ZERO) > 0) {
                                starTaskDO.setFrozenBalanceAmount(BigDecimal.ZERO);
                                starTaskDO.setBalanceDeductionAmount(frozenBalanceAmount);
                            }
                            starTaskDO.setUpdateTime(new Date());
                            starTaskDAO.updateById(starTaskDO);
                        });
                        // 发送一个钉钉消息
                        val message = StrUtil.format("有新发布的任务支付成功，请及时审核。任务Id: {}, 任务名称: {}", starTaskDO.getTaskId(), starTaskDO.getTaskTitle());
                        dingDingCommonService.sendStarTaskDingDingAudit("新任务发布", message, "");
                    } catch (Exception ex) {
                        throw ExceptionUtil.toCommonException(ex);
                    }
                }
            } else {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("未找到信息");
            }

        } catch (Exception e) {
            LogUtil.warn(log, "StarTaskWechatMinaServiceImpl.handlePaySuccessCallback >> 通道处理订单失败 >> 通道:{}, 错误: ", channelCode, e);
            throw ExceptionUtil.toCommonException(e);
        }

    }

    @Override
    public String getCachedAccessTokenByAppId(String appId) {
        val redisKey = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_ACCESS_TOKEN, appId);
        String accessToken = redissonClient.<String>getBucket(redisKey).get();
        if (StringUtils.isBlank(accessToken)) {
            val configDO = starTaskWechatMinaConfigDAO.queryByAppId(appId);
            if (Objects.isNull(configDO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("未找到配置信息");
            }
            accessToken = getMinaAccessToken(appId, configDO.getAppSecret());
        }
        return accessToken;
    }


    /**
     * 更新Balance余额 - 务必放事务中进行处理
     *
     * @param starTaskDO      任务DO
     * @param rechargeOrderDO 支付订单
     */
    private void updateBalance(StarTaskDO starTaskDO, StarTaskRechargeOrderDO rechargeOrderDO) {
        try (val redisLock = new RedisLockHelper(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_APPLY_WITHDRAWAL, starTaskDO.getIdentityId()), redissonClient)) {
            redisLock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES);
            // 余额记录查询
            StarTaskBalanceAccountDO balanceAccountDO = starTaskBalanceAccountDAO.getBalanceAccountForUpdate(starTaskDO.getIdentityId());
            // 余额记录不存在
            if (Objects.isNull(balanceAccountDO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("余额记录不存在");
            }
            // 查询该订单是否关联了组合支付
            StarTaskBalanceLogDO balanceFrozenLog = starTaskBalanceLogDAO.queryLastByRechargeOrder(rechargeOrderDO.getTaskId(), rechargeOrderDO.getOrderSn());
            // 本条余额变动记录
            val rechargeLog = new StarTaskBalanceLogDO();
            // 收银台支付
            BalanceLogRemarkTypeEnum remarkTypeEnum;
            // 冻结金额
            BigDecimal frozenAmount = starTaskDO.getFrozenBalanceAmount();
            // 当前任务有冻结金额
            if (frozenAmount.compareTo(BigDecimal.ZERO) > 0) {
                if (Objects.isNull(balanceFrozenLog)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("未找到冻结订单的记录");
                }
                if (!Objects.equals(balanceFrozenLog.getFrozenAmount(), frozenAmount)) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("冻结订单的中的金额和任务金额不一致，请检查")
                            .extra("[余额设置] 任务Id:{}, 支付单Id:{}, 实际冻结余额:{}, 日志表冻结余额:{}", starTaskDO.getTaskId(), rechargeOrderDO.getOrderSn(), frozenAmount, balanceFrozenLog.getFrozenAmount());
                }
                remarkTypeEnum = BalanceLogRemarkTypeEnum.COMBINED_PAY;

                // 收银台支付
            } else {
                remarkTypeEnum = BalanceLogRemarkTypeEnum.CASHIER_PAY;
            }
            // 设置参数
            rechargeLog.setUserId(starTaskDO.getUserId());
            rechargeLog.setIdentityId(starTaskDO.getIdentityId());
            rechargeLog.setChangeType(BalanceLogChangeTypeEnum.REDUCED.getValue());
            rechargeLog.setRelationNumber(starTaskDO.getTaskId());
            rechargeLog.setRechargeOrderSn(rechargeOrderDO.getOrderSn());
            rechargeLog.setChangeAvailableBalance(balanceAccountDO.getAvailableBalance());
            rechargeLog.setAfterAvailableBalance(balanceAccountDO.getAvailableBalance());
            rechargeLog.setChangeWithdrawalFrozenBalance(balanceAccountDO.getWithdrawalFrozenBalance());
            rechargeLog.setAfterWithdrawalFrozenBalance(balanceAccountDO.getWithdrawalFrozenBalance());
            rechargeLog.setChangeIncomeTotalIncome(balanceAccountDO.getIncomeTotalIncome());
            rechargeLog.setAfterIncomeTotalIncome(balanceAccountDO.getIncomeTotalIncome());
            // 设置订单类型
            rechargeLog.setRemarkType(remarkTypeEnum.getValue());
            rechargeLog.setChangeRemark(remarkTypeEnum.getName() + "-" + starTaskDO.getTaskTitle());

            // 变动金额 = 订单金额 + 冻结金额
            BigDecimal changeAmount = rechargeOrderDO.getTotalAmount().add(frozenAmount);
            // 设置变动金额；组合支付的变动金额
            rechargeLog.setChangeAmount(changeAmount);

            // 因为冻结金额之前已经扣掉了，所以这里只扣订单金额即可
            BigDecimal beforeExpenditureTotalIncome = balanceAccountDO.getExpenditureTotalIncome();
            BigDecimal afterExpenditureTotalIncome = beforeExpenditureTotalIncome.add(rechargeOrderDO.getTotalAmount());
            // 插入支出累计收益变动前后的金额
            rechargeLog.setChangeExpenditureTotalIncome(beforeExpenditureTotalIncome);
            rechargeLog.setAfterExpenditureTotalIncome(afterExpenditureTotalIncome);

            // 设置新的支出累计收益金额
            balanceAccountDO.setExpenditureTotalIncome(afterExpenditureTotalIncome);
            // 更新时间修改
            balanceAccountDO.setUpdateTime(new Date());
            // 插入余额记录
            starTaskBalanceLogDAO.save(rechargeLog);
            // 删除原有冻结记录（如果存在）
            if (Objects.nonNull(balanceFrozenLog)) {
                balanceFrozenLog.setIsDel(DelFlagEnum.DEL.getValue());
                balanceFrozenLog.setUpdateTime(new Date());
                starTaskBalanceLogDAO.updateById(balanceFrozenLog);
            }
            // 更新余额记录
            starTaskBalanceAccountDAO.updateById(balanceAccountDO);
        } catch (Exception ex) {
            throw ExceptionUtil.toCommonException(ex);
        }
    }

    /**
     * 获得小程序AccessToken
     *
     * @param appId 小程序appId
     * @return accessToken
     */
    @Override
    public String getMinaAccessToken(String appId, String appSecret) {
        // PROD环境，按照正常逻辑获取
        if (StringUtils.isBlank(sysConfig.getToolsLaiTanBeiMinaTokenUrl())) {
            val key = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_ACCESS_TOKEN, appId);
            val token = redissonClient.<String>getBucket(key).get();
            if (StringUtils.isBlank(token)) {
                val at = requestMinaAccessToken(appId, appSecret);
                redissonClient.<String>getBucket(key).set(at.getAccessToken(), at.getExpiresIn() - 600, TimeUnit.SECONDS);
                return at.getAccessToken();
            }
            return token;

            // 非PROD环境，请求网络获得
        } else {
            val response = wechatMinaApi.getRequest(sysConfig.getToolsLaiTanBeiMinaTokenUrl(), Maps.newHashMap("appId", appId));
            val json = JSONObject.parseObject(response);
            val code = json.getString(CommonConstant.CODE);
            if (!ErrorCodeEnum.SUCCESS.getCode().equals(code)) {
                throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage(json.getString(CommonConstant.MESSAGE));
            }
            val data = json.getJSONObject(CommonConstant.DATA);
            val accessToken = data.getString("accessToken");
            return XorCipherUtil.xorDecode(accessToken, StarTaskConstant.XOR_IV);
        }
    }

    /**
     * 检查微信返回数据是否正确
     *
     * @param appId        小程序AppId
     * @param minaResponse 返回数据
     */
    private void checkWechatResponse(String appId, @Nullable WechatMinaResponse minaResponse) {
        if (Objects.isNull(minaResponse)) {
            throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("返回数据为空");
        }
        if (!minaResponse.isSuccess()) {
            // AccessToken错误
            if (WechatMinaConstants.ACCESS_TOKEN_IS_INVALID.equals(minaResponse.getErrorCode())) {
                redissonClient.<String>getBucket(StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_ACCESS_TOKEN, appId))
                        .delete();
            }
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("返回数据错误，错误原因:{}", minaResponse.getErrorDesc());
        }
    }

    /**
     * 写入至订单记录中
     *
     * @param userId            用户Id
     * @param identityId        身份Id
     * @param taskId            任务Id
     * @param prepayResultModel 统一支付结果
     * @param channelConfig     通道配置
     */
    private void insertPrepayOrderToDb(String userId, String identityId, String taskId, FubeiPayPrepayModel prepayResultModel, MinaChannelConfigModel channelConfig) {
        if (ObjectUtil.isNull(prepayResultModel) || ObjectUtil.isNull(channelConfig)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("参数错误，预下单结果或通道配置不能为空");
        }

        val recordDO = new StarTaskRechargeOrderDO();
        recordDO.setUserId(userId);
        recordDO.setIdentityId(identityId);
        // channelConfig不为空
        recordDO.setAppId(channelConfig.getAppId());
        recordDO.setChannelCode(channelConfig.getChannelCode());
        // 订单号为预下单的外部订单
        recordDO.setOrderSn(prepayResultModel.getMerchantOrderSn());
        // 外部订单为预下单的订单号
        recordDO.setOutOrderSn(prepayResultModel.getOrderSn());
        recordDO.setTotalAmount(prepayResultModel.getTotalAmount());
        recordDO.setPayChannel(WechatMinaConstants.PAY_CHANNEL_FUBEI);
        val payType = PaymentPayTypeEnum.getEnumByFubeiPayType(prepayResultModel.getPayType());
        if (Objects.nonNull(payType)) {
            recordDO.setPayType(payType.getCode());
        }
        recordDO.setPayAppId(channelConfig.getAppId());
        recordDO.setPayCustomerUid(prepayResultModel.getUserId());
        recordDO.setPayStatus(OrderPayStatusEnum.WAITING.getValue());
        recordDO.setRefundStatus(OrderRefundStatusEnum.NOT_REFUND.getValue());
        recordDO.setTaskId(taskId);
        recordDO.setThirdMerchantId(ObjectUtil.defaultIfNull(prepayResultModel.getMerchantId(), CommonConstant.ZERO).toString());
        recordDO.setThirdStoreId(ObjectUtil.defaultIfNull(prepayResultModel.getStoreId(), CommonConstant.ZERO).toString());
        recordDO.setChannelPayId(prepayResultModel.getPrepayId());
        starTaskRechargeOrderDAO.getBaseMapper().insert(recordDO);
    }

    @Override
    public void afterPropertiesSet() {
        // 初始化所有配置
        reloadMinaConfig();
    }

    private void reloadMinaConfig() {
        starTaskWechatMinaConfigDAO.getAllConfigurations().forEach(config -> {
            LogUtil.info(log, "StarTaskWechatMinaServiceImpl.reloadMinaConfig >> 初始化通道配置:{}", config.getChannelCode());
            val channelConfig = new MinaChannelConfigModel();
            BeanUtils.copyProperties(config, channelConfig);
            val key = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CONFIG, config.getChannelCode());
            redissonClient.<String>getBucket(key).set(JSONObject.toJSONString(channelConfig));
            fubeiPayService.configFubeiApi(config.getChannelCode(), config.getFubeiPayAppId(), config.getFubeiPayAppSecret());
        });
    }
}
