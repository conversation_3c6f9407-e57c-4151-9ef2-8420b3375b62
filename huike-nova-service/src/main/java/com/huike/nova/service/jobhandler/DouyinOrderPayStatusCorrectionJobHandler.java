package com.huike.nova.service.jobhandler;

import com.huike.nova.service.business.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 抖音订单 支付状态修正
 *
 * <AUTHOR> (<EMAIL>)
 * @version DouyinOrderPayStatusCorrectionJobHandler.java, v1.0 12/06/2023 16:09 John Exp$
 */
@Component
@Slf4j
@JobHandler("douyinOrderPayStatusCorrectionJobHandler")
@AllArgsConstructor
public class DouyinOrderPayStatusCorrectionJobHandler extends IJobHandler {

    private TaskService taskService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        taskService.correctionDouyinOrderPayFailed(null, null);
        return ReturnT.SUCCESS;
    }
}
