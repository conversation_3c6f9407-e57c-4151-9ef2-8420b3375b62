package com.huike.nova.service.exception;

import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.sdk.meituan.dianping.enums.DzOpenSendReceiptErrorCodeEnum;
import lombok.Getter;

/**
 * 美团大众点评接口异常
 * 美团的SPI需要记录错误码
 *
 * <AUTHOR> (<EMAIL>)
 * @version MeiTuanDzOpenApiException.java, v1.0 06/02/2024 20:14 John Exp$
 */
@Getter
public class MeiTuanDzOpenApiException extends CommonException {

    private final DzOpenSendReceiptErrorCodeEnum errorCode;

    public MeiTuanDzOpenApiException() {
        this(DzOpenSendReceiptErrorCodeEnum.OTHER_REASON);
    }

    public MeiTuanDzOpenApiException(DzOpenSendReceiptErrorCodeEnum errorCodeEnum) {
        super(ErrorCodeEnum.DIAN_PING_API_ERROR.getCode(), errorCodeEnum.getDesc());
        this.errorCode = errorCodeEnum;
    }

    public MeiTuanDzOpenApiException(DzOpenSendReceiptErrorCodeEnum errorCodeEnum, String msg) {
        super(ErrorCodeEnum.DIAN_PING_API_ERROR.getCode(), msg);
        this.errorCode = errorCodeEnum;
    }
}
