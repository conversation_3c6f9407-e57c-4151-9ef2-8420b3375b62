/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@JobHandler("qykSubscribeMessageSendJobHandler")
@AllArgsConstructor
public class QykSubscribeMessageSendJobHandler extends IJobHandler {

    private TaskService taskService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("qykSubscribeMessageSendJobHandler.execute >> 开始：time = {}", DateUtil.now());
        // 推送订阅
        taskService.sendSubscribe();
        XxlJobLogger.log("qykSubscribeMessageSendJobHandler.execute >> 结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}