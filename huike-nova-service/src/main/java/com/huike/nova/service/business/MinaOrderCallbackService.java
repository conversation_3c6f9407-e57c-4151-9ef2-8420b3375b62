/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.alibaba.fastjson.JSONObject;
import com.huike.nova.service.domain.model.mina.orderCallback.CreateOrderModel;
import com.huike.nova.service.domain.model.mina.orderCallback.CreateRefundModel;
import com.huike.nova.service.domain.model.tiktokopen.DouyinDeveloperOperateRefundModel;
import com.huike.nova.service.domain.param.mina.order.MinaOrderDeveloperOperateRefundParam;

/**
 * <AUTHOR>
 * @version MinaOrderCallbackService.java, v 0.1 2022-10-22 11:34 AM ruanzy
 */
public interface MinaOrderCallbackService {

    /**
     * 预下单
     *
     * @param msg
     * @param channelCode
     * @return
     */
    CreateOrderModel createOrder(String msg, String channelCode);

    /**
     * 订单支付
     *
     * @param msg
     */
    void payOrder(String msg);

    /**
     * 订单退款
     *
     * @param msg
     * @return
     */
    CreateRefundModel createRefund(String msg);

    /**
     * 退款结果通知
     *
     * @param msg
     */
    void refundNotice(String msg);

    /**
     * 撤销核销通知
     *
     * @param msg
     */
    void couponVerifyCancel(String msg);

    /**
     * 核销通知
     *
     * @param msg
     */
    void couponVerify(String msg);

    /**
     * 开发者退款
     *
     * @param param 参数
     * @return Json参数
     */
    DouyinDeveloperOperateRefundModel developerOperateRefund(MinaOrderDeveloperOperateRefundParam param);

    /**
     * 待支付订单状态修正
     *
     * @param outOrderSn 抖音订单号
     */
    void correctionDouyinWaitingPayOrder(String outOrderSn);
}