package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.param.starmanage.*;
import com.huike.nova.service.domain.result.starmanage.AuthCodePosterResult;
import com.huike.nova.service.domain.result.starmanage.QueryDistributionInfoResult;
import com.huike.nova.service.domain.result.starmanage.QueryDistributionRecordResult;
import com.huike.nova.service.domain.result.starmanage.QueryGroupListResult;

/**
 * 达人管理
 *
 * <AUTHOR>
 * @date 2023/2/15 14:54
 * @copyright 2022 barm Inc. All rights reserved
 */
public interface StarManageService {

    /**
     * 分组列表
     *
     * @param param
     * @return
     */
    QueryGroupListResult queryGroupList(QueryGroupListParam param);

    /**
     * 添加分组
     *
     * @param param
     */
    void addGroup(AddGroupParam param);

    /**
     * 修改分组
     *
     * @param param
     */
    void updateGroup(UpdateGroupParam param);

    /**
     * 删除分组
     * @param param
     */
    void deleteGroup(DeleteGroupParam param);

    /**
     * 分组排序
     *
     * @param param
     */
    void groupSort(GroupSortParam param);

    /**
     * 达人移动
     * @param param
     */
    void starGroup(StarGroupParam param);

    /**
     * 获取达人授权码海报链接
     *
     * @param param
     * @return
     */
    AuthCodePosterResult getAuthCodePoster(AuthCodePosterParam param);

    /**
     * 达人分配记录
     *
     * @param param
     * @return
     */
    PageResult<QueryDistributionRecordResult> queryDistributionRecord(PageParam<QueryDistributionRecordParam> param);

    /**
     * 分配记录详情
     *
     * @param param
     * @return
     */
    QueryDistributionInfoResult queryDistributionInfo(QueryDistributionInfoParam param);

    /**
     * 达人分配
     *
     * @param param
     */
    void starDistribution(StarDistributionParam param);
}
