/*
 * ailike.com
 * Copyright (C) 2022-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.util.DingTalkUtil;
import com.huike.nova.common.util.FsDateUtils;
import com.huike.nova.common.util.LogUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@JobHandler("alipayGroupPurchaseAdJobHandler")
@AllArgsConstructor
public class AlipayGroupPurchaseAdJobHandler extends IJobHandler {

    private RedissonClient redissonClient;


    /**
     * 支付宝团购广告消息通知
     *
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("AlipayGroupPurchaseAdJobHandler.execute >> 支付宝团购广告消息通知开始：time = {}", DateUtil.now());
        LogUtil.info(log, "AlipayGroupPurchaseAdJobHandler.execute >> 接口开始 >> 支付宝团购广告消息通知开始");
        String dateStr = DateUtil.format(DateUtil.offsetDay(new Date(), -1), FsDateUtils.NUMBER_DATE_FORMAT);
        RAtomicLong entranceFlow = redissonClient.getAtomicLong(StrUtil.format(RedisPrefixConstant.AD_SHOW_ENTRANCE_FLOW, dateStr));
        RAtomicLong exposureFlow = redissonClient.getAtomicLong(StrUtil.format(RedisPrefixConstant.AD_SHOW_EXPOSURE_FLOW, dateStr));
        LogUtil.info(log, "AlipayGroupPurchaseAdJobHandler.execute >> 数据为 >> entranceFlow = {},exposureFlow = {}", entranceFlow.get(), exposureFlow.get());
        String msg = MessageFormat.format("小程序入口流量为:{0}   \n   小程序广告曝光数量为:{1}", entranceFlow.get(), exposureFlow.get());
        DingTalkUtil.sendAlipayGroupAdMessage("6149674b212eaca9a8fc71a8a0ee083089e63ba449abdddef4057aafa0cb0142", "支付宝团购广告数据", msg);
        LogUtil.info(log, "AlipayGroupPurchaseAdJobHandler.execute >> 接口结束 >> 支付宝团购广告消息通知开始");
        XxlJobLogger.log("AlipayGroupPurchaseAdJobHandler.execute >> 支付宝团购广告消息通知结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}