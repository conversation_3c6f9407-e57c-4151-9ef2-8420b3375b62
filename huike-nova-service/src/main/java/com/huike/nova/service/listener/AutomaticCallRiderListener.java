/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.listener;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.huike.nova.common.enums.takeout.ChangeStatusEnum;
import com.huike.nova.common.enums.takeout.TakeoutOrderStatusEnum;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.dao.entity.TakeoutOrderDO;
import com.huike.nova.dao.repository.TakeoutOrderDAO;
import com.huike.nova.service.business.takeout.TakeoutCommonService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version AutomaticCallRiderListener.java, v 0.1 2023-02-06 4:00 PM ruanzy
 */

@Slf4j
@Component
@AllArgsConstructor
public class AutomaticCallRiderListener implements MessageListener {

    private TakeoutOrderDAO takeoutOrderDAO;

    private TakeoutCommonService takeoutCommonService;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String orderSn = new String(message.getBody(), StandardCharsets.UTF_8);
        LogUtil.info(log, "AutomaticCallRiderListener.consume >> 自动呼叫骑手 >> message = {}", message);
        //第一步： 获取入参orderSn,查询订单信息，判断订单状态
        TakeoutOrderDO takeoutOrderInfo = takeoutOrderDAO.getTakeoutOrderInfo(orderSn);
        if (Objects.isNull(takeoutOrderInfo)) {
            return Action.CommitMessage;
        }
        //前置：订单状态为待配送，其他状态都不做后续处理
        if (!TakeoutOrderStatusEnum.WAITING_DISTRIBUTION.getValue().equals(takeoutOrderInfo.getOrderStatus())) {
            return Action.CommitMessage;
        }
        //第二步：修改订单状态为 配送中，增加流转日志 开始呼叫骑手
        takeoutCommonService.operateTakeout(orderSn, ChangeStatusEnum.CALL_RIDER, TakeoutOrderStatusEnum.IN_DISTRIBUTION.getValue());
        return Action.CommitMessage;
    }
}