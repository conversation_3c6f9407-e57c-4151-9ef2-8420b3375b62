package com.huike.nova.service.business;

import com.huike.nova.service.domain.model.mina.alipayMina.AdShowCheckModel;
import com.huike.nova.service.domain.model.mina.alipayMina.AppletSilentAuthModel;
import com.huike.nova.service.domain.model.mina.alipayMina.FindUserCouponListModel;
import com.huike.nova.service.domain.model.mina.alipayMina.GetAlipayMinaHomeDataModel;
import com.huike.nova.service.domain.model.mina.alipayMina.GetCityPlazaListModel;
import com.huike.nova.service.domain.model.mina.alipayMina.GetContainsPlazaCityListModel;
import com.huike.nova.service.domain.model.mina.alipayMina.GetUserCouponModel;
import com.huike.nova.service.domain.param.mina.alipayMina.AdShowCheckParam;
import com.huike.nova.service.domain.param.mina.alipayMina.AppletSilentAuthParam;
import com.huike.nova.service.domain.param.mina.alipayMina.CouponLockParam;
import com.huike.nova.service.domain.param.mina.alipayMina.FindUserCouponListParam;
import com.huike.nova.service.domain.param.mina.alipayMina.GetAlipayMinaHomeDataParam;
import com.huike.nova.service.domain.param.mina.alipayMina.GetCityPlazaListParam;
import com.huike.nova.service.domain.param.mina.alipayMina.GetContainsPlazaCityListParam;
import com.huike.nova.service.domain.param.mina.alipayMina.GetUserCouponParam;
import com.huike.nova.service.domain.param.mina.alipayMina.PayCouponVerifyParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年11月08日 13:43
 */
public interface AlipayMinaService {

    /**
     * 支付宝小程序用户优惠券查询
     *
     * @param param 查询参数
     * @return 优惠券列表
     */
    List<FindUserCouponListModel> findUserCouponList(FindUserCouponListParam param);


    /**
     * 支付宝小程序优惠券查询
     *
     * @param param 查询参数
     * @return 优惠券
     */
    GetUserCouponModel getUserCoupon(GetUserCouponParam param);

    /**
     * 支付宝小程序优惠券锁定
     *
     * @param param 锁定参数
     */
    void couponLock(CouponLockParam param);

    /**
     * 支付代金券核销
     *
     * @param param 支付参数
     */
    void payCouponVerify(PayCouponVerifyParam param);

    /**
     * 支付宝小程序首页数据
     *
     * @param param 查询参数
     * @return 首页数据
     */
    GetAlipayMinaHomeDataModel getAlipayMinaHomeData(GetAlipayMinaHomeDataParam param);

    /**
     * 支付宝小程序城市门店列表
     *
     * @param param 查询参数
     * @return 门店列表
     */
    List<GetCityPlazaListModel> getCityPlazaList(GetCityPlazaListParam param);

    /**
     * 获取包含广场城市列表
     *
     * @param param 查询参数
     * @return 城市列表
     */
    List<GetContainsPlazaCityListModel> getContainsPlazaCityList(GetContainsPlazaCityListParam param);


    /**
     * 广告展示检查
     *
     * @param param 检查参数
     * @return 检查结果
     */
    AdShowCheckModel adShowCheck(AdShowCheckParam param);

    /**
     * 静默授权
     *
     * @param param 入参
     * @return 静默授权出参
     */
    AppletSilentAuthModel appletSilentAuth(AppletSilentAuthParam param);
}
