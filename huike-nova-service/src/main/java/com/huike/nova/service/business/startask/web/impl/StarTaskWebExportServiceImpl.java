package com.huike.nova.service.business.startask.web.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.google.common.io.Files;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.constant.startask.StarTaskConstant;
import com.huike.nova.common.constant.startask.web.StarTaskWebCommonConstant;
import com.huike.nova.common.enums.EnvEnum;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum;
import com.huike.nova.common.enums.startask.web.OperateExportBusinessTypeEnum;
import com.huike.nova.common.enums.startask.web.OperateExportPlatformTypeEnum;
import com.huike.nova.common.enums.startask.web.OperateExportTaskStatusEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.common.util.BizNoBuildUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.UrlEncodeUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.param.startask.ApplyAuditRecordsPageListDTO;
import com.huike.nova.dao.domain.param.startask.BalanceDetailsPageListParamDTO;
import com.huike.nova.dao.domain.param.startask.WebApplyListParamDTO;
import com.huike.nova.dao.domain.param.startask.WebExportListParamDTO;
import com.huike.nova.dao.domain.param.startask.WebMerchantListParamDTO;
import com.huike.nova.dao.domain.param.startask.WebTaskListParamDTO;
import com.huike.nova.dao.domain.param.startask.WithdrawPageListParamDTO;
import com.huike.nova.dao.entity.StarTaskOcExportTaskDO;
import com.huike.nova.dao.repository.StarTaskOcExportTaskDAO;
import com.huike.nova.sdk.aliyun.oss.AliyunOssClient;
import com.huike.nova.sdk.aliyun.oss.AliyunOssConfig;
import com.huike.nova.service.business.startask.web.StarTaskWebExportService;
import com.huike.nova.service.business.startask.web.StarTaskWebMerchantService;
import com.huike.nova.service.business.startask.web.StarTaskWebStarService;
import com.huike.nova.service.business.startask.web.StarTaskWebTaskService;
import com.huike.nova.service.business.startask.web.StarTaskWebWithdrawService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.web.StarTaskWebExportServiceObjMapper;
import com.huike.nova.service.domain.model.startask.agentweb.login.StarTaskAgentWebLoginModel;
import com.huike.nova.service.domain.model.startask.web.export.StarTaskExportTaskModel;
import com.huike.nova.service.domain.model.startask.web.login.StarTaskWebLoginModel;
import com.huike.nova.service.domain.param.startask.web.export.StarTaskWebExportTaskListParam;
import com.xxl.job.core.log.XxlJobLogger;
import io.reactivex.Observable;
import io.reactivex.schedulers.Schedulers;
import lombok.AllArgsConstructor;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import java.io.File;
import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;

/**
 * 导出服务
 *
 * <AUTHOR> (<EMAIL>)
 * @version StarTaskWebExportServiceImpl.java, v1.0 12/13/2023 14:48 John Exp$
 */
@Service
@Slf4j
@AllArgsConstructor
public class StarTaskWebExportServiceImpl implements StarTaskWebExportService {

    private StarTaskOcExportTaskDAO starTaskOcExportTaskDAO;

    private SysConfig sysConfig;

    private StarTaskWebExportServiceObjMapper starTaskWebExportServiceObjMapper;

    private RedissonClient redissonClient;

    /**
     * 导出文件后缀的时间格式
     */
    private static final String EXPORT_SUFFIX_DATE_TIME_PATTERN = "yyyyMMdd_HHmmss";

    /**
     * 跳过不处理的类型
     */
    private final ImmutableSet<OperateExportTaskStatusEnum> taskStatusSet =
            Sets.immutableEnumSet(OperateExportTaskStatusEnum.PROCESSING, OperateExportTaskStatusEnum.FINISHED);

    /**
     * 新增导出任务
     *
     * @param businessType 业务枚举
     * @param param        参数值
     * @param loginModel   登录态获取参数，可空
     */
    @Override
    public void addExportTask(@NotNull OperateExportBusinessTypeEnum businessType, @Nullable Object param, @Nullable StarTaskWebLoginModel loginModel) {
        // 获得导出的任务名（文件名）
        val taskName = taskName(businessType.getTaskNamePrefix());
        val webLoginModel = ObjectUtil.defaultIfNull(loginModel, LoginUtil.getWebStarTaskLoginBasicInfo());
        val record = createDefaultRecord(taskName, businessType, param, webLoginModel);
        starTaskOcExportTaskDAO.getBaseMapper().insert(record);
        // 通知执行
        notifyExecuteTasks();
    }

    /**
     * 新增导出任务(区代后台)
     *
     * @param businessType 业务枚举
     * @param param        参数值
     * @param loginModel   登录态获取参数，可空
     */
    @Override
    public void addAgentExportTask(@NotNull OperateExportBusinessTypeEnum businessType, @Nullable Object param, @Nullable StarTaskAgentWebLoginModel loginModel) {
        // 获得导出的任务名（文件名）
        val taskName = taskName(businessType.getTaskNamePrefix());
        val webLoginModel = ObjectUtil.defaultIfNull(loginModel, LoginUtil.getAgentWebStarTaskLoginBasicInfo());
        val record = createAgentDefaultRecord(taskName, businessType, param, webLoginModel);
        starTaskOcExportTaskDAO.getBaseMapper().insert(record);
        // 通知执行
        notifyExecuteTasks();
    }

    @Override
    public void applyExportMerchantList(WebMerchantListParamDTO param, @Nullable StarTaskWebLoginModel loginModel) {
        addExportTask(OperateExportBusinessTypeEnum.MERCHANT_MANAGEMENT, param, loginModel);
    }

    @Override
    public void applyExportBalanceDetailsList(Integer identityType, BalanceDetailsPageListParamDTO param, @Nullable StarTaskWebLoginModel loginModel) {
        val identityTypeEnum = StarTaskIdentityTypeEnum.getByValue(identityType);
        OperateExportBusinessTypeEnum businessType = OperateExportBusinessTypeEnum.STAR_BALANCE_DETAILS_LIST;
        if (Objects.nonNull(identityTypeEnum)) {
            switch (identityTypeEnum) {
                case MERCHANT:
                    businessType = OperateExportBusinessTypeEnum.MERCHANT_BALANCE_DETAILS_LIST;
                    break;

                default:
                case STAR:
                    break;
            }
        }
        addExportTask(businessType, param, loginModel);
    }

    @Override
    public void applyExportStarList(WebMerchantListParamDTO param, @Nullable StarTaskWebLoginModel loginModel) {
        addExportTask(OperateExportBusinessTypeEnum.STAR_MANAGEMENT, param, loginModel);
    }

    @Override
    public void applyExportWithdrawalList(WithdrawPageListParamDTO param, @Nullable StarTaskWebLoginModel loginModel) {
        addExportTask(OperateExportBusinessTypeEnum.WITHDRAWAL_APPLICATION, param, loginModel);
    }

    @Override
    public void applyExportWebTaskList(WebTaskListParamDTO param, @Nullable StarTaskWebLoginModel loginModel) {
        addExportTask(OperateExportBusinessTypeEnum.WEB_TASK_LIST, param, loginModel);
    }

    @Override
    public void applyExportWebTaskApplyList(WebApplyListParamDTO param, @Nullable StarTaskWebLoginModel loginModel) {
        addExportTask(OperateExportBusinessTypeEnum.WEB_TASK_TASK_APPLY_LIST, param, loginModel);
    }

    @Override
    public void executeExportTask(StarTaskOcExportTaskDO starTaskOcExportTaskDO) {
        if (Objects.isNull(starTaskOcExportTaskDO)) {
            return;
        }
        // 判断当前的任务状态
        val operateExportTaskStatusEnum = OperateExportTaskStatusEnum.getByValue(starTaskOcExportTaskDO.getTaskStatus());
        if (taskStatusSet.contains(operateExportTaskStatusEnum)) {
            return;
        }
        // 任务Id
        val taskId = starTaskOcExportTaskDO.getTaskId();
        // 获得业务类型
        val bizType = OperateExportBusinessTypeEnum.getByName(starTaskOcExportTaskDO.getBusinessType());
        if (Objects.isNull(bizType)) {
            starTaskOcExportTaskDAO.updateTaskStatus(taskId, OperateExportTaskStatusEnum.FAILED, StarTaskWebCommonConstant.EXPORT_ERROR_BUSINESS_NOT_FOUND);
            return;
        }
        // 异步导出任务
        Observable<File> observable = null;
        switch (bizType) {
            // 商户管理
            case MERCHANT_MANAGEMENT:

                // 区代后台-商户管理
            case AGENT_MERCHANT_MANAGEMENT: {
                val param = JSONObject.parseObject(starTaskOcExportTaskDO.getExportParameter(), WebMerchantListParamDTO.class);
                observable = SpringUtil.getBean(StarTaskWebMerchantService.class).export(starTaskOcExportTaskDO.getTaskName(), param);
                break;
            }

            // 达人管理
            case STAR_MANAGEMENT:

                // 区代后台-达人管理
            case AGENT_STAR_MANAGEMENT: {
                val param = JSONObject.parseObject(starTaskOcExportTaskDO.getExportParameter(), WebMerchantListParamDTO.class);
                observable = SpringUtil.getBean(StarTaskWebStarService.class).export(starTaskOcExportTaskDO.getTaskName(), param);
                break;
            }

            // 提现申请
            case WITHDRAWAL_APPLICATION: {
                val param = JSONObject.parseObject(starTaskOcExportTaskDO.getExportParameter(), WithdrawPageListParamDTO.class);
                observable = SpringUtil.getBean(StarTaskWebWithdrawService.class).export(starTaskOcExportTaskDO.getTaskName(), param);
                break;
            }

            // 任务列表
            case WEB_TASK_LIST:

                // 区代后台-任务列表
            case AGENT_WEB_TASK_LIST: {
                val param = JSONObject.parseObject(starTaskOcExportTaskDO.getExportParameter(), WebTaskListParamDTO.class);
                observable = SpringUtil.getBean(StarTaskWebTaskService.class).export(starTaskOcExportTaskDO.getTaskName(), param);
                break;
            }

            // 任务报名申请
            case WEB_TASK_TASK_APPLY_LIST:

                // 区代后台-任务报名申请
            case AGENT_WEB_TASK_TASK_APPLY_LIST: {
                val param = JSONObject.parseObject(starTaskOcExportTaskDO.getExportParameter(), WebApplyListParamDTO.class);
                observable = SpringUtil.getBean(StarTaskWebTaskService.class).exportApplyList(starTaskOcExportTaskDO.getTaskName(), param);
                break;
            }

            // 账户明细
            case MERCHANT_BALANCE_DETAILS_LIST:

                // 收益明细
            case STAR_BALANCE_DETAILS_LIST:

                // 区代后台-收益明细
            case AGENT_STAR_BALANCE_DETAILS_LIST:

                // 区代后台-账户明细
            case AGENT_MERCHANT_BALANCE_DETAILS_LIST: {
                val param = JSONObject.parseObject(starTaskOcExportTaskDO.getExportParameter(), BalanceDetailsPageListParamDTO.class);
                observable = SpringUtil.getBean(StarTaskWebStarService.class).exportBalanceDetailsList(starTaskOcExportTaskDO.getTaskName(), param);
                break;
            }

            // 报名审核记录
            case APPLY_AUDIT_RECORD: {
                val param = JSONObject.parseObject(starTaskOcExportTaskDO.getExportParameter(), ApplyAuditRecordsPageListDTO.class);
                observable = SpringUtil.getBean(StarTaskWebTaskService.class).exportApplyAuditRecord(starTaskOcExportTaskDO.getTaskName(), param);
                break;
            }
            default:
                break;
        }
        if (Objects.isNull(observable)) {
            return;
        }
        observable.flatMap(file -> uploadFileToOssStorage(file, starTaskOcExportTaskDO.getUserId(), starTaskOcExportTaskDO.getTaskName()))
                .subscribeOn(Schedulers.computation())
                .observeOn(Schedulers.trampoline())
                // 开始处理
                .doOnSubscribe(disposable -> {
                    // 设置状态为：处理中
                    starTaskOcExportTaskDAO.updateTaskStatus(taskId, OperateExportTaskStatusEnum.PROCESSING);
                })
                .blockingSubscribe(s -> {
                    LogUtil.info(log, "StarTaskWebExportServiceImpl.executeExportTask >> 导出中心 >> 任务:{} 导出成功, 地址:{}", taskId, UrlEncodeUtil.urlDecode(s));
                    starTaskOcExportTaskDAO.setExportTaskSuccess(taskId, s);
                }, error -> {
                    LogUtil.warn(log, "StarTaskWebExportServiceImpl.executeExportTask >> 导出中心 >> 任务:{} 导出失败, 错误:", taskId, error);
                    String errorMsg = StarTaskConstant.EXPORT_ERROR;
                    if (error instanceof CommonException) {
                        errorMsg = error.getMessage();
                        // 如果抛出的错误是：文件内容为空
                        if (((CommonException) error).getErrorCodeEnum() == ErrorCodeEnum.EXPORT_FILE_IS_NO_CONTENT) {
                            // 设置错误内容：文件内容为空
                            starTaskOcExportTaskDAO.updateTaskStatus(taskId, OperateExportTaskStatusEnum.NO_DATA);
                            return;
                        }
                    }
                    // 设置错误内容：文件状态失败
                    starTaskOcExportTaskDAO.updateTaskStatus(taskId, OperateExportTaskStatusEnum.FAILED, errorMsg);
                });
    }

    /**
     * 导出任务列表
     *
     * @param param 查询参数
     * @return 导出任务列表
     */
    @Override
    public PageResult<StarTaskExportTaskModel> list(PageParam<StarTaskWebExportTaskListParam> param) {
        if (Objects.isNull(param)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("参数不能为空");
        }
        val loginModel = LoginUtil.getWebStarTaskLoginBasicInfo();
        PageParam<WebExportListParamDTO> pageParam = new PageParam<>();
        pageParam.setPage(param.getPage());
        pageParam.setPageSize(param.getPageSize());
        val query = new WebExportListParamDTO()
                .setBusinessType(param.getQuery().getBusinessType())
                .setUserId(loginModel.getOperatorId());
        pageParam.setQuery(query);
        val dataRecords = starTaskOcExportTaskDAO.queryPageList(pageParam);
        val list = starTaskWebExportServiceObjMapper.toStarTaskOcExportTaskListPageResponse(dataRecords);
        list.getRecords().forEach(records ->
        {
            records.setTaskStatusName(OperateExportTaskStatusEnum.getByValue(records.getTaskStatus()).getDesc());
            records.setCanDelete(!Objects.equals(records.getTaskStatus(), OperateExportTaskStatusEnum.PROCESSING.getTaskStatus()));
        });
        return list;
    }

    /**
     * 删除任务
     *
     * @param taskId 任务Id
     */
    @Override
    public void deleteTask(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("taskId不能为空");
        }
        val userId = LoginUtil.getWebStarTaskLoginBasicInfo();
        if (Objects.isNull(userId)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("请登录后重试，不能为空");
        }
        val starTaskOcExportTaskDO = starTaskOcExportTaskDAO.queryByTaskIdAndBusinessType(userId.getOperatorId(), taskId);
        if (Objects.isNull(starTaskOcExportTaskDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("未找到用户下的任务Id");
        }
        // 删除对应任务
        starTaskOcExportTaskDAO.deleteTaskRecord(taskId);
        // TODO 删除文件?? 是否要加？
    }

    @Override
    public void notifyExecuteTasks() {
        val redisQueueKey = StrUtil.format(RedisPrefixConstant.BLOCKING_QUEUE_STAR_TASK_EXPORT_CENTER_EXECUTING, sysConfig.getEnv());
        val taskQueue = redissonClient.<String>getBlockingQueue(redisQueueKey, RedisPrefixConstant.DEFAULT_REDIS_STRING_CODEC);
        taskQueue.add(CommonConstant.ONE_STR);
    }

    @Override
    public void cleanUp() {
        // 获得7天之前的记录
        val deadlineLDT = LocalDate.now(CommonConstant.GMT_8).minusDays(7).atStartOfDay();
        val deadlineTimestamp = deadlineLDT.toInstant(CommonConstant.GMT_8).toEpochMilli();
        XxlJobLogger.log("导出中心删除过期记录：截止日期: {}", deadlineLDT);
        val count = starTaskOcExportTaskDAO.updateTaskExpiredStatus(new Date(deadlineTimestamp));
        XxlJobLogger.log("导出中心，已删除{}条记录..", count);
    }

    /**
     * 申请审核记录导出
     *
     * @param requestParam 导出参数
     */
    @Override
    public void applyAuditRecordsExcel(ApplyAuditRecordsPageListDTO requestParam) {
        // 获取登录信息
        StarTaskWebLoginModel loginBasicInfo = LoginUtil.getWebStarTaskLoginBasicInfo();
        addExportTask(OperateExportBusinessTypeEnum.APPLY_AUDIT_RECORD, requestParam, loginBasicInfo);
    }

    /**
     * 生成任务名称
     *
     * @param prefix 前缀
     * @return 前缀-时间戳时分秒
     */
    private String taskName(String prefix) {
        val suffix = DateUtil.format(new DateTime(), EXPORT_SUFFIX_DATE_TIME_PATTERN);
        return StrUtil.format("{}-{}", prefix, suffix);
    }

    /**
     * 创建默认的导出任务记录
     *
     * @param taskName              任务名称
     * @param businessTypeEnum      业务类型
     * @param parameter             参数
     * @param starTaskWebLoginModel 登录态获取参数，可空
     * @return StarTaskOcExportTaskDO
     */
    @Synchronized
    private StarTaskOcExportTaskDO createDefaultRecord(@Nonnull String taskName, OperateExportBusinessTypeEnum businessTypeEnum, Object parameter, StarTaskWebLoginModel starTaskWebLoginModel) {
        val record = new StarTaskOcExportTaskDO();
        record.setTaskId(BizNoBuildUtil.build());
        record.setTaskName(taskName);
        val param = ObjectUtil.defaultIfNull(parameter, CommonConstant.EMPTY_OBJ);
        record.setExportParameter(JSONObject.toJSONString(param));
        record.setBusinessType(businessTypeEnum.toString());
        record.setTaskStatus(OperateExportTaskStatusEnum.CREATED.getTaskStatus());
        record.setPlatformType(OperateExportPlatformTypeEnum.OPERATION.getPlatform());
        if (Objects.nonNull(starTaskWebLoginModel)) {
            record.setUserId(starTaskWebLoginModel.getOperatorId());
            record.setOperateName(starTaskWebLoginModel.getContactName());
        }
        record.setRemark(StringPool.EMPTY);
        return record;
    }

    /**
     * 创建默认的导出任务记录
     *
     * @param taskName              任务名称
     * @param businessTypeEnum      业务类型
     * @param parameter             参数
     * @param starTaskWebLoginModel 登录态获取参数，可空
     * @return StarTaskOcExportTaskDO
     */
    @Synchronized
    private StarTaskOcExportTaskDO createAgentDefaultRecord(@Nonnull String taskName, OperateExportBusinessTypeEnum businessTypeEnum, Object parameter, StarTaskAgentWebLoginModel starTaskWebLoginModel) {
        val record = new StarTaskOcExportTaskDO();
        record.setTaskId(BizNoBuildUtil.build());
        record.setTaskName(taskName);
        val param = ObjectUtil.defaultIfNull(parameter, CommonConstant.EMPTY_OBJ);
        record.setExportParameter(JSONObject.toJSONString(param));
        record.setBusinessType(businessTypeEnum.toString());
        record.setTaskStatus(OperateExportTaskStatusEnum.CREATED.getTaskStatus());
        record.setPlatformType(OperateExportPlatformTypeEnum.AGENT_BACKGROUND.getPlatform());
        if (Objects.nonNull(starTaskWebLoginModel)) {
            record.setUserId(starTaskWebLoginModel.getAgentId());
            record.setOperateName(starTaskWebLoginModel.getContactName());
        }
        record.setRemark(StringPool.EMPTY);
        return record;
    }

    /**
     * 上传文件到阿里云OSS
     *
     * @param file          文件
     * @param subObjectPath 子对象路径
     * @param fileName      最终的文件名称
     * @return 上传地址
     */
    private Observable<String> uploadFileToOssStorage(File file, String subObjectPath, String fileName) {
        return Observable.create(emitter -> {
            // 创建OSS上传对象
            try (val uploader = new AliyunOssClient(ossConfig())) {
                // 目标文件
                val targetFileName = fileName + StringPool.DOT + Files.getFileExtension(file.toString());
                val today = DateUtil.today();
                // 生成ObjectKey
                val objectKey = Joiner.on(StringPool.SLASH).join(sysConfig.getStarTaskExportObjectPath(), today, subObjectPath, targetFileName);
                val uploadUrl = uploader.upload(sysConfig.getBucketName(), objectKey, Files.toByteArray(file));
                if (StringUtils.isNotBlank(uploadUrl)) {
                    emitter.onNext(uploadUrl);
                } else {
                    emitter.onError(new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("上传失败"));
                }
                emitter.onComplete();
            } catch (Exception ex) {
                emitter.onError(new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage(ex.getMessage()));
            }
        });
    }

    /**
     * 获得上传的阿里云配置
     *
     * @return 上传的阿里云配置
     */
    private AliyunOssConfig ossConfig() {
        boolean isDev = StringUtils.equalsIgnoreCase(sysConfig.getEnv(), EnvEnum.DEV.getValue());
        // 非Dev环境使用内部域名上传
        AliyunOssConfig config = new AliyunOssConfig(!isDev);
        // endpoint 必须以https://开头
        config.ossEndpoint = CommonConstant.HTTPS_PREFIX + sysConfig.getEndpoint();
        config.ossAccessKeyId = sysConfig.getAliyunOssAk();
        config.ossAccessKeySecret = sysConfig.getAliyunOssSk();
        return config;
    }
}
