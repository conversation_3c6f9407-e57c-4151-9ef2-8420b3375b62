/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.mina.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.constant.startask.mina.StarTaskMinaRedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.oem.BooleanEnum;
import com.huike.nova.common.enums.startask.IdentityWithdrawStatusEnum;
import com.huike.nova.common.enums.startask.WithdrawTypeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskPresetOperatorEnum;
import com.huike.nova.common.enums.startask.mina.WithdrawApplyStatusEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogChangeTypeEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogRemarkTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.common.util.ExceptionUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.param.startask.BalanceDetailsPageListParamDTO;
import com.huike.nova.dao.entity.StarTaskAreaLimitDO;
import com.huike.nova.dao.entity.StarTaskBalanceAccountDO;
import com.huike.nova.dao.entity.StarTaskBalanceLogDO;
import com.huike.nova.dao.entity.StarTaskIdentityDO;
import com.huike.nova.dao.entity.StarTaskUserDO;
import com.huike.nova.dao.entity.StarTaskWithdrawApplyDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.AilikeGaodeCodeDAO;
import com.huike.nova.dao.repository.StarTaskApplyListDAO;
import com.huike.nova.dao.repository.StarTaskAreaLimitDAO;
import com.huike.nova.dao.repository.StarTaskBalanceAccountDAO;
import com.huike.nova.dao.repository.StarTaskBalanceLogDAO;
import com.huike.nova.dao.repository.StarTaskIdentityDAO;
import com.huike.nova.dao.repository.StarTaskUserDAO;
import com.huike.nova.dao.repository.StarTaskWithdrawApplyDAO;
import com.huike.nova.service.business.CommonService;
import com.huike.nova.service.business.startask.mina.StarTaskCommonMinaService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.dto.startask.GetNoticeContentDTO;
import com.huike.nova.service.domain.mapper.startask.mina.StarTaskCommonMinaServiceObjMapper;
import com.huike.nova.service.domain.model.login.FindProtocolDetailModel;
import com.huike.nova.service.domain.model.login.FindProtocolListModel;
import com.huike.nova.service.domain.model.startask.mina.common.CheckAuthModel;
import com.huike.nova.service.domain.model.startask.mina.common.FindTransactionDetailModel;
import com.huike.nova.service.domain.model.startask.mina.common.GetAccountBalanceModel;
import com.huike.nova.service.domain.model.startask.mina.common.GetAuthConfigModel;
import com.huike.nova.service.domain.model.startask.mina.common.GetNoticeContentModel;
import com.huike.nova.service.domain.model.startask.mina.common.GetUserShowAttrModel;
import com.huike.nova.service.domain.model.startask.mina.common.GetWithdrawPermissionModel;
import com.huike.nova.service.domain.model.startask.mina.common.GetWithdrawalInfoModel;
import com.huike.nova.service.domain.model.startask.mina.login.StarTaskMinaLoginModel;
import com.huike.nova.service.domain.param.startask.mina.common.ApplyWithdrawalParam;
import com.huike.nova.service.domain.param.startask.mina.common.AuthenticateParam;
import com.huike.nova.service.domain.param.startask.mina.common.CheckAuthParam;
import com.huike.nova.service.domain.param.startask.mina.common.FindTransactionDetailParam;
import com.huike.nova.service.domain.param.startask.mina.common.GetAccountBalanceParam;
import com.huike.nova.service.domain.param.startask.mina.common.GetNoticeContentParam;
import com.huike.nova.service.domain.param.startask.mina.common.GetWithdrawalInfoParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version StarTaskCommonMinaServiceImpl.java, v 0.1 2023-12-04 4:30 PM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class StarTaskCommonMinaServiceImpl implements StarTaskCommonMinaService {

    private StarTaskBalanceAccountDAO starTaskBalanceAccountDAO;

    private StarTaskWithdrawApplyDAO starTaskWithdrawApplyDAO;

    private StarTaskIdentityDAO starTaskIdentityDAO;

    private StarTaskBalanceLogDAO starTaskBalanceLogDAO;

    private StarTaskApplyListDAO starTaskApplyListDAO;

    private StarTaskUserDAO starTaskUserDAO;

    private StarTaskCommonMinaServiceObjMapper starTaskCommonMinaServiceObjMapper;

    private TransactionTemplate transactionTemplate;

    private RedissonClient redissonClient;

    private CommonService commonService;

    private StarTaskAreaLimitDAO starTaskAreaLimitDAO;

    private AilikeGaodeCodeDAO ailikeGaodeCodeDAO;

    private SysConfig sysConfig;

    /**
     * 余额
     *
     * @param param
     * @return
     */
    @Override
    public GetAccountBalanceModel getAccountBalance(GetAccountBalanceParam param) {
        // 登录态获取数据
        StarTaskMinaLoginModel basicInfo = LoginUtil.getStarTaskLoginBasicInfo(param.getIdentityType());
        String identityId = basicInfo.getIdentityId();
        // 查看账户信息
        StarTaskBalanceAccountDO balanceAccount = starTaskBalanceAccountDAO.getBalanceAccount(basicInfo.getUserId(), identityId);
        if (Objects.isNull(balanceAccount)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账户信息不存在");
        }
        GetAccountBalanceModel model = new GetAccountBalanceModel();
        model.setAvailableBalance(balanceAccount.getAvailableBalance());
        model.setIncomeTotalIncome(balanceAccount.getIncomeTotalIncome());
        // 查询预期任务金收益
        if (StarTaskIdentityTypeEnum.STAR.getValue().equals(param.getIdentityType())) {
            // 根据身份id查询任务金额
            BigDecimal money = starTaskApplyListDAO.sumTaskMoneyByIdentityId(identityId);
            model.setProspectiveIncome(money);
        }
        model.setTotalRefund(balanceAccount.getTotalRefund());
        model.setTotalReward(balanceAccount.getTotalReward());
        return model;
    }

    /**
     * 交易明细
     *
     * @param param
     * @return
     */
    @Override
    public PageResult<FindTransactionDetailModel> findTransactionDetail(PageParam<FindTransactionDetailParam> param) {
        FindTransactionDetailParam query = param.getQuery();
        Integer type = query.getType();
        // 查询登录信息
        StarTaskMinaLoginModel basicInfo = LoginUtil.getStarTaskLoginBasicInfo(query.getIdentityType());
        PageParam<BalanceDetailsPageListParamDTO> pageParam = new PageParam<>();
        pageParam.setPage(param.getPage());
        pageParam.setPageSize(param.getPageSize());
        BalanceDetailsPageListParamDTO paramDTO = new BalanceDetailsPageListParamDTO();
        paramDTO.setIdentityId(basicInfo.getIdentityId());
        paramDTO.setRemarkType(type);
        pageParam.setQuery(paramDTO);
        Page<StarTaskBalanceLogDO> resultPage = starTaskBalanceLogDAO.balanceDetailsPageList(pageParam);
        List<StarTaskBalanceLogDO> records = resultPage.getRecords();
        List<FindTransactionDetailModel> list = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(records)) {
            Map<String, StarTaskWithdrawApplyDO> applyDOMap = Maps.newHashMap();
            // 提现需要特殊处理
            if (CommonConstant.INTEGER_ONE.equals(type)) {
                List<String> numberList = records.stream().map(item -> item.getRelationNumber()).collect(Collectors.toList());
                List<StarTaskWithdrawApplyDO> applyCodeList = starTaskWithdrawApplyDAO.findListByApplyCodeList(numberList);
                applyDOMap = applyCodeList.stream().collect(Collectors.toMap(StarTaskWithdrawApplyDO::getApplyCode, Function.identity(), (v1, v2) -> v2));
            }
            for (StarTaskBalanceLogDO record : records) {
                FindTransactionDetailModel detailModel = starTaskCommonMinaServiceObjMapper.toFindTransactionDetailModel(record);
                if (CommonConstant.INTEGER_ONE.equals(type)) {
                    StarTaskWithdrawApplyDO applyDO = applyDOMap.getOrDefault(record.getRelationNumber(), new StarTaskWithdrawApplyDO());
                    detailModel.setAlipayAccount(applyDO.getAlipayAccount());
                    detailModel.setRealName(applyDO.getRealName());
                    detailModel.setApplyStatus(applyDO.getApplyStatus());
                }
                list.add(detailModel);
            }
        }
        PageResult<FindTransactionDetailModel> pageResult = new PageResult<>();
        pageResult.setSize(resultPage.getSize());
        pageResult.setTotal(resultPage.getTotal());
        pageResult.setCurrent(resultPage.getCurrent());
        pageResult.setRecords(list);
        return pageResult;
    }

    /**
     * 申请提现
     *
     * @param param
     */
    @Override
    public void applyWithdrawal(ApplyWithdrawalParam param) {
        BigDecimal withdrawAmount = param.getWithdrawAmount();
        Integer identityType = param.getIdentityType();
        Integer withdrawType = param.getWithdrawType();
        // 登录态获取信息
        StarTaskMinaLoginModel basicInfo = LoginUtil.getStarTaskLoginBasicInfo(identityType);
        String userId = basicInfo.getUserId();
        String identityId = basicInfo.getIdentityId();
        String lock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_KEY_APPLY_WITHDRAWAL, identityId);
        try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
            redisLockHelper.tryLock();
            // 获取身份信息
            StarTaskIdentityDO starTaskIdentityDO = starTaskIdentityDAO.getIdentityByIdentityId(identityId);
            if (Objects.isNull(starTaskIdentityDO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息不存在");
            }
            // 新增提现申请单
            String applyCode = commonService.buildIncr();
            String alipayAccount = param.getAlipayAccount();
            String realName = param.getRealName();
            StarTaskWithdrawApplyDO withdrawApplyDO = new StarTaskWithdrawApplyDO();
            withdrawApplyDO.setApplyCode(applyCode);
            withdrawApplyDO.setUserId(userId);
            withdrawApplyDO.setIdentityId(identityId);
            withdrawApplyDO.setWithdrawAmount(withdrawAmount);
            withdrawApplyDO.setAlipayAccount(alipayAccount);
            withdrawApplyDO.setRealName(realName);
            withdrawApplyDO.setApplyStatus(WithdrawApplyStatusEnum.WAITING_FOR_PAYMENT.getValue());
            withdrawApplyDO.setWithdrawType(withdrawType);
            withdrawApplyDO.setIdCardNo(starTaskIdentityDO.getIdCardNo());
            // 新增打款纪录表
            StarTaskBalanceLogDO balanceLogDO = new StarTaskBalanceLogDO();
            // 判断类型是否是奖励金类型（老版本默认为任务金提现）
            boolean flag = WithdrawTypeEnum.REWARD.getValue().equals(withdrawType);
            String changeRemark = StringPool.EMPTY;
            if (StarTaskIdentityTypeEnum.MERCHANT.getValue().equals(identityType)) {
                // 商家提现权限判断
                if (flag && IdentityWithdrawStatusEnum.NOT_WITHDRAW_ABLE.getValue().equals(starTaskIdentityDO.getHasWithdraw())) {
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("商家奖励金提现权限未开启");
                }
                balanceLogDO.setOperatorId(StarTaskPresetOperatorEnum.MERCHANT.getOperatorId());
                balanceLogDO.setOperatorName(StarTaskPresetOperatorEnum.MERCHANT.getName());
                changeRemark = "退款提现";
            } else if (StarTaskIdentityTypeEnum.STAR.getValue().equals(identityType)) {
                balanceLogDO.setOperatorId(StarTaskPresetOperatorEnum.STAR.getOperatorId());
                balanceLogDO.setOperatorName(StarTaskPresetOperatorEnum.STAR.getName());
                changeRemark = "任务收益提现";
            }
            balanceLogDO.setUserId(userId);
            balanceLogDO.setIdentityId(identityId);
            balanceLogDO.setChangeType(BalanceLogChangeTypeEnum.REDUCED.getValue());
            balanceLogDO.setRemarkType(flag ? BalanceLogRemarkTypeEnum.REWARD_WITHDRAW.getValue() : BalanceLogRemarkTypeEnum.WITHDRAW.getValue());
            balanceLogDO.setChangeRemark(flag ? BalanceLogRemarkTypeEnum.REWARD_WITHDRAW.getName() : changeRemark);
            balanceLogDO.setChangeAmount(withdrawAmount);
            balanceLogDO.setRelationNumber(applyCode);
            transactionTemplate.execute(status -> {
                // 校验提现金额是否支持提现
                StarTaskBalanceAccountDO balanceAccount = this.checkWithdrawAmount(withdrawAmount, identityId, flag);
                // 构建日志剩余信息
                balanceLogDO.setChangeAvailableBalance(balanceAccount.getAvailableBalance());
                balanceLogDO.setAfterAvailableBalance(balanceAccount.getAvailableBalance().subtract(withdrawAmount));
                balanceLogDO.setChangeExpenditureTotalIncome(balanceAccount.getExpenditureTotalIncome());
                balanceLogDO.setAfterExpenditureTotalIncome(balanceAccount.getExpenditureTotalIncome());
                balanceLogDO.setChangeIncomeTotalIncome(balanceAccount.getIncomeTotalIncome());
                balanceLogDO.setAfterIncomeTotalIncome(balanceAccount.getIncomeTotalIncome());
                balanceLogDO.setChangeWithdrawalFrozenBalance(balanceAccount.getWithdrawalFrozenBalance());
                balanceLogDO.setAfterWithdrawalFrozenBalance(balanceAccount.getWithdrawalFrozenBalance().add(withdrawAmount));
                // 数据库修改
                starTaskWithdrawApplyDAO.saveWithdrawApply(withdrawApplyDO);
                starTaskBalanceAccountDAO.updateWithdrawAmount(identityId, withdrawAmount, flag ? withdrawAmount : BigDecimal.ZERO);
                starTaskBalanceLogDAO.saveBalanceLog(balanceLogDO);
                return true;
            });
        } catch (Exception e) {
            LogUtil.warn(log, "StarTaskCommonServiceImpl.updateStatusToCanceled >> 更新佣金计划异常, 错误原因: ", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }

    /**
     * 校验提现金额是否支持提现
     *
     * @param withdrawAmount
     * @param identityId
     * @param flag
     * @return
     */
    private StarTaskBalanceAccountDO checkWithdrawAmount(BigDecimal withdrawAmount, String identityId, boolean flag) {
        StarTaskBalanceAccountDO balanceAccount = starTaskBalanceAccountDAO.getBalanceAccountForUpdate(identityId);
        if (Objects.isNull(balanceAccount)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账户信息不存在");
        }
        BigDecimal availableBalance = balanceAccount.getAvailableBalance();
        BigDecimal rewardBalance = balanceAccount.getRewardBalance();
        if (withdrawAmount.compareTo(availableBalance) > 0) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("提现金额不足");
        }
        // 防止奖励金余额少于可提现余额
        if (!flag && withdrawAmount.compareTo(availableBalance.subtract(rewardBalance)) > 0) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("小程序版本较低，请先升级版本");
        }
        // 奖励金提现类型/提现金额不足
        if (flag && withdrawAmount.compareTo(rewardBalance) > 0) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("提现金额不足");
        }
        return balanceAccount;
    }

    /**
     * 获取提现信息
     *
     * @param param
     * @return
     */
    @Override
    public GetWithdrawalInfoModel getWithdrawalInfo(GetWithdrawalInfoParam param) {
        // 登录态获取信息
        StarTaskMinaLoginModel basicInfo = LoginUtil.getStarTaskLoginBasicInfo(param.getIdentityType());
        // 获取提现信息
        StarTaskIdentityDO starTaskIdentityDO = starTaskIdentityDAO.getIdentityByIdentityId(basicInfo.getIdentityId());
        if (Objects.isNull(starTaskIdentityDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息不存在");
        }
        // 查看账户信息
        StarTaskBalanceAccountDO balanceAccount = starTaskBalanceAccountDAO.getBalanceAccount(basicInfo.getUserId(), basicInfo.getIdentityId());
        if (Objects.isNull(balanceAccount)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账户信息不存在");
        }
        BigDecimal rewardBalance = balanceAccount.getRewardBalance();
        // 构建返回参数
        GetWithdrawalInfoModel model = new GetWithdrawalInfoModel();
        model.setAlipayAccount(starTaskIdentityDO.getAlipayAccount());
        model.setRealName(starTaskIdentityDO.getRealName());
        model.setHasWithdraw(starTaskIdentityDO.getHasWithdraw());
        // 任务收益余额 = 可提现余额 - 奖励金余额
        model.setTaskIncomeBalance(balanceAccount.getAvailableBalance().subtract(rewardBalance));
        model.setRewardBalance(rewardBalance);
        return model;
    }

    /**
     * 根据任务id获取达人区域限制名称列表
     *
     * @param taskId 任务id
     * @return 名称列表
     */
    @Override
    public List<String> findStarAreaNameList(String taskId) {
        List<StarTaskAreaLimitDO> list = starTaskAreaLimitDAO.findStarAreaList(taskId);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> areaList = new ArrayList<>();
        for (StarTaskAreaLimitDO item : list) {
            if (CommonConstant.NEGATIVE_ONE.equals(item.getStarCity())) {
                areaList.add(item.getStarProvince());
            } else {
                areaList.add(item.getStarCity());
            }
        }
        return ailikeGaodeCodeDAO.findAdNameByAdCodeList(areaList);
    }

    /**
     * 获取提现权限
     *
     * @return
     */
    @Override
    public GetWithdrawPermissionModel getWithdrawPermission() {
        GetWithdrawPermissionModel model = new GetWithdrawPermissionModel();
        model.setFlag(sysConfig.getStarTaskMinaWithdrawPermission());
        return model;
    }

    /**
     * 获取通知内容
     *
     * @param param 入参
     * @return 通知内容
     */
    @Override
    public GetNoticeContentModel getNoticeContent(GetNoticeContentParam param) {
        GetNoticeContentModel model = new GetNoticeContentModel();
        Integer identityType = param.getIdentityType();
        String starTaskMinaNoticeContent = sysConfig.getStarTaskMinaNoticeContent();
        GetNoticeContentDTO contentDTO = JSONArray.parseObject(starTaskMinaNoticeContent, GetNoticeContentDTO.class);
        if (StarTaskIdentityTypeEnum.MERCHANT.getValue().equals(identityType)) {
            model.setTitle(contentDTO.getMerchantTitle());
            model.setPopUpImageUrl(contentDTO.getMerchantPopUpImageUrl());
            model.setPopUpText(contentDTO.getMerchantPopUpText());
        } else {
            model.setTitle(contentDTO.getStarTitle());
            model.setPopUpImageUrl(contentDTO.getStarPopUpImageUrl());
            model.setPopUpText(contentDTO.getStarPopUpText());
        }
        return model;
    }

    /**
     * 判断是否身份认证
     *
     * @param param
     * @return
     */
    @Override
    public CheckAuthModel checkAuth(CheckAuthParam param) {
        // 登录态获取登录信息
        StarTaskMinaLoginModel basicInfo = LoginUtil.getStarTaskLoginBasicInfo(param.getIdentityType());
        // 根据身份id获取是否身份认证信息
        StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(basicInfo.getIdentityId());
        if (Objects.isNull(identityDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息不存在");
        }
        // 构建返回值
        CheckAuthModel model = new CheckAuthModel();
        model.setHasAuth(identityDO.getHasAuth());
        return model;
    }

    /**
     * 身份认证
     *
     * @param param
     */
    @Override
    public void authenticate(AuthenticateParam param) {
        // 登录态获取登录信息
        StarTaskMinaLoginModel basicInfo = LoginUtil.getStarTaskLoginBasicInfo(param.getIdentityType());
        String identityId = basicInfo.getIdentityId();
        // 防止重复认证
        String lock = StrUtil.format(StarTaskMinaRedisPrefixConstant.STAR_TASK_MINA_CACHE_AUTHENTICATE, identityId);
        try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
            redisLockHelper.tryLock();
            // 判断是否已经身份认证
            StarTaskIdentityDO identityDO = starTaskIdentityDAO.getIdentityByIdentityId(identityId);
            if (Objects.isNull(identityDO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("身份信息不存在");
            }
            if (BooleanEnum.YES.getValue().equals(identityDO.getHasAuth())) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("请勿重复认证");
            }
            // 更新身份信息
            starTaskIdentityDAO.updateAlipay(identityId, param.getAlipayAccount(), param.getRealName(), FieldEncryptUtil.encode(param.getIdCardNo()), BooleanEnum.YES.getValue());
        } catch (Exception e) {
            LogUtil.warn(log, "StarTaskCommonMinaServiceImpl.authenticate >> 身份认证, 错误原因: ", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }

    /**
     * 获取认证协议
     *
     * @return
     */
    @Override
    public FindProtocolListModel getAuthenticationAgreement() {
        // 获取配置
        String starTaskMinaProtocolList = sysConfig.getStarTaskMinaAuthenticationAgreement();
        FindProtocolListModel model = new FindProtocolListModel();
        model.setProtocolList(JSONObject.parseArray(starTaskMinaProtocolList, FindProtocolDetailModel.class));
        return model;
    }

    /**
     * 获取用户展示信息
     *
     * @param identityType
     * @return
     */
    @Override
    public GetUserShowAttrModel getUserShowAttr(Integer identityType) {
        StarTaskMinaLoginModel basicInfo = LoginUtil.getStarTaskLoginBasicInfo(identityType);
        StarTaskUserDO info = starTaskUserDAO.getUserInfoByUserId(basicInfo.getUserId());
        if (Objects.isNull(info)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("用户信息异常");
        }
        GetUserShowAttrModel model = new GetUserShowAttrModel();
        model.setIsShow(info.getIsShow());
        return model;
    }

    /**
     * 更新用户展示信息
     *
     * @param identityType
     */
    @Override
    public void updateUserShowAttr(Integer identityType) {
        StarTaskMinaLoginModel basicInfo = LoginUtil.getStarTaskLoginBasicInfo(identityType);
        StarTaskUserDO info = starTaskUserDAO.getUserInfoByUserId(basicInfo.getUserId());
        if (Objects.isNull(info)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("用户信息异常");
        }
        info.setIsShow(BooleanEnum.NO.getValue());
        starTaskUserDAO.updateById(info);
    }

    /**
     * 获取身份认证配置
     *
     * @return
     */
    @Override
    public GetAuthConfigModel getAuthConfig() {
        String config = sysConfig.getStarTaskMinaAuthConfig();
        return JSONObject.parseObject(config, GetAuthConfigModel.class);
    }
}