package com.huike.nova.service.business.startask.web.impl;

import cn.hutool.core.util.StrUtil;
import com.huike.nova.common.constant.AsyncThreadConstant;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.TraceIdGenerator;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.repository.StarTaskOcExportTaskDAO;
import com.huike.nova.service.business.startask.web.StarTaskWebExportDaemonService;
import com.huike.nova.service.business.startask.web.StarTaskWebExportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 导出守护服务
 *
 * <AUTHOR> (<EMAIL>)
 * @version StarTaskWebExportDaemonServiceImpl.java, v1.0 12/17/2023 13:27 John Exp$
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class StarTaskWebExportDaemonServiceImpl implements StarTaskWebExportDaemonService, DisposableBean {

    /**
     * 防止重复执行的标识
     */
    private static final AtomicBoolean runOnceFlag = new AtomicBoolean(false);

    private final SysConfig sysConfig;

    private final RedissonClient redissonClient;

    private final StarTaskWebExportService starTaskWebExportService;

    private final StarTaskOcExportTaskDAO starTaskOcExportTaskDAO;

    @Async(AsyncThreadConstant.STAR_TASK_EXPORT_EXECUTOR)
    @Override
    public void startDaemon() {
        if (runOnceFlag.getAndSet(true)) {
            return;
        }
        isDone = false;
        val redisQueueKey = StrUtil.format(RedisPrefixConstant.BLOCKING_QUEUE_STAR_TASK_EXPORT_CENTER_EXECUTING, sysConfig.getEnv());
        val taskQueue = redissonClient.<String>getBlockingQueue(redisQueueKey, RedisPrefixConstant.DEFAULT_REDIS_STRING_CODEC);
        val timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                starTaskWebExportService.notifyExecuteTasks();
            }
        }, 60 * 1000, 60 * 1000);
        while (!isDone) {
            // 生成一个新的TraceId
            generateNewTraceId();
            try {
                // 每等待10秒
                val content = taskQueue.poll(10, TimeUnit.SECONDS);
                // 如果返回内容不是空
                if (!StringUtils.equals(content, CommonConstant.ONE_STR)) {
                    continue;
                }
                // 查询未导出的订单
                starTaskOcExportTaskDAO.queryUnExportedExportTasks(resultContext ->
                        starTaskWebExportService.executeExportTask(resultContext.getResultObject())
                );
            } catch (Exception ex) {
                LogUtil.error(log, "StarTaskExportCenterRunner.run >> 导出中心服务 >> 任务队列异常", ex);
            }
        }
    }

    /**
     * 终止标识
     */
    private boolean isDone = false;

    @Override
    public void destroy() {
        isDone = true;
    }


    /**
     * 生成一个新的TraceId，以便日志追踪
     * @return 新的TraceId
     */
    private String generateNewTraceId() {
        val traceId = TraceIdGenerator.generate();
        MDC.put(CommonConstant.TRACE_ID, traceId);
        return traceId;
    }
}
