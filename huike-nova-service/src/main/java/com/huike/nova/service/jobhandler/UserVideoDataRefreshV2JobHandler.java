/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.common.util.FsDateUtils;
import com.huike.nova.service.business.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version UserVideoDataRefreshV2JobHandler.java, v 0.1 2023-02-10 11:24 AM ruanzy
 */
@Component
@Slf4j
@JobHandler("userVideoDataRefreshV2JobHandler")
@AllArgsConstructor
public class UserVideoDataRefreshV2JobHandler extends IJobHandler {

    private TaskService taskService;

    /**
     * 获取8到30天内的数据
     *
     * @param s 从xxl-job-admin传过来的参数
     * @return ReturnT.SUCCESS/SUCCESS_MSG/SUCCESS_CODE or ReturnT.FAIL/FAIL_MSG/FAIL_CODE
     * @throws Exception 执行异常
     */
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("UserVideoDataRefreshV2JobHandler.execute >> 用户抖音视频数据刷新脚本脚本执行开始：time = {}", DateUtil.now());
        String startTime = DateUtil.format(FsDateUtils.getAfterDay(-30), FsDateUtils.SIMPLE_DATE_FORMAT);
        String endTime = DateUtil.format(FsDateUtils.getAfterDay(-7), FsDateUtils.SIMPLE_DATE_FORMAT);
        taskService.userVideoDataRefreshJob(startTime, endTime);
        XxlJobLogger.log("UserVideoDataRefreshV2JobHandler.execute >> 用户抖音视频数据刷新脚本脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}