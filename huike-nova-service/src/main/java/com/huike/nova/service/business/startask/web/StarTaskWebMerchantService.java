package com.huike.nova.service.business.startask.web;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.dao.domain.param.startask.WebMerchantListParamDTO;
import com.huike.nova.service.domain.model.startask.web.merchant.DefaultDistributionRateModel;
import com.huike.nova.service.domain.model.startask.web.merchant.PageFindInviterListModel;
import com.huike.nova.service.domain.model.startask.web.merchant.QueryDefaultTaskMoneyModel;
import com.huike.nova.service.domain.model.startask.web.merchant.WebMerchantListModel;
import com.huike.nova.service.domain.param.startask.web.merchant.ModifyWithdrawalPermissionParam;
import com.huike.nova.service.domain.param.startask.web.merchant.PageFindInviterListParam;
import com.huike.nova.service.domain.param.startask.web.merchant.UpdateDistributionRateParam;
import com.huike.nova.service.domain.param.startask.web.merchant.UpdateInviterParam;
import com.huike.nova.service.domain.param.startask.web.merchant.UpdateTaskMoneyParam;
import com.huike.nova.service.domain.param.startask.web.merchant.WebMerchantListParam;
import com.huike.nova.service.domain.param.startask.web.merchant.WebMerchantOperateParam;
import com.huike.nova.service.domain.param.startask.web.merchant.WebMerchantRechargeParam;
import io.reactivex.Observable;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2023年12月06日 17:05
 */
public interface StarTaskWebMerchantService {

    /**
     * 商家列表
     *
     * @param param 入参
     * @return 出参
     */
    PageResult<WebMerchantListModel> list(PageParam<WebMerchantListParam> param);

    /**
     * 商家操作
     *
     * @param param 入参
     */
    void operate(WebMerchantOperateParam param);

    /**
     * 商家充值
     *
     * @param param 入参
     */
    void recharge(WebMerchantRechargeParam param);

    /**
     * 商家列表导出
     *
     * @param param 入参
     */
    void requestExport(WebMerchantListParam param);

    /**
     * 商家列表导出（异步任务）
     *
     * @param param 入参
     */
    Observable<File> export(String fileName, WebMerchantListParamDTO param);

    /**
     * 更新任务金额
     *
     * @param param 入参
     */
    void updateTaskMoney(UpdateTaskMoneyParam param);

    /**
     * 查询默认任务金额
     *
     * @return 出参
     */
    QueryDefaultTaskMoneyModel queryDefaultTaskMoney();

    /**
     * 邀请人分页列表
     *
     * @param param 入参
     * @return 出参
     */
    PageResult<PageFindInviterListModel> pageFindInviterList(PageParam<PageFindInviterListParam> param);

    /**
     * 修改邀请人
     *
     * @param param 入参
     */
    void updateInviter(UpdateInviterParam param);

    /**
     * 查询商家和达人的默认分销比例
     */
    DefaultDistributionRateModel findDefaultDistributionRate();

    /**
     * 更新分销比例
     *
     * @param param 入参
     */
    void updateDistributionRate(UpdateDistributionRateParam param);

    /**
     * 提现权限修改
     *
     * @param param
     */
    void modifyWithdrawalPermission(ModifyWithdrawalPermissionParam param);
}
