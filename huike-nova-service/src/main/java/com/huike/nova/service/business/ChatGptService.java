/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.chatgpt.ApplyActivityModel;
import com.huike.nova.service.domain.model.chatgpt.FindCollectListModel;
import com.huike.nova.service.domain.model.chatgpt.FindTemplateListModel;
import com.huike.nova.service.domain.model.chatgpt.GetCollectDetailsModel;
import com.huike.nova.service.domain.model.chatgpt.GetStoreInfoModel;
import com.huike.nova.service.domain.model.chatgpt.OpenApiSendModel;
import com.huike.nova.service.domain.model.chatgpt.OperateChatModel;
import com.huike.nova.service.domain.param.chatgpt.ApplyActivityParam;
import com.huike.nova.service.domain.param.chatgpt.CollectAddParam;
import com.huike.nova.service.domain.param.chatgpt.DeleteCollectParam;
import com.huike.nova.service.domain.param.chatgpt.FindTemplateListParam;
import com.huike.nova.service.domain.param.chatgpt.GetCollectDetailsParam;
import com.huike.nova.service.domain.param.chatgpt.GetStoreInfoParam;
import com.huike.nova.service.domain.param.chatgpt.OpenApiSendParam;
import com.huike.nova.service.domain.param.chatgpt.OperateChatParam;

/**
 * <AUTHOR>
 * @version ChatGptService.java, v 0.1 2023-04-12 5:28 PM ruanzy
 */
public interface ChatGptService {

    /**
     * 提问接口
     *
     * @param param
     * @return
     */
    OpenApiSendModel sendOpenApi(OpenApiSendParam param);

    /**
     * 获取模板内容列表
     *
     * @param param
     * @return
     */
    FindTemplateListModel findTemplateList(FindTemplateListParam param);

    /**
     * 收藏新增
     *
     * @param param
     */
    void addCollect(CollectAddParam param);

    /**
     * 收藏列表
     *
     * @param param
     * @return
     */
    PageResult<FindCollectListModel> findCollectList(PageParam param);

    /**
     * 收藏删除
     *
     * @param param
     */
    void deleteCollect(DeleteCollectParam param);

    /**
     * 收藏详情
     *
     * @param param
     * @return
     */
    GetCollectDetailsModel getCollectDetails(GetCollectDetailsParam param);

    /**
     * 应用到剪辑任务
     *
     * @param param
     * @return
     */
    ApplyActivityModel applyActivity(ApplyActivityParam param);

    /**
     * 行业和所在区域查询
     *
     * @param param
     * @return
     */
    GetStoreInfoModel getStoreInfo(GetStoreInfoParam param);

    /**
     * 更新在线状态信息
     *
     * @param param
     * @return
     */
    OperateChatModel updateOnlineStatus(OperateChatParam param);
}