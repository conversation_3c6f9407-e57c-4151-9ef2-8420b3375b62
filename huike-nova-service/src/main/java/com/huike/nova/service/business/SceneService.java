/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.service.domain.model.scene.SceneListModel;
import com.huike.nova.service.domain.param.scene.BatchDeleteVideoParam;
import com.huike.nova.service.domain.param.scene.SceneAddParam;
import com.huike.nova.service.domain.param.scene.SceneDeleteParam;
import com.huike.nova.service.domain.param.scene.SceneDescriptionEditParam;
import com.huike.nova.service.domain.param.scene.SceneListParam;
import com.huike.nova.service.domain.param.scene.SceneMaterialUpdateParam;
import com.huike.nova.service.domain.param.scene.SceneTimeAddParam;
import com.huike.nova.service.domain.param.scene.SoundAddParam;
import com.huike.nova.service.domain.param.scene.UpdatePromptCreativeModeParam;
import com.huike.nova.service.domain.param.scene.UpdateSceneModeParam;
import com.huike.nova.service.domain.param.scene.VideoSliceParam;

/**
 * <AUTHOR>
 * @version SceneService.java, v 0.1 2023-03-03 10:37 AM ruanzy
 */
public interface SceneService {

    /**
     * 场景查询列表
     *
     * @param param
     * @return
     */
    SceneListModel findSceneList(SceneListParam param);

    /**
     * 添加场景
     *
     * @param param
     */
    void addScene(SceneAddParam param);

    /**
     * 场景描述
     *
     * @param param
     */
    void editSceneDescription(SceneDescriptionEditParam param);

    /**
     * 删除场景
     *
     * @param param
     */
    void deleteScene(SceneDeleteParam param);

    /**
     * 添加字幕-通用（场景口播、全程口播）
     *
     * @param param
     */
    void addSound(SoundAddParam param);

    /**
     * 场景内容-添加场景时长
     *
     * @param param
     */
    void addSceneTime(SceneTimeAddParam param);

    /**
     * 更新场景视频信息
     *
     * @param param
     */
    void updateSceneMaterial(SceneMaterialUpdateParam param);

    /**
     * 场景视频切片
     *
     * @param param
     */
    void videoSlice(VideoSliceParam param);

    /**
     * 批量删除视频信息
     *
     * @param param
     */
    void batchDeleteVideo(BatchDeleteVideoParam param);

    /**
     * 更新场景模式
     *
     * @param param 场景模式参数
     */
    void updateSceneMode(UpdateSceneModeParam param);

    /**
     * 更新提示创作模式
     *
     * @param param
     */
    void updatePromptCreativeMode(UpdatePromptCreativeModeParam param);
}