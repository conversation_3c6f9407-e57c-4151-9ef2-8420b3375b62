package com.huike.nova.service.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.IceTaskInfoStatusEnum;
import com.huike.nova.common.enums.InterfaceTypeEnum;
import com.huike.nova.common.enums.MerchantStarBindStatusEnum;
import com.huike.nova.common.enums.RpaVideoRecordReleasedEnum;
import com.huike.nova.common.enums.StarMatrixHandleTypeEnum;
import com.huike.nova.common.enums.SupActivityPublishStatusEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.dao.entity.AilikeBSupActivityDO;
import com.huike.nova.dao.entity.MerchantStoreStarDO;
import com.huike.nova.dao.entity.RpaVideoRecordDO;
import com.huike.nova.dao.entity.StarPushRecordDO;
import com.huike.nova.dao.repository.AilikeBIceSynthesisRecordInfoDAO;
import com.huike.nova.dao.repository.AilikeBSupActivityDAO;
import com.huike.nova.dao.repository.MerchantStoreStarDAO;
import com.huike.nova.dao.repository.RpaVideoRecordDAO;
import com.huike.nova.dao.repository.StarPushRecordDAO;
import com.huike.nova.service.business.IceService;
import com.huike.nova.service.business.StarSupActivityService;
import com.huike.nova.service.business.common.DingDingCommonService;
import com.huike.nova.service.domain.param.starsupactivity.AddMaterialAutoPublishParam;
import com.huike.nova.service.domain.param.starsupactivity.SupActivityRetryParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 达人推活动重试
 * 补充素材/失败重试
 *
 * <AUTHOR>
 * @version StarPushSupplyMaterialListener.java, v 0.1 2023/2/10 4:31 下午 mayucong
 */
@Slf4j(topic = "mq")
@Component
@AllArgsConstructor
public class StarPushSupplyMaterialListener implements MessageListener {

    private final AilikeBIceSynthesisRecordInfoDAO ailikeBIceSynthesisRecordInfoDAO;
    private RedissonClient redissonClient;

    private StarSupActivityService starSupActivityService;

    private RpaVideoRecordDAO rpaVideoRecordDAO;

    private IceService iceService;

    private StarPushRecordDAO starPushRecordDAO;

    private AilikeBSupActivityDAO supActivityDAO;

    private DingDingCommonService dingDingCommonService;

    private MerchantStoreStarDAO merchantStoreStarDAO;

    private AilikeBIceSynthesisRecordInfoDAO alikeBIceSynthesisRecordInfoDAO;

    /**
     * 达人推活动重试
     *
     * @param message 消息体
     * @param context 上下文
     * @return 操作MQ行为
     */
    @Override
    public Action consume(Message message, ConsumeContext context) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        SupActivityRetryParam param = JSON.parseObject(body, SupActivityRetryParam.class);
        LogUtil.info(log, "consume >> StarPushSupplyMaterialListener >> 消息处理开始 >> param={}", body);
        StarMatrixHandleTypeEnum handleTypeEnum = StarMatrixHandleTypeEnum.getByValue(param.getHandleType());
        switch (handleTypeEnum) {
            case SUPPLY_MATERIAL:
                syncSupplyMaterialConsume(param.getActivityId());
                break;
            case FAILED_RETRY:
                releaseFailRetry(param.getSupActivityId());
                break;
            default:
                break;
        }
        LogUtil.info(log, "consume >> StarPushSupplyMaterialListener >> 消息处理结束 >> param={}", param);
        return Action.CommitMessage;
    }

    /**
     * 达人推视频发布失败重试
     *
     * @param supActivityId 顾客推活动Id
     */
    private void releaseFailRetry(String supActivityId) {
        LogUtil.info(log, "consume >> StarPushSupplyMaterialListener.releaseFailRetry >> 达人推视频发布失败重试处理开始 >> supActivityId={}", supActivityId);
        String lockKey = StrUtil.format(RedisPrefixConstant.LOCK_STAR_PUSH_FAILED_RETRY, supActivityId);
        RLock redisLock = redissonClient.getLock(lockKey);

        try {
            if (!redisLock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.SECONDS)) {
                throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage("已在其他地方处理");
            }
            // 查出最新一次的发布记录
            StarPushRecordDO recentOneBySupActivityId = starPushRecordDAO.getRecentOneBySupActivityId(supActivityId);
            List<RpaVideoRecordDO> rpaVideoRecordList = rpaVideoRecordDAO.getListBySupActivityIdAndRecordId(
                    supActivityId, recentOneBySupActivityId.getStarPushRecordId(), RpaVideoRecordReleasedEnum.FAIL_SENT.getValue()
            );
            if (CollUtil.isEmpty(rpaVideoRecordList)) {
                LogUtil.warn(log, "StarPushSupplyMaterialListener.releaseFailRetry >> 视频发布列表为空, supActivityId={}, starPushRecordId:{}", supActivityId, recentOneBySupActivityId.getStarPushRecordId());
                return;
            }
            AilikeBSupActivityDO oneBySupActivityId = supActivityDAO.getOneBySupActivityId(supActivityId);
            if (oneBySupActivityId == null) {
                LogUtil.warn(log, "StarPushSupplyMaterialListener.releaseFailRetry >> 活动不存在，supActivityId={}", supActivityId);
                return;
            }
            // 更新状态为发布中
            oneBySupActivityId.setPublishStatus(SupActivityPublishStatusEnum.PUBLISHING.getValue());
            oneBySupActivityId.setUpdateTime(null);
            supActivityDAO.updateById(oneBySupActivityId);
            // ! 尚未合成的记录，可能在合成过程中发生异常
            val notSynthesisedList = Lists.<RpaVideoRecordDO>newArrayList();
            for (val rpaVideoRecordDO : rpaVideoRecordList) {
                // 需要排除未绑定的达人
                MerchantStoreStarDO storeStarDO = merchantStoreStarDAO.getByUserUniqueId(oneBySupActivityId.getMerchantId(), oneBySupActivityId.getStoreId()
                        , rpaVideoRecordDO.getUserUniqueId(), rpaVideoRecordDO.getAgentMcnId(), rpaVideoRecordDO.getSource());
                if (MerchantStarBindStatusEnum.UNBINDING.getValue().equals(storeStarDO.getBindStatus())) {
                    continue;
                }
                // 如果Job和OSS Url不为空，视频已生成，则直接发送到RPA 发布队列中
                if (StringUtils.isNotBlank(rpaVideoRecordDO.getJobId()) && StringUtils.isNotBlank(rpaVideoRecordDO.getOssUrl())) {
                    iceService.pushDataToQueueRpa(rpaVideoRecordDO.getJobId(), rpaVideoRecordDO.getOssUrl());
                    continue;
                }
                // JobId不为空，但是OssUrl为空，表示ICE回调没有过来
                if (StringUtils.isNotBlank(rpaVideoRecordDO.getJobId())) {
                    // 查询ice合成表中是否存在记录，存在记录则推送至RPA
                    if (checkAndPushIceRecord(rpaVideoRecordDO.getJobId())) {
                        continue;
                    } else {
                        // 发送钉钉通知
                        dingDingCommonService.sendOpenCouponMessage("达人推",
                                StrUtil.format("重试发送异常，ICE回调等待超时, 达人推活动Id:{}", rpaVideoRecordDO.getSupActivityId()),
                                JSONObject.toJSONString(rpaVideoRecordDO)
                        );
                    }
                }
                // JobId 和 OssUrl都为空
                notSynthesisedList.add(rpaVideoRecordDO);
            }
            // 重发RPA失败数据，这些数据没有JobId
            starSupActivityService.retrySendRpaVideoLists(recentOneBySupActivityId.getMerchantId(), oneBySupActivityId, notSynthesisedList);
        } catch (Exception e) {
            LogUtil.error(log, "StarPushSupplyMaterialListener.releaseFailRetry >> 处理异常, supActivityId={}", supActivityId, e);
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (redisLock != null && redisLock.isLocked() && redisLock.isHeldByCurrentThread()) {
                    redisLock.unlock();
                }
            } catch (Exception e) {
                LogUtil.error(log, "StarPushSupplyMaterialListener.releaseFailRetry >> 锁释放异常");
            }
        }
        LogUtil.info(log, "consume >> StarPushSupplyMaterialListener.releaseFailRetry >> 达人推视频发布失败重试处理开始 >> activityId={}", supActivityId);
    }

    /**
     * 达人推补充素材处理
     *
     * @param activityId 达人推活动Id
     */
    private void syncSupplyMaterialConsume(String activityId) {
        LogUtil.info(log, "consume >> StarPushSupplyMaterialListener.syncSupplyMaterialConsume >> 达人推补充素材处理开始 >> activityId={}", activityId);
        // 加锁
        String lockKey = StrUtil.format(RedisPrefixConstant.LOCK_STAR_PUSH_SUPPLY_MATERIAL, activityId);
        RLock redisLock = redissonClient.getLock(lockKey);

        try {
            if (!redisLock.tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.SECONDS)) {
                throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage("已在其他地方处理");
            }
            AddMaterialAutoPublishParam autoPublishParam = new AddMaterialAutoPublishParam();
            autoPublishParam.setActivityId(activityId);
            starSupActivityService.syncSupplyMaterial(autoPublishParam);
        } catch (Exception e) {
            LogUtil.error(log, "StarPushSupplyMaterialListener.syncSupplyMaterialConsume > 达人推补充素材消息发生异常, activityId={}", activityId, e);
        } finally {
            try {
                // 锁不为空  是否还是锁定状态  当前执行线程的锁
                if (redisLock != null && redisLock.isLocked() && redisLock.isHeldByCurrentThread()) {
                    redisLock.unlock();
                }
            } catch (Exception e) {
                LogUtil.error(log, "StarPushSupplyMaterialListener.syncSupplyMaterialConsume >> 锁释放异常");
            }
        }
        LogUtil.info(log, "consume >> StarPushSupplyMaterialListener.syncSupplyMaterialConsume >> 达人推补充素材消息处理结束 >> activityId={}", activityId);
    }

    /**
     * 检查ice合成记录
     *
     * @param jobId 合成记录ID
     */
    private boolean checkAndPushIceRecord(String jobId) {
        val iceSynthesisRecordInfoDO = ailikeBIceSynthesisRecordInfoDAO.getInfoByJobId(jobId);
        // 如果ICE合成记录状态未找到，或者不为成功，直接返回
        if (iceSynthesisRecordInfoDO == null) {
            return false;
        }
        // 检查合成记录状态
        // 如果合成状态不为成功，或者接口类型不为RPA，直接返回
        if (ObjectUtil.notEqual(iceSynthesisRecordInfoDO.getFileStatus(), IceTaskInfoStatusEnum.SUCCESS.getValue())
                || !InterfaceTypeEnum.RPA.getValue().equals(iceSynthesisRecordInfoDO.getInterfaceType())) {
            return false;
        }
        // 将文件推送至Redis
        iceService.pushDataToQueueRpa(jobId, iceSynthesisRecordInfoDO.getOssUrl());
        return true;
    }
}
