package com.huike.nova.service.jobhandler;

import com.huike.nova.service.business.AlarmService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 通知告警定时任务
 *
 * <AUTHOR> (<EMAIL>)
 * @version AlarmJobHandler.java, v1.0 11/16/2023 11:58 John Exp$
 */
@Component
@Slf4j
@JobHandler("alarmJobHandler")
@AllArgsConstructor
public class AlarmJobHandler extends IJobHandler {

    private AlarmService alarmService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        // 发送定时消息到钉钉
        alarmService.alarmDbToDingDing();
        return ReturnT.SUCCESS;
    }
}
