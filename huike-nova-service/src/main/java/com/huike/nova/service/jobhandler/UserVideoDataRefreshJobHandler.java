/*
 * ailike.com
 * Copyright (C) 2022-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.common.util.FsDateUtils;
import com.huike.nova.service.business.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version UserVideoDataRefreshJobHandler.java, v 0.1 2023-01-04 14:27 zhangling
 */
@Component
@Slf4j
@JobHandler("userVideoDataRefreshJobHandler")
@AllArgsConstructor
public class UserVideoDataRefreshJobHandler extends IJobHandler {

    private TaskService taskService;

    /**
     * 获取7天内的数据
     *
     * @param param 从xxl-job-admin传过来的参数
     * @return ReturnT.SUCCESS/SUCCESS_MSG/SUCCESS_CODE or ReturnT.FAIL/FAIL_MSG/FAIL_CODE
     * @throws Exception 执行异常
     */
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("UserVideoDataRefreshJobHandler.execute >> 刷新用户抖音视频数据脚本执行开始：time = {}", DateUtil.now());
        String beforeWeek = DateUtil.format(FsDateUtils.getAfterDay(-7), FsDateUtils.SIMPLE_DATE_FORMAT);
        taskService.userVideoDataRefreshJob(beforeWeek, null);
        XxlJobLogger.log("UserVideoDataRefreshJobHandler.execute >> 刷新用户抖音视频数据脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}