/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.startask.web.star.WebStarListModel;
import com.huike.nova.service.domain.param.startask.web.star.BalanceDetailsPageListParam;
import com.huike.nova.service.domain.param.startask.web.star.WebStarListParam;

/**
 * <AUTHOR>
 * @version AgentWebStarService.java, v 0.1 2024-05-22 2:41 PM ruanzy
 */
public interface AgentWebStarService {

    /**
     * 达人列表
     *
     * @param param
     * @return
     */
    PageResult<WebStarListModel> list(PageParam<WebStarListParam> param);

    /**
     * 导出达人列表
     *
     * @param param
     */
    void requestExport(WebStarListParam param);

    /**
     * 收银明细导出
     *
     * @param param 入参
     */
    void requestExportBalanceDetailsList(BalanceDetailsPageListParam param);
}