/*
 * ailike.com
 * Copyright (C) 2022-2023 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.dao.entity.TakeoutOrderDO;
import com.huike.nova.service.domain.model.printer.BasePrinterGetModel;
import com.huike.nova.service.domain.model.printer.PrinterDetailModel;
import com.huike.nova.service.domain.model.printer.PrinterListModel;
import com.huike.nova.service.domain.model.printer.ReceiptConfigModel;
import com.huike.nova.service.domain.param.printer.BasePrinterGetParam;
import com.huike.nova.service.domain.param.printer.BindPrinterPreCheckParam;
import com.huike.nova.service.domain.param.printer.BroadcastTestParam;
import com.huike.nova.service.domain.param.printer.BsjBroadcastParam;
import com.huike.nova.service.domain.param.printer.GetPrinterDetailParam;
import com.huike.nova.service.domain.param.printer.PrinterBindParam;
import com.huike.nova.service.domain.param.printer.PrinterListParam;
import com.huike.nova.service.domain.param.printer.PrinterUnBindParam;
import com.huike.nova.service.domain.param.printer.PrinterUpdateParam;
import com.huike.nova.service.domain.param.printer.ReceiptConfigEditParam;
import com.huike.nova.service.domain.param.printer.ReceiptConfigGetParam;
import com.huike.nova.service.domain.param.printer.ReceiptConfigInitParam;
import com.huike.nova.service.domain.param.printer.ReceiptPrintParam;

/**
 * <AUTHOR>
 * @version PrinterService.java, v 0.1 2023-01-30 09:41 zhangling
 */
public interface PrinterService {

    /**
     * 获取打印机详情
     *
     * @param param
     * @return
     */
    PrinterDetailModel getPrinterDetail(GetPrinterDetailParam param);

    /**
     * 打印设备列表
     *
     * @param param
     * @return
     */
    PrinterListModel findPrinterList(PrinterListParam param);

    /**
     * 打印机信息修改
     *
     * @param param
     */
    void updatePrinter(PrinterUpdateParam param);

    /**
     * 商家绑定打印机
     *
     * @param param
     */
    void bindPrinter(PrinterBindParam param);

    /**
     * 商家解绑打印机
     *
     * @param param
     */
    void unBindPrinter(PrinterUnBindParam param);

    /**
     * 获取小票默认配置
     *
     * @param param
     * @return
     */
    ReceiptConfigModel getInitReceiptConfig(ReceiptConfigInitParam param);

    /**
     * 打印小票设置
     *
     * @param param
     */
    void editReceiptConfig(ReceiptConfigEditParam param);

    /**
     * 获取小票配置
     *
     * @param param
     * @return
     */
    ReceiptConfigModel getReceiptConfig(ReceiptConfigGetParam param);

    /**
     * 语音播报试听
     *
     * @param param
     */
    void broadcastTest(BroadcastTestParam param);

    /**
     * 博实结小票打印
     *
     * @param orderDO
     */
    void bsjReceiptPrint(TakeoutOrderDO orderDO);

    /**
     * 用户下单播报
     *
     * @param param
     */
    void bsjOrderBroadcast(BsjBroadcastParam param);

    /**
     * 门店打印小票
     *
     * @param param
     */
    void receiptPrint(ReceiptPrintParam param);

    /**
     * 获取打印机基础信息
     *
     * @param param
     * @return
     */
    BasePrinterGetModel getBasePrinterInfo(BasePrinterGetParam param);

    /**
     * 商家绑定打印机前置校验
     *
     * @param param
     */
    void bindPrinterPreCheck(BindPrinterPreCheckParam param);
}