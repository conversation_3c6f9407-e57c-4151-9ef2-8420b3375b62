package com.huike.nova.service.business.appletProxyDevelop.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.RequestMethodEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.HttpUtils;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.service.business.appletProxyDevelop.AppletProxyDevelopPlatformService;
import com.huike.nova.service.domain.model.appletProxyDevelop.platform.AuthTokenModel;
import com.huike.nova.service.domain.model.appletProxyDevelop.platform.AuthorizerAppletCode2sessionModel;
import com.huike.nova.service.domain.model.appletProxyDevelop.platform.GetComponentAccessTokenModel;
import com.huike.nova.service.domain.model.appletProxyDevelop.platform.PlatformBaseModel;
import com.huike.nova.service.domain.model.appletProxyDevelop.platform.RetrieveAuthorizationCodeModel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class AppletProxyDevelopPlatformServiceImpl implements AppletProxyDevelopPlatformService {

    private SysConfig sysConfig;
    private RedissonClient redissonClient;

    /**
     * 获取第三方小程序接口调用凭据
     */
    private static final String GET_COMPONENT_ACCESS_TOKEN_URL = "https://open.douyin.com/openapi/v2/auth/tp/token?component_appid={}&component_appsecret={}&component_ticket={}";
    /**
     * 找回授权码
     */
    private static final String RETRIEVE_AUTH_CODE_URL = "https://open.douyin.com/api/tpapp/v2/auth/retrieve_auth_code/";
    /**
     * 获取授权小程序token
     */
    private static final String GET_AUTH_TOKEN_URL = "https://open.douyin.com/api/tpapp/v2/auth/get_auth_token?grant_type=app_to_tp_authorization_code&authorization_code={}";
    /**
     * 刷新授权小程序token
     */
    private static final String REFRESH_AUTH_TOKEN_URL = "https://open.douyin.com/api/tpapp/v2/auth/get_auth_token?grant_type=app_to_tp_authorization_code&authorization_code={}";
    /**
     * authorizerAppletCode2session
     */
    private static final String AUTHORIZER_APPLET_CODE2SESSION_URL = "https://open.douyin.com/api/apps/v1/microapp/code2session/";

    /**
     * 获取第三方小程序接口调用凭据
     *
     * @param componentAppId 三方应用appid
     * @return 回调结果
     */
    @Override
    public GetComponentAccessTokenModel getComponentAccessToken(String componentAppId) {
        LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.getComponentAccessToken >> 接口开始 >> componentAppId = {}", componentAppId);
        try {
            final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.APPLET_PROXY_DEVELOP_COMPONENT_TICKET, componentAppId));
            if (!bucket.isExists()) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("ticket获取异常");
            }
            String ticket = bucket.get();
            // todo 注意当前三方小程序配置为配置项数据 建议维护三方小程序配置表，授权小程序配置表，授权小程序和三方小程序绑定表
            String queryHost = StrUtil.format(GET_COMPONENT_ACCESS_TOKEN_URL, componentAppId, sysConfig.getAppletProxyDevelopTripartiteAppletComponentAppSecret(), ticket);
            HttpResponse response = HttpUtils.doGet(queryHost, "", RequestMethodEnum.GET.getValue(), new HashMap<>(), null);
            String content = EntityUtils.toString(response.getEntity(), "UTF-8");
            GetComponentAccessTokenModel componentAccessTokenModel = JSON.parseObject(content, GetComponentAccessTokenModel.class);
            if (StringUtils.isNotBlank(componentAccessTokenModel.getMessage())) {
                LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.getComponentAccessToken >> 获取第三方小程序接口调用凭据异常 >> jsonObject = {}", componentAccessTokenModel);
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("message");
            }
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.getComponentAccessToken >> 接口结束 >> componentAccessTokenModel = {}", JSON.toJSONString(componentAccessTokenModel));
            return componentAccessTokenModel;
        } catch (Exception e) {
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.getComponentAccessToken >> 获取第三方小程序接口调用凭据全局异常 >> componentAppId = {} ", e, componentAppId);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("获取第三方小程序接口调用凭据全局异常");
        }
    }

    /**
     * 找回授权小程序授权码
     *
     * @param authorizationAppid 授权小程序appId
     * @return 回调结果
     */
    @Override
    public RetrieveAuthorizationCodeModel retrieveAuthorizationCode(String authorizationAppid) {
        LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.retrieveAuthorizationCode >> 接口开始 >> authorizationAppid = {}", authorizationAppid);
        try {

            Map<String, Object> mapParam = new HashMap<>(1);
            mapParam.put("authorization_appid", authorizationAppid);
            HttpResponse response = HttpUtils.doPost(RETRIEVE_AUTH_CODE_URL, "", RequestMethodEnum.POST.getValue(), this.getHeader(), null, mapParam);
            String content = EntityUtils.toString(response.getEntity(), "UTF-8");
            PlatformBaseModel platformBaseModel = JSON.parseObject(content, PlatformBaseModel.class);
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.retrieveAuthorizationCode >> 返回结果 >> platformBaseModel = {}", platformBaseModel);
            if (null == platformBaseModel || !CommonConstant.ZERO.equals(platformBaseModel.getErrNo())) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("找回授权小程序授权码异常");
            }
            return JSON.parseObject(platformBaseModel.getData(), RetrieveAuthorizationCodeModel.class);
        } catch (CommonException commonException) {
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.retrieveAuthorizationCode >> 通用错误 >> authorizationAppid = {}", commonException, authorizationAppid);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage(commonException.getMsg());
        } catch (Exception e) {
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.retrieveAuthorizationCode >> 找回授权小程序授权码全局异常 >> authorizationAppid = {}", e, authorizationAppid);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("找回授权小程序授权码全局异常");
        }
    }

    /**
     * 获取授权小程序token
     *
     * @param authorizationCode 授权码
     * @return 授权小程序token
     */
    @Override
    public AuthTokenModel getAuthToken(String authorizationCode) {
        LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.getAuthToken >> 接口开始 >> authorizationCode = {}", authorizationCode);
        try {
            String queryHost = StrUtil.format(GET_AUTH_TOKEN_URL, authorizationCode);
            HttpResponse response = HttpUtils.doGet(queryHost, "", RequestMethodEnum.GET.getValue(), this.getHeader(), null);
            String content = EntityUtils.toString(response.getEntity(), "UTF-8");
            PlatformBaseModel platformBaseModel = JSON.parseObject(content, PlatformBaseModel.class);
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.getAuthToken >> 返回结果 >> platformBaseModel = {}", platformBaseModel);
            if (null == platformBaseModel || !CommonConstant.ZERO.equals(platformBaseModel.getErrNo())) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("获取授权小程序token异常");
            }
            AuthTokenModel authTokenModel = JSON.parseObject(platformBaseModel.getData(), AuthTokenModel.class);
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.getAuthToken >> 获取token完成 >> authTokenModel = {}", JSON.toJSONString(authTokenModel));
            return authTokenModel;
        } catch (Exception e) {
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.getAuthToken >> 全局异常 >> authorizationCode = {}", e, authorizationCode);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("获取授权小程序token全局异常");
        }
    }

    /**
     * 刷新授权小程序token
     *
     * @param authorizationAppid     授权小程序appId
     * @param authorizerRefreshToken 刷新令牌
     * @return 授权小程序token
     */
    @Override
    public AuthTokenModel refreshAuthToken(String authorizationAppid, String authorizerRefreshToken) {
        LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.refreshAuthToken >> 接口开始 >> authorizationAppid = {},authorizerRefreshToken = {}", authorizationAppid, authorizerRefreshToken);
        try {
            String queryHost = StrUtil.format(REFRESH_AUTH_TOKEN_URL, authorizerRefreshToken);
            HttpResponse response = HttpUtils.doGet(queryHost, "", RequestMethodEnum.GET.getValue(), this.getHeader(), null);
            String content = EntityUtils.toString(response.getEntity(), "UTF-8");
            PlatformBaseModel platformBaseModel = JSON.parseObject(content, PlatformBaseModel.class);
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.refreshAuthToken >> 返回结果 >> platformBaseModel = {}", platformBaseModel);
            if (null == platformBaseModel || !CommonConstant.ZERO.equals(platformBaseModel.getErrNo())) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("刷新授权小程序token异常");
            }
            AuthTokenModel authTokenModel = JSON.parseObject(platformBaseModel.getData(), AuthTokenModel.class);
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.refreshAuthToken >> 刷新token完成 >> authTokenModel = {}", JSON.toJSONString(authTokenModel));
            return authTokenModel;
        } catch (Exception e) {
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.refreshAuthToken >> 全局异常 >> authorizationAppid = {},authorizationCode = {}", e, authorizationAppid, authorizerRefreshToken);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("刷新授权小程序token全局异常");
        }
    }

    /**
     * 授权小程序Code2session
     *
     * @param authorizationAppid 授权小程序appId
     * @param code               登录code
     * @return 小程序用户openId
     */
    @Override
    public AuthorizerAppletCode2sessionModel authorizerAppletCode2session(String authorizationAppid, String code) {
        LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.authorizerAppletCode2session >> 接口开始 >> authorizationAppid = {},code = {}", authorizationAppid, code);
        try {
            Map<String, Object> mapParam = new HashMap<>(1);
            mapParam.put("code", code);
            mapParam.put("app_id", authorizationAppid);
            HttpResponse response = HttpUtils.doPost(AUTHORIZER_APPLET_CODE2SESSION_URL, "", RequestMethodEnum.POST.getValue(), this.getHeader(), null, mapParam);
            String content = EntityUtils.toString(response.getEntity(), "UTF-8");
            PlatformBaseModel platformBaseModel = JSON.parseObject(content, PlatformBaseModel.class);
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.authorizerAppletCode2session >> 返回结果 >> platformBaseModel = {}", platformBaseModel);
            if (null == platformBaseModel || !CommonConstant.ZERO.equals(platformBaseModel.getErrNo())) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("找回授权小程序授权码异常");
            }
            return JSON.parseObject(platformBaseModel.getData(), AuthorizerAppletCode2sessionModel.class);

        } catch (Exception e) {
            LogUtil.info(log, "AppletProxyDevelopPlatformServiceImpl.authorizerAppletCode2session >> 全局异常 >> authorizationAppid = {},code = {}", e, authorizationAppid, code);
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("授权小程序Code2session全局异常");
        }
    }

    /**
     * 获取请求头 带三方应用token
     *
     * @return 请求头
     */
    private Map<String, String> getHeader() {
        GetComponentAccessTokenModel componentAccessTokenModel = this.getComponentAccessToken(sysConfig.getAppletProxyDevelopTripartiteAppletComponentAppid());
        Map<String, String> header = new HashMap<>(2);
        header.put("access-token", componentAccessTokenModel.getComponentAccessToken());
        header.put("content-type", "application/json");
        return header;
    }
}
