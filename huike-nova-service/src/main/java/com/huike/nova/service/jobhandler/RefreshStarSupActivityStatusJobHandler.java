/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 更新达人推发布状态达人推
 *
 * <AUTHOR>
 * @version RefreshStarSupActivityStatusJobHandler.java, v 0.1 2023-01-03 2:09 PM zhouyok
 */
@Component
@Slf4j
@JobHandler("refreshStarSupActivityStatusJobHandler")
@AllArgsConstructor
public class RefreshStarSupActivityStatusJobHandler extends IJobHandler {
    private TaskService taskService;

    @Override
    public ReturnT<String> execute(String supActivityId) throws Exception {
        XxlJobLogger.log("RefreshStarSupActivityStatusJobHandler >> 更新达人推发布状态：time = {}, supActivityId:{}", DateUtil.now(), supActivityId);
        taskService.refreshStarSupActivityStatus(supActivityId);
        XxlJobLogger.log("RefreshStarSupActivityStatusJobHandler >> 更新达人推发布状态：time = {}, supActivityId:{}", DateUtil.now(), supActivityId);
        return ReturnT.SUCCESS;
    }
}