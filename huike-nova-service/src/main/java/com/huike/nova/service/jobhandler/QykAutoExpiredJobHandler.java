/*
 *
 *  * Fshows Technology
 *  * Copyright (C) 2022-2024 All Rights Reserved.
 *
 */

package com.huike.nova.service.jobhandler;

import com.huike.nova.sdk.foundation.ext.UtilsExtKt;
import com.huike.nova.service.business.qyk.mina.QykMinaCardService;
import com.huike.nova.service.domain.param.qyk.mina.card.QykBatchAutoExpiredParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 商圈卡自动过期脚本
 *
 * <AUTHOR> (<EMAIL>)
 * @version QykAutoExpiredJobHandler.java, v1.0 2025/6/7 17:25 John Exp$
 */
@Component
@Slf4j
@JobHandler("qykAutoExpiredJobHandler")
@AllArgsConstructor
public class QykAutoExpiredJobHandler extends AbsJobHandler {

    private QykMinaCardService qykMinaCardService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("商圈卡自动过期脚本, traceId:" + UtilsExtKt.currentTraceId());
        qykMinaCardService.autoBatchExpiredCards(new QykBatchAutoExpiredParam());
        return ReturnT.SUCCESS;
    }
}
