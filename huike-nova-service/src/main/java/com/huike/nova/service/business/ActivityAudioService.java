/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.material.ActivityAudioDetailModel;
import com.huike.nova.service.domain.model.material.AudioListModel;
import com.huike.nova.service.domain.model.material.QueryCategoryListModel;
import com.huike.nova.service.domain.model.material.StoreAudioListModel;
import com.huike.nova.service.domain.param.material.ActivityAudioCreateParam;
import com.huike.nova.service.domain.param.material.BatchSavePresetAudioParam;
import com.huike.nova.service.domain.param.material.CollectAudioParam;
import com.huike.nova.service.domain.param.material.DeleteStoreAudioParam;
import com.huike.nova.service.domain.param.material.FindActivityAudioParam;
import com.huike.nova.service.domain.param.material.QueryAudioParam;
import com.huike.nova.service.domain.param.material.StoreAudioListParam;
import com.huike.nova.service.domain.param.material.UpdateAudioNameParam;
import com.huike.nova.service.domain.param.material.UrlAudioParsingParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version ActivityAudioService.java, v 0.1 2022-09-02 11:19 zhangling
 */
public interface ActivityAudioService {

    /**
     * 根据活动ID分页查询音频素材
     *
     * @param pageParam
     * @return
     */
    PageResult<ActivityAudioDetailModel> findActivityAudioPage(PageParam<FindActivityAudioParam> pageParam);

    /**
     * 查询音频素材
     *
     * @param pageParam
     * @return
     */
    PageResult<AudioListModel> findAudioPage(PageParam<QueryAudioParam> pageParam);

    /**
     * 批量新增音频
     *
     * @param param
     * @return
     */
    boolean batchSavePresetAudio(BatchSavePresetAudioParam param);

    /**
     * 新增活动音频素材
     *
     * @param param {@link ActivityAudioCreateParam}
     */
    void createActivityAudio(ActivityAudioCreateParam param);

    /**
     * 删除活动音频
     *
     * @param materialAudioId
     * @return
     */
    boolean deleteActivityAudio(String activityId, String materialAudioId);

    /**
     * 添加/取消音频收藏
     *
     * @param param
     * <AUTHOR>
     */
    void collectAudio(CollectAudioParam param);

    /**
     * 查询音频类目列表
     *
     * @param
     * @return {@link List<QueryCategoryListModel>}
     * <AUTHOR>
     */
    List<QueryCategoryListModel> findCategoryList();

    /**
     * 分页查询门店音频
     *
     * @param pageParam 分页参数
     * @return 门店音频列表
     */
    PageResult<StoreAudioListModel> findStoreAudioPage(PageParam<StoreAudioListParam> pageParam);

    /**
     * 更新音频名称
     *
     * @param param
     */
    void updateAudioName(UpdateAudioNameParam param);

    /**
     * 链接音频解析
     *
     * @param param
     */
    void urlAudioParsing(UrlAudioParsingParam param);

    /**
     * 删除门店音频
     *
     * @param param
     */
    void deleteStoreAudio(DeleteStoreAudioParam param);
}