package com.huike.nova.service.business.startask.mina;

import com.huike.nova.sdk.wechat.mina.model.domain.response.WechatMinaClientTokenResponse;
import com.huike.nova.service.domain.model.mina.customer.MinaLoginModel;
import com.huike.nova.service.domain.model.mina.wechat.WechatMinaGetUserPhoneNumberModel;
import com.huike.nova.service.domain.model.payment.NovaPrepayModel;
import com.huike.nova.service.domain.model.startask.mina.common.MinaChannelConfigModel;
import com.huike.nova.service.domain.model.startask.mina.templatemessage.WxSubscribeMessageListQueryModel;
import com.huike.nova.service.domain.param.startask.mina.pay.StarTaskMinaCloseUnpaidOrderParam;
import com.huike.nova.service.domain.param.startask.mina.pay.StarTaskMinaPrepayParam;
import com.huike.nova.service.domain.param.startask.mina.templatemessage.WxSubscribeMessageListQueryParam;
import com.huike.nova.service.domain.param.startask.mina.templatemessage.WxSubscribeMessageReportParam;

/**
 * 达人激励广场第三方微信服务
 *
 * <AUTHOR> (<EMAIL>)
 * @version StarTaskThirdPartyService.java, v1.0 12/09/2023 09:45 John Exp$
 */
public interface StarTaskWechatMinaService {

    /**
     * 查询小程序配置
     *
     * @param channelCode 通道类型
     * @return 小程序配置
     */
    MinaChannelConfigModel getMinaChannelConfig(String channelCode);

    /**
     * 小程序登录
     *
     * @param channelCode 小程序通道号
     * @param jsCode      jsCode
     * @return 小程序登录返回
     */
    MinaLoginModel toMinaLogin(String channelCode, String jsCode);

    /**
     * 获得用户手机号
     *
     * @param channelCode 小程序应用Id
     * @param code        小程序获取手机号码的授权码
     * @return 手机号码信息
     */
    WechatMinaGetUserPhoneNumberModel getUserPhoneNumber(String channelCode, String code);

    /**
     * 请求小程序的AccessToken
     *
     * @return AccessToken响应
     */
    WechatMinaClientTokenResponse requestMinaAccessToken(String appId, String appSecret);

    /**
     * 微信预支付 - 付呗通道
     *
     * @param param 预支付
     */
    NovaPrepayModel prepay(StarTaskMinaPrepayParam param);


    /**
     * 关闭指定任务下的未支付的订单，关闭成功之后，设置订单状态（异步接口）
     *
     * @param param 参数
     */
    void asyncCloseUnpaidOrder(StarTaskMinaCloseUnpaidOrderParam param);


    /**
     * 支付成功回调处理
     *
     * @param channelCode 通道
     * @param content     内容
     */
    void handlePaySuccessCallback(String channelCode, String content);

    /**
     * 获得缓存的的AccessToken
     *
     * @param appId appId
     */
    String getCachedAccessTokenByAppId(String appId);

    /**
     * 获得小程序AccessToken
     *
     * @param appId
     * @param appSecret
     * @return
     */
    String getMinaAccessToken(String appId, String appSecret);

}
