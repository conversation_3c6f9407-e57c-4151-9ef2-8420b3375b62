/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.job.activity.ActivityStatisticsDayService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version ActivityStatisticsDayOrderJobHandler.java, v 0.1 2023-07-12 9:34 AM ruanzy
 */
@Component
@Slf4j
@JobHandler("activityStatisticsDayOrderJobHandler")
@AllArgsConstructor
public class ActivityStatisticsDayOrderJobHandler extends IJobHandler {

    private ActivityStatisticsDayService activityStatisticsDayService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("ActivityStatisticsDayOrderJobHandler.execute >> 活动订单数据日统计脚本执行开始：time = {}", DateUtil.now());
        activityStatisticsDayService.statisticsActivityOrderDay(s);
        XxlJobLogger.log("ActivityStatisticsDayOrderJobHandler.execute >> 活动订单数据日统计脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}