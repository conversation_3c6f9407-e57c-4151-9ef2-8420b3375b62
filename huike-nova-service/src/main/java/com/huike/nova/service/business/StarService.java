package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.star.PageQueryStarModel;
import com.huike.nova.service.domain.param.akstarmatrix.PageQueryAgentMcnStarParam;
import com.huike.nova.service.domain.param.star.PageQueryStarParam;
import com.huike.nova.service.domain.param.star.RefreshStarInfoParam;
import com.huike.nova.service.domain.param.star.UpdateStarParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/10 10:47
 */
public interface StarService {
    /**
     * 商家达人矩阵号分页查询
     *
     * @param pageParam
     * @return
     */
    PageResult<PageQueryStarModel> pageQueryStarModel(PageParam<PageQueryStarParam> pageParam);

    /**
     * 代理商达人矩阵号分页查询
     *
     * @param pageParam
     * @return
     */
    PageResult<PageQueryStarModel> pageQueryAgentMcnStarModel(PageParam<PageQueryAgentMcnStarParam> pageParam);

    /**
     * 更新达人信息
     * @param param
     */
    void updateStar(UpdateStarParam param);

    /**
     * 查询门店下所有过期的账号数量
     *
     * @param merchantId
     * @param storeId
     * @return
     */
    Integer findExpireStarByStoreId(String merchantId, String storeId);

    /**
     * 更新达人详情
     *
     * @param param
     */
    void refreshStarInfo(RefreshStarInfoParam param);
}
