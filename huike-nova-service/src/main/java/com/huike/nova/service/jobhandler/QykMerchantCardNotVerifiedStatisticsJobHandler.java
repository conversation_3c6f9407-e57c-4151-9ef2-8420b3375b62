/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.huike.nova.service.business.common.QykCommonService;
import com.huike.nova.service.domain.param.qyk.mina.card.StatisticsNotVerifiedMerchantCardParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version ActivityStatisticsDayOrderJobHandler.java, v 0.1 2023-07-12 9:34 AM ruanzy
 */
@Component
@Slf4j
@JobHandler("QykMerchantCardNotVerifiedStatisticsJobHandler")
@AllArgsConstructor
public class QykMerchantCardNotVerifiedStatisticsJobHandler extends IJobHandler {

    private QykCommonService qykCommonService;


    @Override
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("QykMerchantCardNotVerifiedStatisticsJobHandler.execute >> 脚本执行开始：time = {}", DateUtil.now());
        qykCommonService.statisticsNotVerifiedMerchantCard(JSONArray.parseObject(param, StatisticsNotVerifiedMerchantCardParam.class));
        XxlJobLogger.log("QykMerchantCardNotVerifiedStatisticsJobHandler.execute >> 脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}