package com.huike.nova.service.jobhandler;

import com.alibaba.fastjson.JSONObject;
import com.huike.nova.service.business.qyk.mina.QykToolsService;
import com.huike.nova.service.domain.param.job.AutoReissueMerchantCardJobParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 自动补发商家券
 *
 * <AUTHOR> (<EMAIL>)
 * @version AutoReissueMerchantCardJobHandler.java, v1.0 10/01/2024 09:21 John Exp$
 */
@Component
@Slf4j
@JobHandler("autoReissueMerchantCardJobHandler")
@AllArgsConstructor
public class AutoReissueMerchantCardJobHandler extends AbsJob<PERSON>andler {

    private QykToolsService qykToolsService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            XxlJobLogger.log("执行补发商家券任务，traceId：{}", getOrGenerateTraceId());
            // 生成TraceId
            AutoReissueMerchantCardJobParam param = JSONObject.parseObject(s, AutoReissueMerchantCardJobParam.class);
            qykToolsService.reissueMerchantCardsByConfigId(param.getConfigId());
            return ReturnT.SUCCESS;
        } catch (Exception ex) {
            XxlJobLogger.log("执行补发商家券失败，错误：{}", ex);
            return ReturnT.FAIL;
        }
    }
}
