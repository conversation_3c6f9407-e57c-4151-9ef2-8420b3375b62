package com.huike.nova.service.listener;

import com.alibaba.fastjson.JSONArray;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.service.business.verifyTool.VerifyToolService;
import com.huike.nova.service.domain.param.verifyTool.PushServiceDoneParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class PushServiceDoneListener implements MessageListener {

    private VerifyToolService verifyToolService;


    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        LogUtil.info(log, "PushServiceDoneListener.consume >> 接口开始 >> body = {}", body);
        verifyToolService.pushServiceDone(JSONArray.parseObject(body, PushServiceDoneParam.class));
        LogUtil.info(log, "PushServiceDoneListener.consume >>  接口结束 >> body = {}", body);
        return Action.CommitMessage;
    }


}
