package com.huike.nova.service.business;

import com.alipay.api.domain.AlipayMarketingCertificateCertificationPrepareuseModel;
import com.alipay.api.domain.AlipayMarketingCertificateCertificationRefundModel;
import com.alipay.api.domain.AlipayOpenMiniOrderRefundModel;
import com.alipay.api.response.AlipayMarketingCertificateCertificationBatchqueryResponse;
import com.alipay.api.response.AlipayMarketingCertificateCertificationPrepareuseResponse;
import com.alipay.api.response.AlipayMarketingCertificateCertificationRefundResponse;
import com.alipay.api.response.AlipayMarketingCertificateCertificationUseResponse;
import com.alipay.api.response.AlipayMarketingCertificateUserBatchqueryResponse;
import com.alipay.api.response.AlipayOpenAppMiniTemplatemessageSendResponse;
import com.alipay.api.response.AlipayOpenMiniOrderQueryResponse;
import com.alipay.api.response.AlipayOpenMiniOrderRefundResponse;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.huike.nova.service.domain.param.alipay.AliOauthTokenParam;
import com.huike.nova.service.domain.param.alipay.AlipayCommonParam;
import com.huike.nova.service.domain.param.alipay.SendSubscribeMessageParam;

/**
 * <AUTHOR>
 * @date 2023年11月02日 16:09
 */
public interface AliPayService {


    /**
     * 支付宝订单查询
     *
     * @param orderQueryParam 订单查询参数
     * @return AlipayOpenMiniOrderQueryResponse
     */
    AlipayOpenMiniOrderQueryResponse alipayOrderQuery(AlipayCommonParam orderQueryParam);

    /**
     * 凭证核销准备
     *
     * @param param 入参
     * @return 出参
     */
    AlipayMarketingCertificateCertificationPrepareuseResponse voucherWriteOffPreparation(AlipayMarketingCertificateCertificationPrepareuseModel param);

    /**
     * 凭证核销
     *
     * @param param 入参
     * @return 出参
     */
    AlipayMarketingCertificateCertificationUseResponse voucherWriteOff(AlipayCommonParam param);

    /**
     * 凭证核销撤销
     *
     * @param param 入参
     * @return 出参
     */
    AlipayMarketingCertificateCertificationRefundResponse revokeVoucherWriteOffStatus(AlipayMarketingCertificateCertificationRefundModel param);

    /**
     * 用户凭证分页查询
     *
     * @param param 入参
     * @return 出参
     */
    AlipayMarketingCertificateUserBatchqueryResponse userCertificatePageQuery(AlipayCommonParam param);

    /**
     * 用户凭证批量查询
     *
     * @param param 入参
     * @return 出参
     */
    AlipayMarketingCertificateCertificationBatchqueryResponse userCertificateBatchQuery(AlipayCommonParam param);

    /**
     * 换取授权访问令牌
     */
    AlipaySystemOauthTokenResponse oauthToken(AliOauthTokenParam param);

    /**
     * 发送支付宝订阅消息
     */
    AlipayOpenAppMiniTemplatemessageSendResponse sendSubscribeMessage(SendSubscribeMessageParam param);

    /**
     * 支付宝小程序订单售后退款
     * 参数传入{@link AlipayOpenMiniOrderRefundModel}
     *
     * @param param 参数
     * @return 结果
     */
    AlipayOpenMiniOrderRefundResponse miniOrderRefund(AlipayCommonParam param);
}
