package com.huike.nova.service.business;

import com.huike.nova.service.domain.param.material.MaterialTransCodeParam;

import java.util.List;

/**
 * 视频转码服务
 *
 * <AUTHOR> (<EMAIL>)
 * @version MediaTransCodeService.java, v1.0 2024/4/1 17:53 John Exp$
 */
public interface MediaTransCodeService {

    /**
     * 检查视频是否需要转码（异步方法）
     *
     * @param param 请求参数
     */
    void checkVideoTransCode(MaterialTransCodeParam param);

    /**
     * 检查视频是否需要转码（异步方法）
     *
     * @param list 转码列表
     */
    void checkVideoTransCode(List<MaterialTransCodeParam> list);

    /**
     * 添加剪辑任务下的Id的视频转码
     *
     * @param activityId 剪辑任务Id
     */
    void transCodeSceneMaterialByActivityId(String activityId);

    /**
     * 添加门店下的门店素质的视频转码
     *
     * @param storeId 门店Id
     */
    void transCodeStoreMaterialByStoreId(String storeId);

    /**
     * 处理转码回调
     *
     * @param content 回调
     */
    void handleTransCodeCallback(String content);

    /**
     * 检查已提交（但是未完成）的的任务
     */
    void checkSubmittedJobs();
}
