/*
 * ailike.com
 * Copyright (C) 2022-2023 All Rights Reserved.
 */
package com.huike.nova.service.business;

/**
 * <AUTHOR>
 * @version PushService.java, v 0.1 2023-02-01 11:09 zhangling
 */
public interface PushService {

    /**
     * 播报未处理的订单
     * @param orderSn
     */
    void asyncCyclePushVoice(String orderSn);

    /**
     * 用户下单播报
     * @param orderSn
     */
    void onceCyclePushVoice(String orderSn);

    /**
     * 用户退款消息
     * @param storeId
     */
    void refundPushVoice(String storeId);
}