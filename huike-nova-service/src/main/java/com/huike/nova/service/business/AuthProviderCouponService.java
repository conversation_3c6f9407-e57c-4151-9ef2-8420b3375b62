package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.authprovider.*;
import com.huike.nova.service.domain.model.order.VerifyCouponModel;
import com.huike.nova.service.domain.param.authprovider.*;
import com.huike.nova.service.domain.param.order.VerifyCouponParam;

/**
 *
 * <AUTHOR>
 * @version AuthProviderCouponService.java, v 0.1 2023-02-06 4:10 下午 wangyi
 */
public interface AuthProviderCouponService {

    /**
     * 查询来团呗门店id
     *
     * @param appId
     * @param poiId
     * @return
     */
    String getMerchantStoreId(String appId, String poiId);

    /**
     * 对账统计
     *
     * @param param
     * @return
     */
    CouponOrderStatisticalModel couponOrderStatistical(CouponOrderStatisticalParam param);

    /**
     * 订单列表
     *
     * @param param
     * @return
     */
    PageResult<QueryCouponOrderListModel> queryCouponOrderList(PageParam<QueryCouponOrderListParam> param);

    /**
     * 订单详情
     *
     * @param param
     * @return
     */
    QueryCouponOrderInfoModel queryCouponOrderInfo(QueryCouponOrderInfoParam param);

    /**
     * 订单导出
     *
     * @param param
     * @return
     */
    ExportCouponOrderModel exportCouponOrderInfo(ExportCouponOrderParam param);

    /**
     * 撤销核销
     *
     * @param param
     * @return
     */
    CancelCouponModel cancelCoupon(CancelCouponParam param);

    /**
     * 核销
     *
     * @param param
     * @return
     */
    VerifyCouponModel verifyCoupon(VerifyCouponParam param);
}
