/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.dao.domain.result.web.SelectedStyleDTO;
import com.huike.nova.service.domain.model.material.FindStyleListModel;
import com.huike.nova.service.domain.model.material.TitleMaterialModel;
import com.huike.nova.service.domain.param.material.FindStyleListParam;
import com.huike.nova.service.domain.param.material.FindTitleParam;
import com.huike.nova.service.domain.param.material.SaveActivityTitleParam;
import com.huike.nova.service.domain.param.material.TextStyleUpdateParam;
import com.huike.nova.service.domain.param.material.TitleBatchSaveParam;
import com.huike.nova.service.domain.param.material.UpdateActivityTitleParam;

/**
 * <AUTHOR>
 * @version ActivityTitleService.java, v 0.1 2022-09-02 09:48 zhangling
 */
public interface ActivityTitleService {
    /**
     * 根据活动ID分页查询活动标题
     *
     * @param param
     * @return
     */
    PageResult<TitleMaterialModel> findTitlePage(PageParam<FindTitleParam> param);

    /**
     * 新增活动标题
     *
     * @param param
     * @return
     */
    Long saveActivityTitle(SaveActivityTitleParam param);

    /**
     * 批量新增标题
     *
     * @param param
     * @return
     */
    Long saveBatchTitle(TitleBatchSaveParam param);

    /**
     * 根据活动标题ID删除
     *
     * @param materialTitleId
     * @return
     */
    Long deleteActivityTitle(String activityId, String materialTitleId);

    /**
     * 更新活动标题内容
     *
     * @param param
     * @return
     */
    Long updateActivityTitle(UpdateActivityTitleParam param);

    /**
     * 设置顶部文案样式
     *
     * @param param 请求参数
     */
    void updateTextStyle(TextStyleUpdateParam param);

    /**
     * 查询顶部文案样式列表
     *
     * @return 响应
     */
    FindStyleListModel findStyleList(FindStyleListParam param);


    /**
     * 查询选中顶部文案样式列表
     *
     * @param activityId 剪辑任务id
     * @return 响应
     */
    SelectedStyleDTO selectedStyle(String activityId);
}