/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.authprovider.CancelCouponModel;
import com.huike.nova.service.domain.model.order.AmapCouponVerifyResultModel;
import com.huike.nova.service.domain.model.order.AmapScanCodeModel;
import com.huike.nova.service.domain.model.order.CouponInfoQueryModel;
import com.huike.nova.service.domain.model.order.FindChangeLogListModel;
import com.huike.nova.service.domain.model.order.FindReasonListModel;
import com.huike.nova.service.domain.model.order.OperatePayOrderModel;
import com.huike.nova.service.domain.model.order.OrderDetailGetModel;
import com.huike.nova.service.domain.model.order.OrderTypeModel;
import com.huike.nova.service.domain.model.order.ScanCodeModel;
import com.huike.nova.service.domain.model.order.TakeoutDetailModel;
import com.huike.nova.service.domain.model.order.VerifyCouponModel;
import com.huike.nova.service.domain.param.order.*;

/**
 * <AUTHOR>
 * @version OrderService.java, v 0.1 2022-11-07 10:19 AM ruanzy
 */
public interface OrderService {

    /**
     * 查看订单列表
     *
     * @param param
     * @return
     */
    PageResult<OrderDetailGetModel> findOrderList(PageParam<OrderListParam> param);

    /**
     * 查看订单详情
     *
     * @param param
     * @return
     */
    OrderDetailGetModel getOrderDetail(OrderDetailGetParam param);

    /**
     * 扫码验券准备
     *
     * @param param
     * @return
     */
    ScanCodeModel scanCode(ScanCodeParam param);

    /**
     * 扫码验券准备
     *
     * @param param
     * @return
     */
    AmapScanCodeModel amapScanCode(AmapScanCodeParam param);

    /**
     * 输码验券准备
     *
     * @param param
     * @return
     */
    ScanCodeModel inputCode(InputCodeParam param);

    /**
     * 券码核销
     *
     * @param param
     * @return
     */
    VerifyCouponModel verifyCoupon(VerifyCouponParam param);
    /**
     * 来团呗撤销核销
     *
     * @param param
     * @return
     */
    CancelCouponModel LTBCancelCoupon(CancelCouponParam param);
    /**
     * 撤销核销
     *
     * @param param
     */
    void cancelVerify(CancelVerifyCouponParam param);

    /**
     * 高德券码核销
     *
     * @param param
     * @return
     */
    AmapCouponVerifyResultModel amapVerifyCoupon(AmapVerifyCouponParam param);

    /**
     * 查看券状态信息
     *
     * @param param
     * @return
     */
    CouponInfoQueryModel queryCouponInfo(CouponInfoQueryParam param);


    /**
     * 查看外卖订单列表
     *
     * @param param
     * @return
     */
    PageResult<OrderTypeModel> findTakeoutOrderList(PageParam<TakeoutOrderListParam> param);

    /**
     * 订单下拉详情
     *
     * @param param
     * @return
     */
    TakeoutDetailModel getTakeoutDetail(TakeoutDetailParam param);

    /**
     * 订单流转跟踪日志
     *
     * @param param
     * @return
     */
    FindChangeLogListModel findChangeLog(FindChangeLogParam param);

    /**
     * 拒绝订单理由列表
     *
     * @return
     */
    FindReasonListModel findRefundReasonList();

    /**
     * 取消订单理由列表
     *
     * @param param
     * @return
     */
    FindReasonListModel findCancelReasonList(FindCancelReasonListParam param);

    /**
     * 商家自配送-回传配送信息接口
     *
     * @param param
     * @return
     */
    OperatePayOrderModel configureSelfDelivery(ConfigureSelfDeliveryParam param);


    /**
     * 待退款订单立即处理
     *
     * @param param
     * @return
     */
    OperatePayOrderModel dealRefundOrder(DealRefundOrderParam param);


    /**
     * 商家接单/拒单
     *
     * @param param
     * @return
     */
    OperatePayOrderModel operatePayOrder(OperatePayOrderParam param);

    /**
     * 商家取消订单
     *
     * @param param
     */
    void refundApply(RefundApplyParam param);

    /**
     * 三方配送商标示列表
     *
     * @return
     */
    FindReasonListModel findThreeSourceList();
}