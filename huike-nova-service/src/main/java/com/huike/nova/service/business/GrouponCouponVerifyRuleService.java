package com.huike.nova.service.business;

import com.huike.nova.dao.entity.AilikeBOrderDO;

import javax.annotation.Nonnull;

/**
 * 团购券码核销规则服务
 *
 * <AUTHOR> (<EMAIL>)
 * @version GrouponCouponVerifyRuleService.java, v1.0 2025-06-23 9:53 John Exp$
 */
public interface GrouponCouponVerifyRuleService {

    /**
     * 检查商圈卡核销权限
     *
     * @param orderDO 订单记录列表
     */
    void checkVerifyPermission(@Nonnull AilikeBOrderDO orderDO);

    /**
     * 检查商圈卡的核销权限
     *
     * @param outOrderSn 订单号
     */
    void checkVerifyPermission(String outOrderSn);
}
