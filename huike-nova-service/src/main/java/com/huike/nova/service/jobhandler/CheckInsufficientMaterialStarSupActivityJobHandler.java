package com.huike.nova.service.jobhandler;

import com.huike.nova.service.business.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 检查所有素材不足且为手动合成的达人推活动
 *
 * <AUTHOR> (<EMAIL>)
 * @version CheckInsufficientMaterialStarSupActivityJobHandler.java, v1.0 05/10/2024 16:54 John Exp$
 */
@Component
@Slf4j
@JobHandler("checkInsufficientMaterialStarSupActivityJobHandler")
@AllArgsConstructor
public class CheckInsufficientMaterialStarSupActivityJobHandler extends IJobHandler {

    private TaskService taskService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        taskService.checkInsufficientMaterialStarSupActivityJobHandler();
        return ReturnT.SUCCESS;
    }
}
