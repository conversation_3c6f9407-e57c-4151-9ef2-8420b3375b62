/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.service.business;

import cn.hutool.core.lang.Pair;
import com.annimon.stream.function.Consumer;
import com.huike.nova.dao.entity.AilikeBActivityDO;
import com.huike.nova.dao.entity.AilikeBVideoCartesianProductDO;
import com.huike.nova.dao.entity.RpaStarInfoDO;
import com.huike.nova.service.domain.param.releasevideo.NewVideoSynthesisParam;
import com.huike.nova.service.domain.param.starsupactivity.SupActivityStarParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version VideoCheckService.java, v 0.1 2022-09-15 8:34 PM ruanzy
 */
public interface VideoCheckService {

    /**
     * 计算笛卡尔积
     *
     * @param activityId
     */
    void cartesianProduct(String activityId);

    /**
     * 视频限制校验-自动合成
     *
     * @param activityId
     */
    void limitVideo(String activityId);

    /**
     * 视频限制校验-手动合成
     *
     * @param activityId
     * @param presetGroupIds
     */
    void limitManualVideo(String activityId, List<String> presetGroupIds);

    /**
     * rpa视频限制校验
     *
     * @param supActivityId
     * @param activityDO
     */
    void videoSynthesisRpa(String supActivityId, AilikeBActivityDO activityDO, List<RpaStarInfoDO> starInfoDOS);

    /**
     * 活动校验
     *
     * @param activityDO
     */
    void verifyActivity(AilikeBActivityDO activityDO);

    /**
     * 加锁获取预合成视频
     *
     * @param activityId 剪辑任务id
     * @param num        获取数量
     */
    List<AilikeBVideoCartesianProductDO> findPreSynthesisList(String activityId, Integer num, List<String> presetGroupIds);

    /**
     * 视频合成
     *
     * @param param
     * @param preVideoList 合成视频笛卡尔积列表
     */
    void videoSynthesisSound(NewVideoSynthesisParam param, List<AilikeBVideoCartesianProductDO> preVideoList);
}
