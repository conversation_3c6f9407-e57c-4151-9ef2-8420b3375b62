package com.huike.nova.service.jobhandler.startask;

import com.huike.nova.service.business.startask.web.StarTaskWebExportService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 过期文件清理任务
 *
 * <AUTHOR> (<EMAIL>)
 * @version ExpiredFileCleanUpJobHandler.java, v1.0 12/19/2023 09:33 John Exp$
 */
@Component
@Slf4j
@JobHandler("expiredFileCleanUpJobHandler")
@AllArgsConstructor
public class ExpiredFileCleanUpJobHandler extends IJobHandler {

    private StarTaskWebExportService webExportService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        webExportService.cleanUp();
        return ReturnT.SUCCESS;
    }
}
