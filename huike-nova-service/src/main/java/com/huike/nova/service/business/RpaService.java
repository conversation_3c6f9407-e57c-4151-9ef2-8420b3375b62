package com.huike.nova.service.business;

import com.huike.nova.service.domain.param.rpa.DyCreatorAuthorizationParam;
import com.huike.nova.service.domain.param.rpa.DyCreatorRemoteBindingParam;
import com.huike.nova.service.domain.param.rpa.DyUserUnauthorizedParam;

/**
 * <AUTHOR>
 * @date 2023/2/13 13:38
 * @copyright 2022 barm Inc. All rights reserved
 */
public interface RpaService {
    /**
     * 异步处理抖音创作者平台授权回调
     *
     * @param param
     */
    void dyCreatorAuthorization(DyCreatorAuthorizationParam param);

    /**
     * 抖音远程绑定
     *
     * @param param
     */
    void dyCreatorRemoteBinding(DyCreatorRemoteBindingParam param);

    /**
     * 抖音用户取消授权
     *
     * @param
     */
    void dyUserUnauthorized(DyUserUnauthorizedParam param);
}
