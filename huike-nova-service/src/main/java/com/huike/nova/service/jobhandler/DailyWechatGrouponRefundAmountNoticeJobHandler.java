package com.huike.nova.service.jobhandler;

import com.alibaba.fastjson.JSONObject;
import com.annimon.stream.function.Supplier;
import com.huike.nova.service.business.TaskService;
import com.huike.nova.service.domain.param.job.DailySettledAmountNotifyParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 每日微信团购退款金额提醒
 *
 * <AUTHOR> (<EMAIL>)
 * @version DailyWechatGrouponRefundAmountNoticeJobHandler.java, v1.0 2025-04-27 10:17 John Exp$
 */
@Component
@AllArgsConstructor
@Slf4j
@JobHandler("dailyWechatGrouponRefundAmountNoticeJobHandler")
public class DailyWechatGrouponRefundAmountNoticeJobHandler extends AbsJobHandler {

    /**
     * 微信自研团购服务
     */
    private TaskService taskService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        DailySettledAmountNotifyParam param = Supplier.Util.safe(() ->
                JSONObject.parseObject(s, DailySettledAmountNotifyParam.class),
                new DailySettledAmountNotifyParam())
                .get();
        taskService.wechatDailyGrouponRefundNotice(param.getDate(), param.getAt());
        return ReturnT.SUCCESS;
    }
}
