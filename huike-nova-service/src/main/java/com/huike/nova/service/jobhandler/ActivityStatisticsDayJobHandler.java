/*
 * ailike.com
 * Copyright (C) 2022-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version ActivityStatisticsDayJobHandler.java, v 0.1 2023-02-10 10:54 zhangling
 */
@Component
@Slf4j
@JobHandler("activityStatisticsDayJobHandler")
@AllArgsConstructor
public class ActivityStatisticsDayJobHandler extends IJobHandler {

    private TaskService taskService;

    /**
     * execute handler, invoked when executor receives a scheduling request
     *
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("ActivityStatisticsDayJobHandler.execute >> 活动数据日统计脚本执行开始：time = {}", DateUtil.now());
        taskService.statisticsActivityDay(param);
        XxlJobLogger.log("ActivityStatisticsDayJobHandler.execute >> 活动数据日统计脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}