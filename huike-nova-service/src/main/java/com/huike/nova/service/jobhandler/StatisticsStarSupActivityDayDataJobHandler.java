package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 统计每天达人推数据的新增数据
 * 每天0.30执行(等待rpa0.执行完毕以后再执行)
 *
 * <AUTHOR>
 * @date 2023/3/14 15:23
 * @copyright 2022 barm Inc. All rights reserved
 */
@Component
@Slf4j
@JobHandler("statisticsStarSupActivityDayDataJobHandler")
@AllArgsConstructor
public class StatisticsStarSupActivityDayDataJobHandler extends IJobHandler {
    private TaskService taskService;

    @Override
    public ReturnT<String> execute(String supActivityId) throws Exception {
        XxlJobLogger.log("RefreshStarSupActivityStatusJobHandler >> 统计每天达人推数据的新增数据：time = {}, supActivityId:{}", DateUtil.now(), supActivityId);
        taskService.statisticsStarSupActivityDayDataV2(null);
        XxlJobLogger.log("RefreshStarSupActivityStatusJobHandler >> 统计每天达人推数据的新增数据：time = {}, supActivityId:{}", DateUtil.now(), supActivityId);
        return ReturnT.SUCCESS;
    }
}
