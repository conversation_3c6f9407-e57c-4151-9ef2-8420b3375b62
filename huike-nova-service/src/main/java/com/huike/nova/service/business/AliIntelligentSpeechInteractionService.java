package com.huike.nova.service.business;

import com.huike.nova.service.domain.model.alispeech.TextListModel;
import com.huike.nova.service.domain.param.alispeech.SpeechToTextParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年07月25日 09:55
 */
public interface AliIntelligentSpeechInteractionService {

    /**
     * 获取clientToken
     *
     * @return AccessToken
     */
    String getAccessToken();


    /**
     * 语音转文字
     *
     * @param param 语音地址
     * @return TextListModel
     */
    List<TextListModel> speechToText(SpeechToTextParam param);


}
