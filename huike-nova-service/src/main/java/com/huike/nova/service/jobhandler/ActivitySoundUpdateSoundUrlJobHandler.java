/*
 * ailike.com
 * Copyright (C) 2022-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.service.business.ActivitySoundService;
import com.huike.nova.service.business.TaskService;
import com.huike.nova.service.domain.param.material.SoundBatchSaveParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@JobHandler("activitySoundUpdateSoundUrlJobHandler")
@AllArgsConstructor
public class ActivitySoundUpdateSoundUrlJobHandler extends IJobHandler {

    private TaskService taskService;

    private ActivitySoundService activitySoundService;

    /**
     * 修改口播脚本url
     *
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("ActivitySoundUpdateSoundUrlJobHandler.execute >> 修改口播脚本url开始：time = {}", DateUtil.now());
        List<String> activityIdList = activitySoundService.updateNullSound(param);
        if (CollectionUtil.isNotEmpty(activityIdList)) {
            for (String item : activityIdList) {
                LogUtil.info(log, "ActivitySoundUpdateSoundUrlJobHandler.execute >> 循环开始 >> item = {}", item);
                SoundBatchSaveParam soundParam = new SoundBatchSaveParam();
                soundParam.setActivityId(item);
                activitySoundService.batchUpdateSoundUrl(soundParam);
            }
        }
        XxlJobLogger.log("ActivitySoundUpdateSoundUrlJobHandler.execute >> 修改口播脚本url结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}