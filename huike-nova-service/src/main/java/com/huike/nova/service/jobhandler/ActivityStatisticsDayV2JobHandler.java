/*
 * Fshows Technology
 * Copyright (C) 2022-2024 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.TaskService;
import com.huike.nova.service.domain.param.activity.DayStatisticsActivityDayParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 活动日数据统计2.0
 *
 * <AUTHOR> (<EMAIL>)
 * @version ActivityStatisticsDayV2JobHandler.java, v1.0 2024/5/5 22:50 John Exp$
 */
@Component
@Slf4j
@JobHandler("activityStatisticsDayV2JobHandler")
@AllArgsConstructor
public class ActivityStatisticsDayV2JobHandler extends IJobHandler {

    private TaskService taskService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("ActivityStatisticsDayV2JobHandler.execute >> 活动数据日统计脚本2.0执行开始：time = {}", DateUtil.now());
        DayStatisticsActivityDayParam param = new DayStatisticsActivityDayParam();
        taskService.statisticsActivityDayV2(param);
        XxlJobLogger.log("ActivityStatisticsDayV2JobHandler.execute >> 活动数据日统计脚本2.0执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}
