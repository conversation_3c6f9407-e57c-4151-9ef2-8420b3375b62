/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.web.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.OemCommonConstant;
import com.huike.nova.common.constant.startask.StarTaskConstant;
import com.huike.nova.common.constant.startask.web.StarTaskWebRedisPrefixConstant;
import com.huike.nova.common.enums.EmployeeAdminEnum;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.GrantPlatformEnum;
import com.huike.nova.common.enums.oem.BooleanEnum;
import com.huike.nova.common.enums.startask.mina.StarTaskIdentityTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.common.util.ExceptionUtil;
import com.huike.nova.common.util.IdWorkerUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.common.util.XorCipherUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.result.startask.AgentAreaResultDTO;
import com.huike.nova.dao.domain.result.startask.PageAgentListResultDTO;
import com.huike.nova.dao.entity.AilikeRoleDO;
import com.huike.nova.dao.entity.AilikeRoleIdentityDO;
import com.huike.nova.dao.entity.StarTaskAgentAccountDO;
import com.huike.nova.dao.entity.StarTaskAgentAreaDO;
import com.huike.nova.dao.entity.StarTaskAgentDO;
import com.huike.nova.dao.entity.StarTaskAgentUserDO;
import com.huike.nova.dao.entity.StarTaskIdentityDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.AilikeRoleDAO;
import com.huike.nova.dao.repository.AilikeRoleIdentityDAO;
import com.huike.nova.dao.repository.StarTaskAgentAccountDAO;
import com.huike.nova.dao.repository.StarTaskAgentAreaDAO;
import com.huike.nova.dao.repository.StarTaskAgentDAO;
import com.huike.nova.dao.repository.StarTaskAgentUserDAO;
import com.huike.nova.dao.repository.StarTaskIdentityDAO;
import com.huike.nova.service.business.startask.web.StarTaskWebAgentService;
import com.huike.nova.service.domain.mapper.startask.web.StarTaskWebAgentServiceObjMapper;
import com.huike.nova.service.domain.model.startask.AgentAreaModel;
import com.huike.nova.service.domain.model.startask.web.agent.WebAgentDetailModel;
import com.huike.nova.service.domain.model.startask.web.agent.WebAgentListModel;
import com.huike.nova.service.domain.param.startask.web.agent.AgentAreaParam;
import com.huike.nova.service.domain.param.startask.web.agent.WebAgentAddParam;
import com.huike.nova.service.domain.param.startask.web.agent.WebAgentDetailParam;
import com.huike.nova.service.domain.param.startask.web.agent.WebAgentListParam;
import com.huike.nova.service.domain.param.startask.web.agent.WebAgentUpdateParam;
import com.huike.nova.service.domain.param.startask.web.agent.WebOperateAgentParam;
import com.huike.nova.service.enums.startask.WebOperationAgentType;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.assertj.core.util.Lists;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version StarTaskWebAgentServiceImpl.java, v 0.1 2024-05-15 9:53 AM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class StarTaskWebAgentServiceImpl implements StarTaskWebAgentService {

    private StarTaskAgentUserDAO starTaskAgentUserDAO;

    private StarTaskAgentAreaDAO starTaskAgentAreaDAO;

    private StarTaskAgentDAO starTaskAgentDAO;

    private StarTaskAgentAccountDAO starTaskAgentAccountDAO;

    private AilikeRoleIdentityDAO ailikeRoleIdentityDAO;

    private AilikeRoleDAO ailikeRoleDAO;

    private StarTaskIdentityDAO starTaskIdentityDAO;

    private StarTaskWebAgentServiceObjMapper starTaskWebAgentServiceObjMapper;

    private SysConfig sysConfig;

    private TransactionTemplate transactionTemplate;

    private RedissonClient redissonClient;

    /**
     * 新增区代
     *
     * @param param
     */
    @Override
    public void add(WebAgentAddParam param) {
        // 加锁
        String lock = StrUtil.format(StarTaskWebRedisPrefixConstant.STAR_TASK_AGENT_WEB_AGENT_OPERATE_CACHE_KEY);
        try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
            redisLockHelper.tryLock();
            // 业务逻辑
            this.addAgent(param);
        } catch (Exception e) {
            LogUtil.warn(log, "StarTaskWebAgentServiceImpl" + " >>>>> " + "add" + "  锁释放失败", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }

    /**
     * 修改区代
     *
     * @param param
     */
    @Override
    public void update(WebAgentUpdateParam param) {
        String lock = StrUtil.format(StarTaskWebRedisPrefixConstant.STAR_TASK_AGENT_WEB_AGENT_OPERATE_CACHE_KEY);
        try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
            redisLockHelper.tryLock();
            String agentId = param.getAgentId();
            // 校验区域
            StarTaskAgentDO agentInfo = starTaskAgentDAO.getAgentInfo(agentId);
            if (Objects.isNull(agentInfo)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("代理商信息不存在");
            }
            List<StarTaskAgentAreaDO> checkAgentAreaToUpdate = starTaskAgentAreaDAO.checkAgentAreaToUpdate(
                    param.getAreaList().stream().map(AgentAreaParam::getCity).collect(Collectors.toList()), agentId);
            if (CollectionUtil.isNotEmpty(checkAgentAreaToUpdate)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("所选区域已被代理，请重新选择");
            }
            // 更新用户区域
            List<StarTaskAgentAreaDO> areaList = Lists.newArrayList();
            for (AgentAreaParam agentAreaParam : param.getAreaList()) {
                StarTaskAgentAreaDO agentAreaDO = new StarTaskAgentAreaDO();
                agentAreaDO.setAgentId(agentId);
                agentAreaDO.setProvince(agentAreaParam.getProvince());
                agentAreaDO.setCity(agentAreaParam.getCity());
                agentAreaDO.setUsedStatus(agentInfo.getUsedStatus());
                areaList.add(agentAreaDO);
            }
            // 事务
            transactionTemplate.execute(status -> {
                // 更新用户基本信息
                starTaskAgentDAO.updateByAgentId(agentId, param.getName(), param.getMaxAgentRate(), param.getRemark());
                // 更新用户联系人
                starTaskAgentUserDAO.updateContactName(param.getAgentUserId(), param.getContactName());
                // 删除区域
                starTaskAgentAreaDAO.removeByAgentId(agentId);
                starTaskAgentAreaDAO.batchAgentArea(areaList);
                return Boolean.TRUE;
            });
        } catch (Exception e) {
            LogUtil.warn(log, "StarTaskWebAgentServiceImpl" + " >>>>> " + "update" + "  锁释放失败", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }

    /**
     * 区代详情
     *
     * @param param
     * @return
     */
    @Override
    public WebAgentDetailModel detail(WebAgentDetailParam param) {
        // 查询区代用户信息
        StarTaskAgentUserDO agentUser = starTaskAgentUserDAO.getAgentUserByAgentUserId(param.getAgentUserId());
        if (Objects.isNull(agentUser)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("信息异常");
        }
        String agentId = agentUser.getAgentId();
        // 查询区代基本信息
        StarTaskAgentDO agentInfo = starTaskAgentDAO.getAgentInfo(agentId);
        if (Objects.isNull(agentInfo)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("信息异常");
        }
        // 查询区代区域信息
        List<AgentAreaResultDTO> list = starTaskAgentAreaDAO.findAgentAreaDTOByAgentId(agentId);
        WebAgentDetailModel model = new WebAgentDetailModel();
        model.setAgentUserId(agentUser.getAgentUserId());
        model.setPhoneNumber(FieldEncryptUtil.decode(agentUser.getPhoneNumber()));
        model.setContactName(agentUser.getContactName());
        model.setAgentId(agentId);
        model.setName(agentInfo.getName());
        model.setRemark(agentInfo.getRemark());
        model.setMaxAgentRate(agentInfo.getMaxAgentRate());
        model.setAreaList(starTaskWebAgentServiceObjMapper.toAgentAreaModelList(list));
        return model;
    }

    /**
     * 区代列表
     *
     * @param param
     * @return
     */
    @Override
    public PageResult<WebAgentListModel> list(PageParam<WebAgentListParam> param) {
        // 区代列表
        Page<PageAgentListResultDTO> resultDTOPage = starTaskAgentDAO.pageAgentList(starTaskWebAgentServiceObjMapper.toPageAgentListParamDTOPage(param));
        List<PageAgentListResultDTO> records = resultDTOPage.getRecords();
        List<WebAgentListModel> list = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(records)) {
            List<String> agentIds = records.stream().map(PageAgentListResultDTO::getAgentId).collect(Collectors.toList());
            // 代理区域
            List<AgentAreaResultDTO> areaList = starTaskAgentAreaDAO.findByAgentIds(agentIds);
            Map<String, List<AgentAreaResultDTO>> areaMap = areaList.stream().collect(Collectors.groupingBy(AgentAreaResultDTO::getAgentId));
            // 商户/达人数量
            List<StarTaskIdentityDO> identityList = starTaskIdentityDAO.findIdentityByCity(areaList.stream().map(AgentAreaResultDTO::getCity).collect(Collectors.toList()));
            Map<String, List<StarTaskIdentityDO>> identityMap = identityList.stream().collect(Collectors.groupingBy(StarTaskIdentityDO::getCity));
            for (PageAgentListResultDTO record : records) {
                WebAgentListModel model = starTaskWebAgentServiceObjMapper.toWebAgentListModel(record);
                // 手机号解密
                model.setPhoneNumber(FieldEncryptUtil.decode(record.getPhoneNumber()));
                // 代理区域列表
                List<AgentAreaResultDTO> mapList = areaMap.getOrDefault(record.getAgentId(), Lists.newArrayList());
                int merchantNumber = CommonConstant.ZERO;
                int starNumber = CommonConstant.ZERO;
                List<AgentAreaModel> modelList = Lists.newArrayList();
                for (AgentAreaResultDTO agentAreaResultDTO : mapList) {
                    AgentAreaModel areaModel = starTaskWebAgentServiceObjMapper.toAgentAreaModel(agentAreaResultDTO);
                    // 商户/达人数量计算
                    List<StarTaskIdentityDO> area = identityMap.getOrDefault(agentAreaResultDTO.getCity(), Lists.newArrayList());
                    long size = area.stream().filter(item -> StarTaskIdentityTypeEnum.MERCHANT.getValue().equals(item.getIdentityType())).count();
                    merchantNumber += size;
                    starNumber += area.size() - size;
                    modelList.add(areaModel);
                }
                model.setAreaList(modelList);
                // 商户/达人数量
                model.setMerchantNumber(merchantNumber);
                model.setStarNumber(starNumber);
                // 域名
                model.setDomainName(sysConfig.getStarTaskAgentDomainName());
                model.setEncodeInfo(XorCipherUtil.xorEncode(model.getAgentUserId(), CommonConstant.XOR_IV));
                list.add(model);
            }
        }
        PageResult<WebAgentListModel> pageResult = new PageResult<>();
        pageResult.setRecords(list);
        pageResult.setTotal(resultDTOPage.getTotal());
        pageResult.setSize(resultDTOPage.getSize());
        pageResult.setCurrent(resultDTOPage.getCurrent());
        return pageResult;
    }

    /**
     * 区代操作
     *
     * @param param
     */
    @Override
    public void operate(WebOperateAgentParam param) {
        WebOperationAgentType typeEnum = WebOperationAgentType.getByValue(param.getOperationType());
        if (Objects.isNull(typeEnum)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("类型不正确");
        }
        String lock = StrUtil.format(StarTaskWebRedisPrefixConstant.STAR_TASK_AGENT_WEB_AGENT_OPERATE_CACHE_KEY);
        try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
            redisLockHelper.tryLock();
            // 业务逻辑
            String agentId = param.getAgentId();
            StarTaskAgentDO agentInfo = starTaskAgentDAO.getAgentInfo(agentId);
            if (Objects.isNull(agentInfo)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("代理商信息不存在");
            }
            Integer usedStatus = agentInfo.getUsedStatus();
            switch (typeEnum) {
                case ENABLED:
                    if (WebOperationAgentType.ENABLED.getValue().equals(usedStatus)) {
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("状态无需变更");
                    }
                    List<StarTaskAgentAreaDO> list = starTaskAgentAreaDAO.findByAgentId(agentId);
                    // 启用，校验区域是否被代理
                    List<StarTaskAgentAreaDO> checkAgentAreaToUpdate = starTaskAgentAreaDAO.checkAgentAreaToUpdate(
                            list.stream().map(StarTaskAgentAreaDO::getCity).collect(Collectors.toList()), agentId);
                    if (CollectionUtil.isNotEmpty(checkAgentAreaToUpdate)) {
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("所选区域已被代理，无法启用");
                    }
                    break;
                case DISABLED:
                    if (WebOperationAgentType.DISABLED.getValue().equals(usedStatus)) {
                        throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("状态无需变更");
                    }
                    // 清除登录态
                    StpUtil.logout(param.getAgentUserId());
                    break;
                default:
                    throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("类型不正确");
            }
            // 事务
            transactionTemplate.execute(status -> {
                // 修改状态
                starTaskAgentDAO.updateUsedStatus(agentId, typeEnum.getValue());
                starTaskAgentAreaDAO.updateUsedStatus(agentId, typeEnum.getValue());
                return Boolean.TRUE;
            });
        } catch (Exception e) {
            LogUtil.warn(log, "StarTaskWebAgentServiceImpl" + " >>>>> " + "operate" + "  锁释放失败", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }

    private void addAgent(WebAgentAddParam param) {
        String phone = param.getPhoneNumber();
        String phoneNumber = FieldEncryptUtil.encode(phone);
        String encodeLikeFieldExt = FieldEncryptUtil.encodeLikeFieldExt(phone);
        // 判断手机号和区域
        StarTaskAgentUserDO checkPhone = starTaskAgentUserDAO.checkPhone(phoneNumber);
        if (Objects.nonNull(checkPhone)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("该手机号已被注册，请更换或联系客服处理");
        }
        List<String> cityList = param.getAreaList().stream().map(AgentAreaParam::getCity).collect(Collectors.toList());
        List<StarTaskAgentAreaDO> checkAgentArea = starTaskAgentAreaDAO.checkAgentArea(cityList);
        if (CollectionUtil.isNotEmpty(checkAgentArea)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("所选区域已被代理，请重新选择");
        }
        // 区代基本信息
        String agentId = StarTaskConstant.AGENT_PREFIX + IdWorkerUtil.getSingleId();
        StarTaskAgentDO agentDO = new StarTaskAgentDO();
        agentDO.setAgentId(agentId);
        agentDO.setName(param.getName());
        agentDO.setMaxAgentRate(param.getMaxAgentRate());
        agentDO.setRemark(param.getRemark());
        agentDO.setAgentRate(new BigDecimal(sysConfig.getStarTaskMinaPlatformRate()));
        agentDO.setMerchantRate(sysConfig.getStarTaskMinaMerchantDistributionRate());
        agentDO.setStarRate(sysConfig.getStarTaskMinaStarDistributionRate());
        // 区代用户信息
        String agentUserId = StarTaskConstant.AGENT_USER_PREFIX + IdWorkerUtil.getSingleId();
        StarTaskAgentUserDO agentUserDO = new StarTaskAgentUserDO();
        agentUserDO.setAgentId(agentId);
        agentUserDO.setAgentUserId(agentUserId);
        agentUserDO.setPhoneNumber(phoneNumber);
        agentUserDO.setPhoneNumberEncryptExt(encodeLikeFieldExt);
        agentUserDO.setIsAdmin(EmployeeAdminEnum.IS_ADMIN.getValue());
        agentUserDO.setContactName(param.getContactName());
        // 区代账户
        StarTaskAgentAccountDO starTaskAgentAccountDO = new StarTaskAgentAccountDO();
        starTaskAgentAccountDO.setAgentId(agentId);
        // 区代区域
        List<StarTaskAgentAreaDO> areaList = Lists.newArrayList();
        for (AgentAreaParam agentArea : param.getAreaList()) {
            StarTaskAgentAreaDO agentAreaDO = new StarTaskAgentAreaDO();
            agentAreaDO.setAgentId(agentId);
            agentAreaDO.setProvince(agentArea.getProvince());
            agentAreaDO.setCity(agentArea.getCity());
            areaList.add(agentAreaDO);
        }
        // 区代后台角色
        String roleId = IdWorkerUtil.getSingleId();
        AilikeRoleDO ailikeRoleDO = new AilikeRoleDO();
        ailikeRoleDO.setRoleName(OemCommonConstant.ADMINISTRATOR);
        ailikeRoleDO.setPartnerId(agentId);
        ailikeRoleDO.setRoleId(roleId);
        ailikeRoleDO.setBelong(GrantPlatformEnum.STAR_TASK_AGENT_BACKSTAGE.getValue());
        ailikeRoleDO.setPrimaryFlag(BooleanEnum.YES.getValue());
        ailikeRoleDO.setDataGrant(BooleanEnum.YES.getValue());
        ailikeRoleDO.setSystemFlag(BooleanEnum.YES.getValue());
        // 角色和身份关系
        AilikeRoleIdentityDO ailikeRoleIdentityDO = new AilikeRoleIdentityDO();
        ailikeRoleIdentityDO.setRoleId(roleId);
        ailikeRoleIdentityDO.setIdentityId(agentId);
        // 事务
        transactionTemplate.execute(status -> {
            starTaskAgentDAO.saveAgent(agentDO);
            starTaskAgentUserDAO.saveAgentUser(agentUserDO);
            starTaskAgentAreaDAO.batchAgentArea(areaList);
            starTaskAgentAccountDAO.saveAgentAccount(starTaskAgentAccountDO);
            ailikeRoleDAO.saveRole(ailikeRoleDO);
            ailikeRoleIdentityDAO.addIdentityRole(ailikeRoleIdentityDO);
            return Boolean.TRUE;
        });
    }


}