package com.huike.nova.service.listener;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.huike.nova.common.enums.CouponLockOperationTypeEnum;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.service.business.AlipayMinaService;
import com.huike.nova.service.domain.dto.zbt.AlipayCouponUnlockListenerDTO;
import com.huike.nova.service.domain.mapper.AlipayCouponUnlockListenerObjMapper;
import com.huike.nova.service.domain.param.mina.alipayMina.CouponLockParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023年11月21日 11:52
 */
@Slf4j(topic = "mq")
@Component
@AllArgsConstructor
public class AlipayCouponUnlockListener implements MessageListener {

    private AlipayMinaService alipayMinaService;

    private AlipayCouponUnlockListenerObjMapper alipayCouponUnlockListenerObjMapper;

    /**
     * 消费消息接口，由应用来实现<br>
     * 网络抖动等不稳定的情形可能会带来消息重复，对重复消息敏感的业务可对消息做幂等处理
     *
     * @param message 消息
     * @param context 消费上下文
     * @return 消费结果，如果应用抛出异常或者返回Null等价于返回Action.ReconsumeLater
     * @see <a href="https://help.aliyun.com/document_detail/44397.html">如何做到消费幂等</a>
     */
    @Override
    public Action consume(Message message, ConsumeContext context) {
        LogUtil.info(log, "AlipayCouponUnlockListener.consume >> 消费开始 >> message = {}", message);
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        AlipayCouponUnlockListenerDTO liveListenerDTO = JSON.parseObject(body, AlipayCouponUnlockListenerDTO.class);
        LogUtil.info(log, "AlipayCouponUnlockListener.consume >> liveListenerDTO内容 >> liveListenerDTO = {},body = {}", liveListenerDTO, body);
        if (null == liveListenerDTO) {
            LogUtil.info(log, "AlipayCouponUnlockListener.consume >> mq推送消息为空");
            return Action.CommitMessage;
        }
        CouponLockParam param = alipayCouponUnlockListenerObjMapper.toCouponLockParam(liveListenerDTO);
        param.setLockType(CouponLockOperationTypeEnum.UNLOCK.getValue());
        alipayMinaService.couponLock(param);
        return Action.CommitMessage;
    }
}
