/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.mina;

import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.service.domain.model.startask.mina.team.ExportCommonModel;
import com.huike.nova.service.domain.model.startask.mina.team.GetInvitationInfoModel;
import com.huike.nova.service.domain.model.startask.mina.team.PageRewardDetailModel;
import com.huike.nova.service.domain.model.startask.mina.team.StatisticsTeamInfoModel;
import com.huike.nova.service.domain.model.startask.mina.team.TeamRankModel;
import com.huike.nova.service.domain.param.startask.mina.team.GetInvitationInfoParam;
import com.huike.nova.service.domain.param.startask.mina.team.PageRewardDetailParam;
import com.huike.nova.service.domain.param.startask.mina.team.StatisticsTeamInfoParam;
import com.huike.nova.service.domain.param.startask.mina.team.TeamRankParam;

/**
 * <AUTHOR>
 * @version StarTaskTeamMinaService.java, v 0.1 2024-01-22 2:35 PM ruanzy
 */
public interface StarTaskTeamMinaService {

    /**
     * 团队信息总览
     *
     * @param param
     * @return
     */
    StatisticsTeamInfoModel statisticsTeamInfo(StatisticsTeamInfoParam param);

    /**
     * 成员列表
     *
     * @param param
     * @return
     */
    PageResult<TeamRankModel> teamRank(PageParam<TeamRankParam> param);

    /**
     * 奖励明细
     *
     * @param param
     * @return
     */
    PageResult<PageRewardDetailModel> pageRewardDetail(PageParam<PageRewardDetailParam> param);

    /**
     * 导出成员列表
     *
     * @param param
     * @return
     */
    ExportCommonModel exportTeamRank(TeamRankParam param);

    /**
     * 导出奖励明细
     *
     * @param param
     * @return
     */
    ExportCommonModel exportRewardDetail(PageRewardDetailParam param);

    /**
     * 邀请信息查询
     *
     * @param param
     * @return
     */
    GetInvitationInfoModel getInvitationInfo(GetInvitationInfoParam param);
}