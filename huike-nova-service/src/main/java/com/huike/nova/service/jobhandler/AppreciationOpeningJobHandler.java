/**
 * ailike.com
 * Copyright (C) 2021-2023 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.huike.nova.common.enums.AliOssEnum;
import com.huike.nova.common.enums.PayStatusEnum;
import com.huike.nova.common.util.DingTalkUtil;
import com.huike.nova.dao.config.SysConfig;
import com.huike.nova.dao.domain.result.OpeningRecordExcelDTO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.AppreciationOpeningRecordDAO;
import com.huike.nova.service.business.OssManager;
import com.huike.nova.service.domain.mapper.OpeningJobObjMapper;
import com.huike.nova.service.domain.model.appreciation.OpeningRecordExcel;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version AppreciationOpeningJobHandler.java, v 0.1 2023-02-16 6:19 下午 mayucong
 */
@Component
@Slf4j
@JobHandler("appreciationOpeningJobHandler")
@AllArgsConstructor
public class AppreciationOpeningJobHandler extends IJobHandler {

    private SysConfig sysConfig;
    private AppreciationOpeningRecordDAO appreciationOpeningRecordDAO;
    private OpeningJobObjMapper openingJobObjMapper;
    private OssManager ossManager;
    private final static String DING_DING_URL = "https://oapi.dingtalk.com/robot/send?access_token={}";

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("UserVideoDataRefreshJobHandler.execute >> 刷新用户抖音视频数据脚本执行开始：time = {}", DateUtil.now());
        List<OpeningRecordExcelDTO> openingRecordDOList = appreciationOpeningRecordDAO.findAllExcel();
        List<OpeningRecordExcel> excelList = new ArrayList<>();
        for (OpeningRecordExcelDTO openingRecordDO : openingRecordDOList) {
            OpeningRecordExcel openingRecordExcel = openingJobObjMapper.toOpeningRecordExcel(openingRecordDO);
            openingRecordExcel.setMerchantPhoneNumber(FieldEncryptUtil.decode(openingRecordDO.getMerchantPhoneNumber()));
            PayStatusEnum payStatusEnum = PayStatusEnum.getByValue(openingRecordDO.getPayStatus());
            openingRecordExcel.setPayStatusStr(ObjectUtil.isNull(payStatusEnum) ? "未知" : payStatusEnum.getName());
            excelList.add(openingRecordExcel);
        }
        String fileName = StrUtil.format("付费开通统计表-{}-new.xlsx", DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT));
        EasyExcel.write(fileName, OpeningRecordExcel.class).sheet().doWrite(excelList);

        File writeFile = new File(fileName);
        //上传oss
        String exportUrl = ossManager.uploadFileInner(writeFile, AliOssEnum.EXCEL_EXPORT);
        //删除本地文件
        File excelFile = new File(fileName);
        if (!excelFile.delete()) {
            log.error("delete" + fileName + "error");
        }

        // 发送钉钉

        String url = StrUtil.format(DING_DING_URL, sysConfig.getAppreciationOpenToken());
        String secret = sysConfig.getAppreciationOpenSecret();
        DingTalkUtil.sendAlarm(DingTalkUtil.getSignedUrl(url, secret), DingTalkUtil.buildMessage("付费开通统计表:\n" + exportUrl));
        XxlJobLogger.log("UserVideoDataRefreshJobHandler.execute >> 刷新用户抖音视频数据脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }

}
