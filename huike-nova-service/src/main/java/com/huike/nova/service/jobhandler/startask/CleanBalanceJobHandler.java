/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.jobhandler.startask;

import cn.hutool.core.date.DateUtil;
import com.huike.nova.service.business.startask.common.StarTaskJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 余额数据清洗
 *
 * <AUTHOR>
 * @version CleanBalanceJobHandler.java, v 0.1 2024-02-29 4:32 PM ruanzy
 */
@Component
@Slf4j
@JobHandler("cleanBalanceJobHandler")
@AllArgsConstructor
public class CleanBalanceJobHandler extends IJobHandler {

    private StarTaskJobService starTaskJobService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("CleanBalanceJobHandler.execute >> 余额数据清洗脚本执行开始：time = {}", DateUtil.now());
        starTaskJobService.cleanBalanceJobHandler();
        XxlJobLogger.log("CleanBalanceJobHandler.execute >> 余额数据清洗脚本执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}