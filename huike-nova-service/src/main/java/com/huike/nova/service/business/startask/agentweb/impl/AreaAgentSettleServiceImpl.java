/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.service.business.startask.agentweb.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.constant.startask.agentweb.StarTaskAgentWebRedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.startask.WithdrawTypeEnum;
import com.huike.nova.common.enums.startask.mina.WithdrawApplyStatusEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogChangeTypeEnum;
import com.huike.nova.common.enums.startask.web.BalanceLogRemarkTypeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.common.util.ExceptionUtil;
import com.huike.nova.common.util.FsDateUtils;
import com.huike.nova.common.util.IdWorkerUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.dao.domain.param.startask.AgentDayStatisticsDataParamDTO;
import com.huike.nova.dao.domain.param.startask.WithdrawPageListParamDTO;
import com.huike.nova.dao.domain.result.startask.PageSettleDetailResultDTO;
import com.huike.nova.dao.domain.result.startask.PageSettleListResultDTO;
import com.huike.nova.dao.domain.result.startask.WithdrawPageListResultDTO;
import com.huike.nova.dao.entity.AilikeGaodeCodeDO;
import com.huike.nova.dao.entity.StarTaskAgentAccountDO;
import com.huike.nova.dao.entity.StarTaskAgentDayStatisticsDO;
import com.huike.nova.dao.entity.StarTaskBalanceLogDO;
import com.huike.nova.dao.entity.StarTaskWithdrawApplyDO;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import com.huike.nova.dao.repository.AilikeGaodeCodeDAO;
import com.huike.nova.dao.repository.StarTaskAgentAccountDAO;
import com.huike.nova.dao.repository.StarTaskAgentDAO;
import com.huike.nova.dao.repository.StarTaskAgentDayStatisticsDAO;
import com.huike.nova.dao.repository.StarTaskBalanceLogDAO;
import com.huike.nova.dao.repository.StarTaskSettleDetailDAO;
import com.huike.nova.dao.repository.StarTaskWithdrawApplyDAO;
import com.huike.nova.service.business.startask.agentweb.AreaAgentSettleService;
import com.huike.nova.service.common.LoginUtil;
import com.huike.nova.service.domain.mapper.startask.agentweb.AreaAgentSettleServiceObjMapper;
import com.huike.nova.service.domain.model.startask.agentweb.login.StarTaskAgentWebLoginModel;
import com.huike.nova.service.domain.model.startask.agentweb.settle.GetAgentAccountModel;
import com.huike.nova.service.domain.model.startask.agentweb.settle.GetSettleDetailModel;
import com.huike.nova.service.domain.model.startask.agentweb.settle.PageSettleDetailModel;
import com.huike.nova.service.domain.model.startask.agentweb.settle.PageSettleListModel;
import com.huike.nova.service.domain.model.startask.agentweb.settle.PageWithdrawalListModel;
import com.huike.nova.service.domain.param.startask.agentweb.settle.ApplyAgentWithdrawalParam;
import com.huike.nova.service.domain.param.startask.agentweb.settle.GetSettleDetailParam;
import com.huike.nova.service.domain.param.startask.agentweb.settle.PageSettleListParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version AreaAgentSettleServiceImpl.java, v 0.1 2024-05-23 10:31 AM ruanzy
 */
@Slf4j
@Service
@AllArgsConstructor
public class AreaAgentSettleServiceImpl implements AreaAgentSettleService {

    /**
     * 字符串值-500
     */
    private final static String FIVE_HUNDRED = "500";

    private StarTaskAgentAccountDAO starTaskAgentAccountDAO;

    private StarTaskAgentDAO starTaskAgentDAO;

    private StarTaskWithdrawApplyDAO starTaskWithdrawApplyDAO;

    private StarTaskBalanceLogDAO starTaskBalanceLogDAO;

    private StarTaskAgentDayStatisticsDAO starTaskAgentDayStatisticsDAO;

    private AreaAgentSettleServiceObjMapper areaAgentSettleServiceObjMapper;

    private AilikeGaodeCodeDAO ailikeGaodeCodeDAO;

    private RedissonClient redissonClient;

    private TransactionTemplate transactionTemplate;

    private StarTaskSettleDetailDAO starTaskSettleDetailDAO;

    /**
     * 账户信息
     *
     * @return
     */
    @Override
    public GetAgentAccountModel getAgentAccount() {
        // 获取登录态信息
        StarTaskAgentWebLoginModel loginModel = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        // 查询账户信息
        StarTaskAgentAccountDO agentAccount = starTaskAgentAccountDAO.getAgentAccount(loginModel.getAgentId());
        if (Objects.isNull(agentAccount)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账户信息不存在");
        }
        // 构建返回参数
        GetAgentAccountModel model = new GetAgentAccountModel();
        model.setAvailableBalance(agentAccount.getAvailableBalance());
        model.setAccumulatedCommission(agentAccount.getAccumulatedCommission());
        return model;
    }

    /**
     * 代理商申请提现
     *
     * @param param
     */
    @Override
    public void applyAgentWithdrawal(ApplyAgentWithdrawalParam param) {
        // 获取登录态信息
        StarTaskAgentWebLoginModel loginModel = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        String agentId = loginModel.getAgentId();
        // 加锁-代理商账户
        String lock = StrUtil.format(StarTaskAgentWebRedisPrefixConstant.STAR_TASK_AGENT_ACCOUNT_CACHE_KEY, agentId);
        try (val redisLockHelper = new RedisLockHelper(lock, redissonClient)) {
            redisLockHelper.tryLock();
            BigDecimal withdrawAmount = param.getWithdrawAmount();
            String alipayAccount = param.getAlipayAccount();
            String realName = param.getRealName();
            String idCardNo = FieldEncryptUtil.encode(param.getIdCardNo());
            // 校验-提现余额是否足够、单次提现是否大于500
            StarTaskAgentAccountDO agentAccount = starTaskAgentAccountDAO.getAgentAccount(agentId);
            if (Objects.isNull(agentAccount)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("账户信息不存在");
            }
            if (agentAccount.getAvailableBalance().compareTo(withdrawAmount) < 0) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("提现余额不足");
            }
            if (withdrawAmount.compareTo(new BigDecimal(FIVE_HUNDRED)) < 0) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("单次提现不能小于500");
            }
            // 新增提现申请单
            String applyCode = IdWorkerUtil.getSingleId();
            StarTaskWithdrawApplyDO withdrawApplyDO = new StarTaskWithdrawApplyDO();
            withdrawApplyDO.setApplyCode(applyCode);
            withdrawApplyDO.setWithdrawAmount(withdrawAmount);
            withdrawApplyDO.setAlipayAccount(alipayAccount);
            withdrawApplyDO.setRealName(realName);
            withdrawApplyDO.setApplyStatus(WithdrawApplyStatusEnum.WAITING_FOR_PAYMENT.getValue());
            withdrawApplyDO.setWithdrawType(WithdrawTypeEnum.AGENT_COMMISSION.getValue());
            withdrawApplyDO.setIdCardNo(idCardNo);
            withdrawApplyDO.setActualAmount(withdrawAmount.multiply(new BigDecimal("0.92")).setScale(2, BigDecimal.ROUND_HALF_UP));
            withdrawApplyDO.setAgentId(agentId);
            // 新增日志
            StarTaskBalanceLogDO balanceLogDO = new StarTaskBalanceLogDO();
            balanceLogDO.setChangeType(BalanceLogChangeTypeEnum.REDUCED.getValue());
            balanceLogDO.setChangeAmount(withdrawAmount);
            balanceLogDO.setRelationNumber(applyCode);
            balanceLogDO.setRemarkType(BalanceLogRemarkTypeEnum.AGENT_COMMISSION.getValue());
            balanceLogDO.setChangeRemark(BalanceLogRemarkTypeEnum.AGENT_COMMISSION.getName());
            balanceLogDO.setAgentId(agentId);
            // 数据库操作
            transactionTemplate.execute(status -> {
                // 更新收款账号信息
                starTaskAgentDAO.updateWithdrawalInfo(agentId, realName, alipayAccount, idCardNo);
                // 更新账号信息
                starTaskAgentAccountDAO.updateWithdrawAmount(agentId, withdrawAmount, withdrawAmount);
                // 新增提现记录
                starTaskWithdrawApplyDAO.saveWithdrawApply(withdrawApplyDO);
                // 新增日志
                starTaskBalanceLogDAO.saveBalanceLog(balanceLogDO);
                return true;
            });
        } catch (Exception e) {
            LogUtil.warn(log, "AreaAgentSettleServiceImpl" + " >>>>> " + "applyAgentWithdrawal" + "  锁释放失败", e);
            throw ExceptionUtil.toCommonException(e);
        }
    }

    /**
     * 分页查询结算列表
     *
     * @param param
     * @return
     */
    @Override
    public PageResult<PageSettleListModel> pageSettleList(PageParam<PageSettleListParam> param) {
        PageSettleListParam query = param.getQuery();
        // 获取登录态信息
        StarTaskAgentWebLoginModel loginModel = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        // 构建入参
        PageParam<AgentDayStatisticsDataParamDTO> pageParam = new PageParam<>();
        pageParam.setPage(param.getPage());
        pageParam.setPageSize(param.getPageSize());
        AgentDayStatisticsDataParamDTO dto = new AgentDayStatisticsDataParamDTO();
        dto.setAgentId(loginModel.getAgentId());
        dto.setCityList(query.getSettleAreaList());
        dto.setStartTime(FsDateUtils.dateToInteger(query.getStartTime()));
        dto.setEndTime(FsDateUtils.dateToInteger(query.getEndTime()));
        pageParam.setQuery(dto);
        Page<PageSettleListResultDTO> resultDTOPage = starTaskAgentDayStatisticsDAO.pageSettleList(pageParam);
        return areaAgentSettleServiceObjMapper.toPageSettleListModelPage(resultDTOPage);
    }

    /**
     * 结算详情
     *
     * @param param
     * @return
     */
    @Override
    public GetSettleDetailModel getSettleDetail(GetSettleDetailParam param) {
        // 获取结算详情
        StarTaskAgentDayStatisticsDO settleDetail = starTaskAgentDayStatisticsDAO.getSettleDetail(param.getStatisticsId());
        if (Objects.isNull(settleDetail)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("结算详情不存在");
        }
        String city = settleDetail.getCity();
        // 构建返回信息
        GetSettleDetailModel model = new GetSettleDetailModel();
        model.setStatisticsId(settleDetail.getStatisticsId());
        model.setCreateTime(DateUtil.format(settleDetail.getCreateTime(), FsDateUtils.SIMPLE_DATE_FORMAT));
        model.setUserReward(settleDetail.getMerchantReward().add(settleDetail.getStarReward()));
        model.setCity(city);
        model.setTasksCompleted(settleDetail.getTasksCompleted());
        model.setTaskTransactionAmount(settleDetail.getTaskTransactionAmount());
        // 查询市信息
        AilikeGaodeCodeDO codeInfo = ailikeGaodeCodeDAO.getCodeInfo(city);
        model.setCityName(Objects.isNull(codeInfo) ? StringPool.EMPTY : codeInfo.getAdname());
        model.setCommissionSettle(settleDetail.getCommissionSettle());
        return model;
    }

    /**
     * 分页查询提现列表
     *
     * @param Param
     * @return
     */
    @Override
    public PageResult<PageWithdrawalListModel> pageWithdrawalList(PageParam Param) {
        // 获取登录态信息
        StarTaskAgentWebLoginModel loginModel = LoginUtil.getAgentWebStarTaskLoginBasicInfo();
        PageParam<WithdrawPageListParamDTO> pageParam = new PageParam<>();
        pageParam.setPage(Param.getPage());
        pageParam.setPageSize(Param.getPageSize());
        WithdrawPageListParamDTO dto = new WithdrawPageListParamDTO();
        dto.setAgentId(loginModel.getAgentId());
        pageParam.setQuery(dto);
        // 分页查询提现列表
        Page<WithdrawPageListResultDTO> resultDTOPage = starTaskWithdrawApplyDAO.pageAgentList(pageParam);
        return areaAgentSettleServiceObjMapper.toPageWithdrawalListModelPage(resultDTOPage);
    }

    /**
     * 分页查询结算详情明细
     *
     * @param param
     * @return
     */
    @Override
    public PageResult<PageSettleDetailModel> pageSettleDetail(PageParam<GetSettleDetailParam> param) {
        Page<PageSettleDetailResultDTO> resultDTOPage = starTaskSettleDetailDAO.pageSettleDetail(areaAgentSettleServiceObjMapper.toGetSettleDetailParamDTOPage(param));
        return areaAgentSettleServiceObjMapper.toPageSettleDetailModelPage(resultDTOPage);
    }


}